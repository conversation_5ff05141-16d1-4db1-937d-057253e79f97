项目实施策略将紧密围绕项目建设的发展需求，统一规划，分步、分阶段进行项目建设，具体原则包括：
1、质量第一，重点突出
项目质量是衡量项目成败的决定性要素。在项目实施过程中我们遵循质量第一、重点突出的原则。质量管理可分成如下三步：首先根据项目的总体目标、项目范围以及项目进度计划，双方确定有效的项目质量标准。其次，在项目执行过程中采取有效措施监测项目的实际运行，为了达到有效监测的目的，通常采取的监测和沟通渠道包括项目进度报告、项目例会、里程碑会议、各种会议纪要等。再次，把项目实施过程中的实际表现与项目质量衡量标准进行比较，分析出差异。最后，根据具体情况采取合理的纠正措施。经过比较与分析，如果发现偏差，就要采取适当的措施进行纠正，让项目实施回到正轨。
2、设计先进性和适用性
在设计过程中，将充分借鉴国内外设计经验，利用成熟的产品和技术；同时充分考虑南网云平台、数据中心和业务发展的实际情况。并立足今后发展需要，使之成为既适合南方电网需求，又能照顾到分子公司情况，既能够保证先进性和成熟性，也可又保证系统的适用性。
3、有效沟通，多点落实
采用有效沟通、多点落实的沟通原则：首先识别项目的各个干系人，关注各个干系人的期望与需求；其次在项目实施过程中采用项目总结、项目例会、项目周报、临时会议等方式充分和项目相关人员进行沟通；最后沟通的结果会形成书面记录以便有效执行。全过程、多层次地沟通确保了双方目标一致、信息同步，从而有利于项目中各种问题的解决。
4、全程参与，知识转移
项目实施采用关键用户全程参与的方式来达到知识转移的目的。在项目实施过程中按照转移方式不同将知识内容分为显性知识和隐性知识，其中，显性知识包括系统客户化方法、系统操作方法、系统数据库结构及二次开发规范；隐性知识则是指难以用文字记录和传播的知识，是与人结合在一起的经验性的知识，难于通过常规方法收集，也难于通过常规的信息工具传播，主要包括技术要素（主要指技术诀窍、技能和能力）、认知要素（主要指选择和分析问题、判断力、前瞻性）、经验要素（指经验、阅历和文化要素）和价值要素（包括文化和行为准则规范等）。显性知识转移通过培训、项目实施文档进行转移，隐性知识需要关键用户参与需求分析、方案编写、客户化设置等系统实施的全过程。
5、系统标准化和扩展性
在标准化方面，数据上遵循各种国家标准及南方电网企业标准；应用系统上应该符合企业本身制定的一些安全标准和管理标准；建设过程中也应该遵循企业的项目管理和各种开发管理的标准。