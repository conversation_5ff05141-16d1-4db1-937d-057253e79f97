﻿序号（标准）,序号,任务域,任务项,工作内容,工作量（人天）,总部,超高压,广东,广西,云南,贵州,海南,深圳,工作量说明,备注
项目管理工作,项目管理工作,,,,128.5,93.5,5.0,5.0,5.0,5.0,5.0,5.0,5.0,,
P01,1,前期准备,,,16.0,16.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,,统一开发、分级部署实施的项目中，各分级实施单位的相关工作调节系数按0.5计取
P0101,1.1,,制定工作方案,确定工作目标、内容、计划和组织保障等,8.0,8.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,总部统筹建设项目，仅公司总部列支2人4天。,
P0102,1.2,,召开启动会,策划、组织落实会议材料和召开会议,8.0,8.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,总部统筹建设项目，仅公司总部列支2人4天。,
P02,2,项目实施,,,97.5,69.5,4.0,4.0,4.0,4.0,4.0,4.0,4.0,,
P0201,2.1,,制定总体实施方案,在工作方案基础上，细化项目具体实施工作、实施计划、工作职责与分工等,8.0,8.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,总部统筹建设项目，仅公司总部列支2人4天。,
P0202,2.2,,召开项目例会,项目组内部例会（周）、领导小组例会（双周或月度）等的组织及会后事务处理工作,28.5,21.5,1.0,1.0,1.0,1.0,1.0,1.0,1.0,按照周例会或月度领导例会、每次1人*0.5天=0.5人天，总部或试点单位列支，合同到终验历时10个月，共计21.5人天；其他单位仅考虑中期和终期各1次会议，每次0.5人天，共计1人天。,周期按合同签订后至终验，本处样例按周期一年，每周半天
P0203,2.3,,阶段质量检查与汇报,进行项目阶段质量检查及组织汇报工作,17.0,10.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,关键节点为需求分析阶段、初步设计阶段、详细设计阶段、开发阶段、实施阶段等5个关键阶段质量检查与汇报，总部或试点单位列支5个阶段*2人天，共计10人天；其他单位仅考虑中期和终期各1次会议，每次0.5人天，共计1人天。,
P0204,2.4,,交付件自验证（或测试）组织,组织交付前自查,13.5,10.0,0.5,0.5,0.5,0.5,0.5,0.5,0.5,关键节点为需求分析阶段、初步设计阶段、详细设计阶段、开发阶段、实施阶段等5个关键阶段交付件自验证（或测试）组织，总部或试点单位列支5个阶段*2人天，共计10人天；其他单位仅终期开展并列支0.5人天。,
P0205,2.5,,交付件质量审查（评审）,组织交付文件的评审过程,13.5,10.0,0.5,0.5,0.5,0.5,0.5,0.5,0.5,关键节点为需求分析阶段、初步设计阶段、详细设计阶段、开发阶段、实施阶段等5个关键阶段交付件质量审查（评审），总部或试点单位列支5个阶段*2人天，共计10人天；其他单位仅终期开展并列支0.5人天。,
P0206,2.6,,日常管理,项目日常管理，日常事务处理,17.0,10.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,平均每月1人天，总部或试点单位列支，合同到终验历时10个月，共计10人天；其他单位仅考虑中期和终期各1次会议，每次0.5人天，共计1人天。,周期按合同签订后至终验，本处样例按周期一年，每周一天
P03,3,项目验收,,,15.0,8.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,,
P0301,3.1,,项目初验,组织项目初验过程管理，包括验收过程策划、资料安排与审查、验收过程管理等,7.5,4.0,0.5,0.5,0.5,0.5,0.5,0.5,0.5,总部或试点单位列支4人天，其他单位0.5人天。,
P0302,3.2,,项目终验,组织项目终验过程管理，包括验收过程策划、资料安排与审查、验收过程管理等,7.5,4.0,0.5,0.5,0.5,0.5,0.5,0.5,0.5,总部或试点单位列支4人天，其他单位0.5人天。,
项目实施工作,项目实施工作,,,,432.0,50.0,46.0,61.0,61.0,61.0,61.0,46.0,46.0,,
T2-0601,1,项目启动,,,34.0,5.0,3.0,5.0,5.0,5.0,5.0,3.0,3.0,,统一开发、分级部署实施的项目中，各分级实施单位的相关工作调节系数按0.5计取
T2-060101,1.1,,编制及评审实施方案,,26.0,4.0,2.0,4.0,4.0,4.0,4.0,2.0,2.0,总部、广东、广西、云南和贵州为4人天，超高压、海南、深圳2人天。,公司总部项目：2-5 天其他单位项目：1-2 天
T2-060102,1.2,,召开项目启动会,,8.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,所有单位均1人天。,
T2-0602,2,系统环境准备,,,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,,
T2-060201,2.1,,服务器环境准备,,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,共投运X台设备，总部侧投运X台、MM单位投运X台、….，单位和人天安排详见后面说明并在此清晰描述。,若没有新增资源或资源变更情况就不列支，若有新增资源或资源变更情况但没有明确是哪个单位资源就由总部统一列支、若明确了各个单位所属就直接由相关单位列支，根据服务器、专属设备等规模确定工作量，500台以上、500台至200台、200台至50台、50台至20台、20台及以下，分别为40人天、20人天、10人天、5人天、2人天。
T2-0603,3,系统初始化,,,26.0,4.0,2.0,4.0,4.0,4.0,4.0,2.0,2.0,,
T2-060301,3.1,,数据准备工作-静态数据准备,,13.0,2.0,1.0,2.0,2.0,2.0,2.0,1.0,1.0,,
T2-06030101,3.1.1,,,静态数据环境分析,11.5,0.5,1.0,2.0,2.0,2.0,2.0,1.0,1.0,基于各单位本项目涉及用户数量，若用户规模5万以上2人天，用户规模5万到2千以上1人天，2千以下0.5人天。,基于项目涉及的用户数，用户规模5万以上2人天，用户规模5万到5千以上1人天，5千以下0.5人天。
T2-06030102,3.1.2,,,编制静态数据准备方案,0.5,0.5,0.0,0.0,0.0,0.0,0.0,0.0,0.0,总部或试点单位列支0.5人天。,
T2-06030103,3.1.3,,,编制静态数据准备工作作业指导书,0.5,0.5,0.0,0.0,0.0,0.0,0.0,0.0,0.0,总部或试点单位列支0.5人天。,
T2-06030104,3.1.4,,,静态数据准备工作前培训,0.5,0.5,0.0,0.0,0.0,0.0,0.0,0.0,0.0,总部或试点单位列支0.5人天。,
T2-060302,3.2,,数据准备工作-动态数据准备,,13.0,2.0,1.0,2.0,2.0,2.0,2.0,1.0,1.0,,升级改造项目默认不列支，若新增功能有数据再适当考虑工作量；完全新建项目适当考量工作量。
T2-06030201,3.2.1,,,动态数据环境分析,11.5,0.5,1.0,2.0,2.0,2.0,2.0,1.0,1.0,基于各单位本项目涉及用户数量，若用户规模5万以上2人天，用户规模5万到2千以上1人天，2千以下0.5人天。,
T2-06030202,3.2.2,,,编制动态数据准备方案,0.5,0.5,0.0,0.0,0.0,0.0,0.0,0.0,0.0,总部或试点单位列支0.5人天。,
T2-06030203,3.2.3,,,编制动态数据准备工作作业指导书,0.5,0.5,0.0,0.0,0.0,0.0,0.0,0.0,0.0,总部或试点单位列支0.5人天。,
T2-06030204,3.2.4,,,动态数据准备工作前培训,0.5,0.5,0.0,0.0,0.0,0.0,0.0,0.0,0.0,总部或试点单位列支0.5人天。,
T2-060303,3.3,,开发数据清理工具,,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,,若没有动态数据，本项工作不用开展。若有动态数据，总部或试点单位列支XX人天。
T2-060304,3.4,,编制数据清理方案,,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,,若没有动态数据，本项工作不用开展。若有动态数据，总部或试点单位列支XX人天。
T2-060305,3.5,,数据清理工作宣贯与培训,,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,,若没有动态数据，本项工作不用开展。若有动态数据，总部或试点单位列支XX人天。
T2-060306-1,3.6,,数据清理,,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,,若没有动态数据，本项工作不用开展。若有动态数据，各单位根据数据规模进行列支。
T2-060306-2,3.7,,数据导入,,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,,若没有动态数据，本项工作不用开展。若有动态数据，<1万条为0.5人天，1万条≤数据量<1千万条为1人天，1千万条≤数据量<1亿万条为1.5人天，1亿条≤为2人天。
T2-060307,3.8,,数据校核,,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,,若没有动态数据，本项工作不用开展。若有动态数据，各单位根据数据规模进行列支。默认广东、广西、云南、贵州为1人天，其他单位0.5人天。
T2-0604,4,系统集成调试,,,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,,
T2-060401,4.1,,软件集成调试,,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,详细描述接口情况信息。,微服务交互调试按照每个逻辑文件0.5人天，非微服务继承调试按照每个逻辑文件0.25人天列支。若是网级系统由总部或试点单位，若网省两级部署，各单位根据实际情况列支。
T2-060402,4.2,,硬件集成调试,,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,共投运X台设备，总部侧投运X台、MM单位投运X台、….，单位和人天安排详见后面说明并在此清晰描述。,若没有新增资源或资源变更情况就不列支，若有新增资源或资源变更情况但没有明确是哪个单位资源就由总部统一列支、若明确了各个单位所属就直接由相关单位列支，根据服务器、专属设备等规模确定工作量，500台以上、500台至200台、200台至50台、50台至20台、20台及以下，分别为40人天、20人天、10人天、5人天、2人天。
T2-0605,5,最终用户培训,,,27.5,10.0,2.5,2.5,2.5,2.5,2.5,2.5,2.5,,
T2-060501,5.1,,编写培训教材,可以在实施的软件产品厂商或者开发厂商提供的培训课件基础上，针对实施单位的具体情况进行调整和优化；或者新编写培训教材。,9.5,6.0,0.5,0.5,0.5,0.5,0.5,0.5,0.5,"总部或试点单位2人3天,其他单位0.5人天。",统一开发、分级部署实施的项目中，各分级实施单位的相关工作调节系数按0.5计取
T2-060502,5.2,,准备培训环境,培训场地的落实和必要设备部署等。,9.0,2.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,"总部或试点单位2人1天,其他单位2人0.5天。",培训场地的落实和必要设备部署等
T2-060503,5.3,,培训开展对甲方用户或内训师培训,按计划组织相关培训，并对培训成效进行及时评估，对于多批次培训，应依据评估不断优化培训过程，提升培训质量。,9.0,2.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,"总部或试点单位2人1天,其他单位2人0.5天。",
T2-0606,6,系统上线切换,,,47.5,9.0,5.5,5.5,5.5,5.5,5.5,5.5,5.5,,公司总部项目：不超过 15 天，2 人； 其他单位项目：不超过 10 天，2 人。
T2-060601,6.1,,上线切换准备,,31.5,7.0,3.5,3.5,3.5,3.5,3.5,3.5,3.5,,
T2-06060101,6.1.1,,,系统切换测试,9.0,2.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,总部或试点单位2人天，其他单位1人天。,
T2-06060102,6.1.2,,,编制上线方案,9.0,2.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,总部或试点单位2人天，其他单位1人天。,
T2-06060103,6.1.3,,,系统上线演练,9.0,2.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,总部或试点单位2人天，其他单位1人天。,
T2-06060104,6.1.4,,,召开上线启动会,4.5,1.0,0.5,0.5,0.5,0.5,0.5,0.5,0.5,总部或试点单位1人天，其他单位0.5人天。,
T2-060602,6.2,,系统上线切换,,16.0,2.0,2.0,2.0,2.0,2.0,2.0,2.0,2.0,,
T2-06060201,6.2.1,,,正式环境切换,16.0,2.0,2.0,2.0,2.0,2.0,2.0,2.0,2.0,各单位2人天。,
T2-06060202,6.2.2,,,差量（动态）数据补录（选填内容）,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,,不考虑。
T2-0607,7,运行持续支持（试运行）,,,297.0,22.0,33.0,44.0,44.0,44.0,44.0,33.0,33.0,,
T2-060701,7.1,,小版本发布,,297.0,22.0,33.0,44.0,44.0,44.0,44.0,33.0,33.0,1个月试运行，总部1人、超高压、海南、深圳1.5人，广东、广西、云南、贵州2人。,
T2-06070101,7.1.1,,,一线问题解决：解答与处理一线用户问题（操作咨询、权限配置等现场可以直接处理的问题）要包含现场和后台人员。,,,,,,,,,,,
T2-06070102,7.1.2,,,二线问题管理跟踪：管理及跟踪反馈二线 用户问题（系统缺陷、功能改进、功能需求等现场不能直接处理的功能性问题）,,,,,,,,,,,
T2-060702,7.2,,运行维护,,,,,,,,,,,,
T2-06070201,7.2.1,,,日常运行监控：实施期间进行应用服务器及数据服务器的日常运行监控,,,,,,,,,,,
T2-06070202,7.2.2,,,日常维护调优：实施期间进行服务器平台调优与日常维护,,,,,,,,,,,
T2-060703,7.3,,使用答疑,,,,,,,,,,,,
