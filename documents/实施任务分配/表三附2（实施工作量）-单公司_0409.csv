﻿序号（标准）,序号,任务域,任务项,工作内容,工作量（人天）,工作量说明,备注
项目管理工作,项目管理工作,,,,33.5,,
P01,1,前期准备,,,4,,统一开发、分级部署实施的项目中，各分级实施单位的相关工作调节系数按0.5计取
P0101,1.1,,制定工作方案,方案的编制、评审和定稿，确定工作目标、内容、计划和组织保障等。,2,2人天。,
P0102,1.2,,召开启动会,策划、组织落实会议材料（材料的编写、评审、定稿）和召开会议（会议现场准备）。,2,2人天。,
P02,2,项目实施,,,25.5,,
P0201,2.1,,制定总体实施方案,在工作方案基础上，细化项目具体实施工作、实施计划、工作职责与分工等。,4,2人2天。,
P0202,2.2,,召开项目例会,项目组内部例会（周）、领导小组例会（双周或月度）等的组织及会后事务处理工作。,4.5,按照双周例会或月度领导例会、每次1人*0.5天=0.5人天，合同到终验历时4个月，共计4.5人天。,周期按合同签订后至终验，本处样例按周期一年，每周半天
P0203,2.3,,阶段质量检查与汇报,进行项目阶段质量检查（如：代码走查，技术和业务验证、质量问题）形成检查总结报告，并落实举措；编制项目阶段性总结报告及组织汇报工作。,5,关键节点为需求分析阶段、初步设计阶段、详细设计阶段、开发阶段、实施阶段等5个关键阶段质量检查与汇报，列支5个阶段*1人天，共计5人天。,
P0204,2.4,,交付件自验证（或测试）组织,项目过程各阶段的交付文件清单自查工作。,5,关键节点为需求分析阶段、初步设计阶段、详细设计阶段、开发阶段、实施阶段等5个关键阶段质量检查与汇报，总部或试点单位列支5个阶段*1人天，共计5人天；其他单位仅终期开展并列支0.5人天。,
P0205,2.5,,交付件质量审查（评审）,组织对项目过程交付物的内容和质量进行评审，以满足最终交付要求。,5,关键节点为需求分析阶段、初步设计阶段、详细设计阶段、开发阶段、实施阶段等5个关键阶段质量检查与汇报，列支5个阶段*1人天，共计5人天。,
P0206,2.6,,日常管理,项目日常管理，日常事务处理（日报、周报、月报、问题分析）。,2,平均每月0.5人天，合同到终验历时4个月，共计2人天。,周期按合同签订后至终验，本处样例按周期一年，每周一天
P0207,2.7,,专项工作,领导小组、里程碑专项汇报，督办工作、业务痛点解决方案、业务协同等专项工作。,0,根据实际工作内容评估进行列支。,选填内容，若有就在可研报告里面需要有专项工作的安排，否则默认为0.
P03,3,项目验收,,,4,,
P0301,3.1,,项目初验,组织项目初验过程管理，包括验收过程策划、资料安排与审查、验收过程管理等。,2,2人天。,
P0302,3.2,,项目终验,组织项目终验过程管理，包括验收过程策划、资料安排与审查、验收过程管理等。,2,2人天。,
项目实施工作,项目实施工作,,,,34,,
T1-0101,1,技术管控,,,14,,
T1-010101,1.1,,可研阶段,,0,考虑到项目可研后无法再给到可研编制单位，不列支。,
T1-010102,1.2,,需规概设阶段,,3,3人天。,需规概设阶段：3-8天
T1-010103,1.3,,详细设计阶段,,3,3人天。,详细设计阶段：3-8天
T1-010104,1.4,,开发测试阶段,,3,3人天。,开发测试阶段：3-8天
T1-010105,1.5,,试运行（初验）,,3,3人天。,试运行（初验）阶段：3-8天
T1-010106,1.6,,终验,,2,2人天。,终验阶段：2-6天
T2-0601,2,项目启动,,,1.5,,统一开发、分级部署实施的项目中，各分级实施单位的相关工作调节系数按0.5计取
T2-060101,2.1,,编制及评审实施方案,按照工作方案的目标及要求，组织编制及评审整体实施方案。,1,1人天。,公司总部项目：2-5 天其他单位项目：1-2 天
T2-060102,2.2,,召开项目启动会,召开项目启动会，明确项目实施负责人、干系人、目标和计划。,0.5,0.5人天。,
T2-0602,3,系统环境准备,,,0,,"工作量参考取值：公司总部项目：5-10 天,其他单位项目：5-8 天"
T2-060201,3.1,,服务器环境准备,环境搭建准备：资源申请、策略开通、数据库部署、应用服务部署等。,0,共投运X台设备，总部侧投运X台、MM单位投运X台、….，单位和人天安排详见后面说明并在此清晰描述。,若没有新增资源或资源变更情况就不列支，若有新增资源或资源变更情况但没有明确是哪个单位资源就由总部统一列支、若明确了各个单位所属就直接由相关单位列支，根据服务器、专属设备等规模确定工作量，500台以上、500台至200台、200台至50台、50台至20台、20台及以下，分别为40人天、20人天、10人天、5人天、2人天。
T2-0603,4,系统初始化,,,4,,
T2-060301,4.1,,数据准备工作-静态数据准备,,2,,
T2-06030101,4.1.1,,,静态数据环境分析-分析静态数据环境（库表完整性、数据同步工具、相关策略开通、权限情况等）。,0.5,基于各单位本项目涉及用户数量，若用户规模5万以上2人天，用户规模5万到2千以上1人天，2千以下0.5人天。,"基于项目涉及的用户数，用户规模5万以上2人天，用户规模5万到5千以上1人天，5千以下0.5人天。
公司总部项目：2-3天
其他单位项目：1-2天"
T2-06030102,4.1.2,,,编制静态数据准备方案-编制静态数据准备方案（落实各项准备工作任务和交付物，如：角色、岗位、流程、台账基础和业务配置收集模板）。,0.5,0.5人天。,
T2-06030103,4.1.3,,,编制静态数据准备工作作业指导书-系统权限（角色、岗位、流程）配置、业务基础数据配置工作操作指引等。,0.5,0.5人天。,
T2-06030104,4.1.4,,,静态数据准备工作前培训-组织开展基础数据维护、人员、角色、岗位、流程配置工作宣贯和配置培训。,0.5,0.5人天。,
T2-060302,4.2,,数据准备工作-动态数据准备,,2,,升级改造项目默认不列支，若新增功能有数据再适当考虑工作量；完全新建项目适当考量工作量。
T2-06030201,4.2.1,,,动态数据环境分析-分析动态数据环境准备工作（软件安装、环境配置参数、数据库空间、中间件、策略开通等）。,0.5,基于各单位本项目涉及用户数量，若用户规模5万以上2人天，用户规模5万到2千以上1人天，2千以下0.5人天。,"公司总部项目：2-3天
其他单位项目：1-2天"
T2-06030202,4.2.2,,,编制动态数据准备方案-编制数据清理方案、数据迁移方案和数据验证方案等。,0.5,0.5人天。,
T2-06030203,4.2.3,,,编制动态数据准备工作作业指导书-编制数据治理工作指引、迁移工作指引和数据验证工作指引等。,0.5,0.5人天。,
T2-06030204,4.2.4,,,动态数据准备工作前培训-组织开展数据治理、迁移、验证工作方案宣贯和迁移培训。,0.5,0.5人天。,
T2-060303,4.3,,开发数据清理工具,编制数据清理、迁移脚本；编制脚本操作指引等。,0,,若没有动态数据，本项工作不用开展。若有动态数据，总部或试点单位列支XX人天。
T2-060304,4.4,,编制数据清理方案,编写数据迁移工作方案、数据清理工作方案，明确数据清理技术路线、数据清理范围、工作职责及分工。,0,,若没有动态数据，本项工作不用开展。若有动态数据，总部或试点单位列支XX人天。
T2-060305,4.5,,数据清理工作宣贯与培训,组织开展数据清理工作的宣贯和培训，加强对迁移脚本工具及脚本操作指引。,0,,若没有动态数据，本项工作不用开展。若有动态数据，总部或试点单位列支XX人天。
T2-060306,4.6,,数据清理和导入,开展数据清理（数据加工、数据转换、数据导出）和数据导入工作。,0,,若没有动态数据，本项工作不用开展。若有动态数据，各单位根据数据规模进行列支。
T2-060307,4.7,,数据校核,数据导入后，组织开展数据完整性、一致性、准确性校核工作。,0,,若没有动态数据，本项工作不用开展。若有动态数据，各单位根据数据规模进行列支。默认广东、广西、云南、贵州为1人天，其他单位0.5人天。
T2-0604,5,系统集成调试,,,0,,
T2-060401,5.1,,软件集成调试,包含应用侧与中台或其他系统间进行集成所涉及的适配、调试工作，如：测试用例、环境准备、数据准备、接口调试、测试报告、问题处理。,0,详细描述接口情况信息。,微服务交互调试按照每个逻辑文件0.5人天，非微服务继承调试按照每个逻辑文件0.25人天列支。若是网级系统由总部或试点单位，若网省两级部署，各单位根据实际情况列支。
T2-060402,5.2,,硬件集成调试,,0,共投运X台设备，总部侧投运X台、MM单位投运X台、….，单位和人天安排详见后面说明并在此清晰描述。,若没有新增资源或资源变更情况就不列支，若有新增资源或资源变更情况但没有明确是哪个单位资源就由总部统一列支、若明确了各个单位所属就直接由相关单位列支，根据服务器、专属设备等规模确定工作量，500台以上、500台至200台、200台至50台、50台至20台、20台及以下，分别为40人天、20人天、10人天、5人天、2人天。
T2-0605,6,最终用户培训,,,4,,
T2-060501,6.1,,编写培训教材,可以在实施的软件产品厂商或者开发厂商提供的培训课件基础上，针对实施单位的具体情况进行调整和优化；或者新编写培训教材。,2,1人2天,统一开发、分级部署实施的项目中，各分级实施单位的相关工作调节系数按0.5计取
T2-060502,6.2,,准备培训环境,培训场地的落实和必要设备部署和调试工作等。,1,2人0.5天,培训场地的落实和必要设备部署等
T2-060503,6.3,,培训开展对甲方用户或内训师培训,按计划组织相关培训，并对培训成效进行及时评估，对于多批次培训，应依据评估不断优化培训过程，提升培训质量。,1,2人0.5天,
T2-0606,7,系统上线切换,,,5,,公司总部项目：不超过 15 天，2 人； 其他单位项目：不超过 10 天，2 人。
T2-060601,7.1,,上线切换准备,,4,,
T2-06060101,7.1.1,,,系统切换测试-技术验证（系统基础配置、权限配置有效性验证；系统功能、性能、安全测试；）和业务验证（编制业务验证指引和用例，组织业务验证培训，组织开展业务验证；）。,1,1人天。,
T2-06060102,7.1.2,,,编制上线方案-系统上线切换方案，上线应急保障方案，上线演练方案，上线试运行方案。,1,1人天。,
T2-06060103,7.1.3,,,系统上线演练-按照上线切换工作方案和演练方案，组织开展上线切换演练。,1,1人天。,
T2-06060104,7.1.4,,,召开上线启动会-策划、组织落实会议材料（材料的编写、评审、定稿）和召开上线启动会议（会议现场准备）。,1,1人天。,
T2-060602,7.2,,系统上线切换,,1,,
T2-06060201,7.2.1,,,正式环境切换-按照切换准备工作方案，成立切换工作组，开展系统正式环境切换工作。,1,各单位1人天。,
T2-06060202,7.2.2,,,差量（动态）数据补录-系统切换期间产生的增量数据，在切换后，需要开展数据补录专项工作（选填内容）。,0,,不考虑。
T2-0607,8,运行持续支持（试运行）,,,5.5,,"工作量总数参考取值：
公司总部项目：不超过90天，人数不超过5人；
其他单位项目：不超过90天，人数不超过5人。
其中系统故障处置及分析工作量参考值：
故障发生次数：不超过3次
故障处置及分析人员：技术专家+开发人员+实施人员
公司总部项目：1-2天 * 故障处置及分析人员 * 故障发生次数
其他单位项目：1-2天 * 故障处置及分析人员 * 故障发生次数
"
T2-060701,8.1,,小版本发布,,5.5,1个月试运行，总部0.25人、超高压、海南、深圳0.25人，广东、广西、云南、贵州0.5人。,
T2-06070101,8.1.1,,,解答与处理一线用户问题（操作咨询、权限配置等现场可以直接处理的问题）要包含现场和后台人员。,,,
T2-06070102,8.1.2,,,管理及跟踪反馈二线 用户问题（系统缺陷、功能改进、功能需求等现场不能直接处理的功能性问题）。,,,
T2-06070103,8.1.3,,,开展发布测评，形成测评报告。,,,
T2-06070104,8.1.4,,,发布作业管理（申请、审批），按照作业操作票，执行版本发布更新。,,,
T2-06070105,8.1.5,,,按照版本问题更新清单，组织开展功能测试验证工作。,,,
T2-060702,8.2,,运行维护,,,,
T2-06070201,8.2.1,,,实施期间进行应用服务器及数据服务器的日常运行监控。,,,
T2-06070202,8.2.2,,,实施期间进行服务器平台调优与日常维护。,,,
T2-060703,8.3,,使用答疑,,,,
T2-060704,8.4,,问题管理,,,,
,,,工作量合计,,67.5,,
