3.5.1总体安全防护要求
1、按照“同步规划、同步建设、同步投运”原则加强本系统的网络安全防护，确保不发生重大及以上信息安全事件是本系统建设与运行的网络安全防护底线。
2、需要根据《信息安全技术 网络安全等级保护基本要求》（GB/T 22239-2019）及云计算、移动互联、物联网和工业控制系统等拓展要求，进行本系统的建设、定级、防护、测评及备案，确保关键信息基础设施安全。
3、需要根据《信息安全技术 信息系统密码应用基本要求》（GB/T 39786-2021）实现国产密码应用，并对接公司统一密码服务平台以实现密码等服务的统一管控和认证。
4、需要遵循国家发改委《电力监控系统安全防护规定》（2014年第14号令）以及国家能源局《电力监控系统安全防护总体方案等安全防护方案和评估规范》(国能安全〔2015〕36号)等要求，贯彻落实“安全分区、网络专用、横向隔离、纵向认证”十六字方针。
5、需要满足《中国南方电网电力监控系统安全防护技术规范》、《南方电网公司网络安全合规库（2020版）》、《南方电网公司网络安全布防典型设计方案（2023版）》、《南方电网公司IT主流设备安全基线技术规范》、《南方电网公司涉密事项界定范围表》（南方电网办〔2016〕13号）、《南方电网公司数据共享开放指导意见（试行版)》(信息〔2017〕51号)等相关要求。
6、需要按照《中华人民共和国网络安全法》、《中华人民共和国数据安全法》、《中华人民共和国个人信息保护法》、《中国南方电网有限责任公司数据安全管理办法》（Q/CSG2152001-2022）、《南方电网数据安全总体要求技术规范》（Q/CSG1210049-2020）等法律法规相关要求，落实数据安全相关要求。
7、通过数字身份与访问控制中心，实现本系统统一账号、统一认证、统一权限控制、统一审计管理。
8、通过信息安全运行与预警系统，进行本系统的集中监控以及安全态势感知，实现集监测、预警、防护、检测、响应和恢复的动态网络安全管控。
3.5.2安全保护等级
根据《信息安全技术 网络安全等级保护基本要求》（GB/T 22239-2019）以及《南方电网管理信息系统安全等级保护标准》要求，本系统的安全等级保护拟定级为二级。并需要按照中华人民共和国国家标准《信息安全技术 网络安全等级保护基本要求》（GB/T 22239-2019）进行系统的安全防护。
系统建设完成后按照国家及南网的要求开展系统入网安评及信息系统安全等级测评及备案工作。
3.5.3物理和环境安全
本信息系统需部署在南方电网公司专业机房内。专业机房需符合国家以及南方电网公司信息机房建设技术规范以及运营相关要求。具备防震、防风、防雨、防雷、防火、防盗等保护措施。机房温、湿度的变化在本系统设备运行所允许的范围之内。
通过符合国产密码要求的电子门禁系统确保机房出入控制；通过符合国产密码要求的视频监控系统实现视频监控。
3.5.4网络和通信安全
为保证网络层的安全性，需要合理设计网络拓扑结构，并实施网络边界控制措施。
3.5.4.1网络拓扑结构
网络结构安全保证网络设备的业务处理能力具备冗余空间和链路负载均衡能力，满足业务高峰期需要。
根据本系统的安全属性，其部署在信息内网。并按照“三级（及以上）系统独立成域、二级（及以下）系统集成成域”的原则，通过虚拟化网络技术或者SDN技术实现本系统单独设域，与其他系统实现逻辑隔离，在不同网段之间进行路由控制，建立安全的访问路径，实行针对性、差异化防护。
涉及internet的应用，需部署在信息外网区，使用统一集中的互联网出口，并通过信息安全交换平台实现强逻辑隔离。
要求采用冗余技术设计网络拓扑结构，确保路由冗余。
网络优先级配置：根据本系统的重要性设置带宽分配级别，保证在网络发生拥堵的时候优先本系统服务连续性。
网络设备冗余配置，避免存在网络单点故障，确保网络设备高可靠性。
3.5.4.2网络通信安全
数据通信中要采用校验技术或密码技术保证通信过程中数据的完整性以及保密性。
3.5.4.3网络边界防护
通过ACL技术或防火墙技术，在网络边界或区域之间根据访问控制策略设置访问控制规则，实现对本系统域实现端口级访问控制，默认情况下除允许通信外受控接口拒绝所有通信；应删除多余或无效的访问控制规则，优化访问控制列表，并保证访问控制规则数量最小化。并对源地址、目的地址、源端口、目的端口和协议等进行检查，以允许/拒绝数据包进出，保证信息及网络资源不被非法使用和访问。 
通过网络流量威胁分析系统在重要边界全覆盖，提供攻击者维度的网络流量安全监测分析，对攻击者进行溯源分析及攻击场景分析。通过在各边界部署的网络数据防泄漏系统，识别传输敏感数据，监控敏感数据使用情况，并进行阻断，防止敏感数据外泄。
结合实际选用攻击诱捕系统对攻击行为进行捕获分析。
在第三方边界按实际需求使用网闸提供网络物理隔离和数据摆渡能力。
通过入侵监测技术，在网络边界处监视如端口扫描、强力攻击、木马后门攻击、拒绝服务攻击、缓冲区溢出攻击、IP碎片攻击和网络蠕虫攻击等攻击行为，并给予告警以及响应和处理。
在网络边界及核心业务网段处对恶意代码进行检测和清除；及时实现恶意代码库升级和检测系统更新。
通过网络安全扫描工具，利用优化系统配置和打补丁等各种方式最大可能地弥补最新的安全漏洞和消除安全隐患。
管理信息区外网V区DMZ区域需按照《南方电网公司网络安全布防典型设计方案（2023版）》要求，具备访问控制、入侵防御、应用攻击防护、网页防篡改、流量监测、病毒防护、数据防泄漏、威胁感知、主机安全防护和攻击诱捕等能力。区域内各逻辑子域之间实现逻辑隔离，可共享或独立部署区域内网络安全布防措施。
管理信息区内网IV区IDC区域需按照《南方电网公司网络安全布防典型设计方案（2023版）》要求，具备访问控制、网络流量威胁分析、应用攻击防护、网页防篡改、病毒防护、数据防泄漏、威胁感知、主机安全防护以及攻击诱捕等能力，区域内各逻辑子域之间实现逻辑隔离，可共享或独立部署区域内网络安全布防措施。
3.5.4.4网络安全审计
通过信息安全运行预警系统，实现对网络设备、安全设备运行状况、网络流量、用户行为等进行日志信息实时采集、集中监控及实时预警。审计记录应包括：事件的日期和时间、用户、事件类型、事件是否成功及其他与审计相关的信息。
3.5.4.5网络安全加固
1、对登录网络设备的用户进行身份鉴别。
2、禁止采用默认的管理员账号和密码。
3、对网络设备管理员登录的地址进行限制。
4、通过支持国密算法的U-key认证方式登录，key证书具有唯一性。
5、网络设备账号满足密码复杂度设置，并定期进行更新，存储为加密存储方式。
6、具有登录失败处理功能，登录5次失败后，采取结束会话的措施。
7、采取SSH加密协议远程管理网络设备。
8、已通过服务器区防火墙进行限制，只对系统的特定端口进行开放。
3.5.5设备和计算安全
3.5.5.1硬件安全
采用服务器设备具备冗余配置（包括双机热备等）；具备不间断电源保障，具备服务器运行状态监控，确保本系统处理性能要求。
对登录的用户进行身份标识和鉴别，身份标识具有唯一性，身份鉴别信息具有复杂度要求并定期更换；应具有登录失败处理功能，应配置并启用结束会话、限制非法登录次数和当登录连接超时自动退出等相关措施；当进行远程管理时，应采取加密措施。
3.5.5.2操作系统安全
操作系统原则上采用自主可控系统，加强操作系统账号管理、认证授权、安全日志等功能，实现从身份鉴别，访问控制，安全审计入侵防范，恶意代码规范，资源控制几个方面进行主机安全基线加固。
3.5.5.3中间件安全
从身份鉴别，访问控制，安全审计，通信完整性，通信保密性，软件容错，资源控制几个方面进行中间件安全基线加固。
3.5.5.4数据库安全
禁止采用默认的管理员账号和密码，避免非法用户进入到网络后通过直接调用监控末端设备查看相关信息。
3.5.6应用和数据安全
3.5.6.1数据安全
采用身份认证、权限控制、加密存储、加密传输、数据防泄密等技术，加强本系统数据机密性及安全性防护：
1、通过对数据库表设置完整性约束，如Check、NOT NULL、Unique、Primary、Foreign key来保证数据的完整性。
2、使用国产密码技术对本系统数据库表访问权限进行控制。
3、采用国产密码技术对本系统的重要数据进行加密存储，防止数据库被黑客攻击导致系统机密泄漏。
4、使用国产密码技术保证本系统重要数据传输过程中的机密性及完整性。
5、仅采集和保存业务必需的用户个人信息；禁止未授权访问、使用用户个人信息。
6、通过数据防泄密网关，减少敏感数据泄密。
7、采用数据本地备份或者数据灾备技术，确保本系统核心数据安全，确保在某个存储设备故障或灾害发生时，数据不会丢失。
8、存储设备报废前按照规定通过消磁粉碎一体机进行信息彻底清除，确保数据不能被恢复、还原。
9、确保本系统日志信息保持6个月以上。并采用国产密码技术实现本系统日志信息完整性保护。
10、采用国产密码技术实现本系统的加载和卸载安全控制。
11、实现数据库访问审计。
3.5.6.2应用安全
1、采取三权分立，本系统应具备完善的权限管理，贯穿全系统的分级授权和界面信息操作控制，完整的应用程序日志记录和审计机制。
2、提供访问控制功能，通过角色划分实现各层各级人员对于功能页面的访问控制；依据安全策略控制用户对文件、数据库表等客体的访问；访问控制的覆盖范围应包括与资源访问相关的主体、客体及它们之间的操作；授权主体配置访问控制策略，并严格限制默认账户的访问权限。
3、实现对登录用户的统一身份标识和鉴别，实现身份鉴别信息的防截获、防假冒和防重用，保证本系统用户身份的真实性。
4、启用身份鉴别、用户身份标识唯一性检查、用户身份鉴别信息复杂度检查以及登录失败处理功能，并根据安全策略配置相关参数。
1）用户身份鉴别信息应不易被冒用，口令复杂度应满足要求并定期更换。应提供用户身份标识唯一和鉴别信息复杂度检查功能，保证应用系统中不存在重复用户身份标识；用户在第一次登录系统时修改分发的初始口令，口令长度不得小于8位，且为字母、数字或特殊字符的混合组合，用户名和口令禁止相同；应用软件不得明文存储口令数据；
2）提供登录失败处理功能，可采取结束会话、限制非法登录次数和自动退出等措施；
3）授予不同账户为完成各自承担任务所需的最小权限，并在它们之间形成相互制约的关系。
4）及时删除或停用多余的、过期的账号，避免共享账号。
5、采用基于国产密码的数据验签技术保证通信过程中数据的完整性；
6、通过基于国产密码的加解密技术，实现对重要数据的传输安全防护。
7、通过基于国产密码的加解密技术实现数据有效性检验功能
8、加强数据有效性检查，保证通过人机接口输入或通过通信接口输入的数据格式或长度符合系统设定要求。
9、关闭不需要的端口及服务。
10、通过负载均衡技术，确保在突发数据流的情况下，本系统依然可以提供适当的服务能力。
11、当通信双方中的一方在一段时间内未作任何响应，另一方自动结束会话；对系统的最大并发会话连接数进行限制；对单个账号的多重并发会话进行限制。
12、通过防病毒系统，实现统一控制台对应用系统病毒防范，包括统一的分发、维护、更新和报警等。
13、通过对用户的登录、退出、增加用户、修改用户权限等进行应用审计。
14、通过信息安全运行预警系统，实现对本系统的运行状态、用户体验等的实时监控与预警。
15、要求通过支持国产密码的堡垒机等技术，实现对本系统运维的统一管控，避免对网络和服务器资源的直接访问，对不合法命令进行命令阻断，过滤掉所有对目标设备的非法访问行为，减少和恶意攻击，拦截非法访问，并实现运维操作行为审计。
3.5.7终端安全设计
1、要求通过终端安全管理系统，提供操作终端系统的准入、认证及授权管理。
2、通过终端数据防泄漏系统，识别终端外发敏感数据，监控敏感数据使用情况，并进行阻断，防止敏感数据外泄。
3、通过终端防病毒系统，提供终端病毒、木马等终端风险监测及清除能力。
4、通过上网行为管理系统，加强信息外网办公终端internet访问控制，如网络应用控制、内容过滤、带宽流量管理、上网行为分析等。对内部网络中出现的内部用户未通过准许私自联到外部网络的行为进行检查。
5、存储设备报废前按照规定通过消磁粉碎一体机进行信息彻底清除，确保数据不能被恢复、还原。
6、对访问的移动终端进行安全防护，通过沙箱等技术确保本地数据安全存储，通过身份认证及权限控制技术确保访问安全；通过加密技术实现传输安全了通过设备远程控制技术，如设备定位、设备远程数据擦除、锁定、更改密码等确保重要数据一键式擦除。
3.5.8数据安全防护
1、系统应按照公司相关要求完成对应的数据分类分级工作并申请开展数据安全风险评估工作。
2、定期梳理排查系统元数据是否均已录入数据资产管理平台，按照数据资产安全管理要求进行管控。
3、对数据安全事件和可能引发数据安全事件的风险隐患进行收集、分析判断和持续监控预警，常态化持续监测数据安全风险。
4、系统应明确数据管理、审计类账号权限开通、分配、使用、变更、注销等安全管理要求，明确对账号及对应权限进行记录。
5、对数据跨网络区域传输采取安全管控措施，包括但不限于网络及应用层的访问控制策略，控制粒度为端口级。
6、在数据接口调用前进行身份鉴别，通过技术手段限制非白名单接口接入，对数据接口开展定期安全检测、实施调用审批流程并开展接口日志审计。
7、在数据收集、数据存储、数据传输、数据加工、数据使用、数据共享、数据销毁与删除等数据安全全生命周期各阶段，采取对应安全保障措施。
8、涉及个人信息收集需严格遵循《中华人民共和国个人信息保护法》等法律法规要求，应检查是否向个人信息主体告知收集、使用个人信息的目的、方式和范围等规则，并获得个人信息主体的授权同意。
3.5.9安全管理
3.5.9.1安全管理机构与人员
1、设立信息安全管理工作的职能部门。
2、具备专职安全管理员，具备明确岗位及职责。
3、建立关键岗位人员保密制度和调离制度。
4、对被录用人员的身份、背景、专业资格和资质等进行审查；人员离职时应及时终止离岗员工的所有访问权限，取回各种身份证件、钥匙、徽章等以及机构提供的软硬件设备。
3.5.9.2安全策略和管理制度
1、遵循南方电网公司网络安全工作的总体方针和安全策略。
2、针对安全管理活动中的各类管理内容建立安全管理制度及作业指导书。 
3、建立人员培训制度。 
4、了解并遵守密码相关法律法规。
3.5.9.3安全建设管理
1、根据本系统的安全保护等级及国产密码应用需求，进行安全整体规划和安全方案设计。
2、开展本系统的定级、防护、测评及备案。
3、开展本系统商用密码应用安全性评估、网络安全风险评估、数据安全风险评估。
4、信息安全产品采购和使用符合国家的有关规定；应确保密码产品采购和使用符合国家密码主管部门的要求。全面推进IT设备国产化。
5、系统上线前开展系统入网安评。
3.5.9.4安全运维管理
1、定期更新漏洞库。采用漏洞扫描、数据库扫描等检测技术，定期检测操作系统、中间件、数据库、本系统的安全漏洞，全面评估安全漏洞和认证、授权、完整性方面的问题，并进行安全加固。
2、按照安全运维规范，采用符合南方电网公司网络安全布防典型设计方案的安全工具对主机恶意代码进行定期查杀，预防主机恶意代码库和主机恶意代码软件，定期检查主机恶意代码库的升级情况，对截获的主机恶意代码进行及时分析处理。
3、实现本系统统一IT资产统一管理以及运维管理服务。
4、实现本系统统一的安全运维及集中监控及预警。
5、对本系统实现定期本地备份。
6、对本系统实现同城与异地数据级或应用级灾备防护，定制应急预案并加强定期演练。
7、实现安全事件快速响应、及时处理。
8、正确使用密码相关产品。
9、原则上自主运维。如确实需要外包运维的，需与选定的外包运维服务商签订服务协议，明确约定外包运维的范围、工作内容及工作要求。