-- 创建用户如果不存在
CREATE USER IF NOT EXISTS 'gedi'@'%' IDENTIFIED BY '<EMAIL>';

-- 授予权限
GRANT ALL PRIVILEGES ON gedi.* TO 'gedi'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

USE gedi;

CREATE TABLE users (
    user_id INT NOT NULL AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    phone_number VARCHAR(20),
    department VARCHAR(100),
    full_name VARCHAR(100),
    is_admin TINYINT(1) DEFAULT 0,
    registration_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id)
); 