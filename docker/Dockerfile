# ---- Builder Stage ----
FROM python:3.11-slim AS builder

WORKDIR /app

# Install system dependencies and build tools
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    python3-dev \
    libffi-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy the production requirements file
COPY requirements.txt .

# Install Python production dependencies
RUN pip install --no-cache-dir -i https://pypi.mirrors.ustc.edu.cn/simple/ -r requirements.txt

# Copy the rest of your application source code to the builder stage
# This is done after pip install to optimize layer caching if requirements change less often than code
COPY . .

# ---- Final Stage ----
FROM python:3.11-slim

WORKDIR /app

# Install runtime system dependencies
# libffi-dev is kept here as some Python packages might need libffi at runtime.
# If you identify that only libffi.so.X (e.g. libffi8) is needed, you can install that specific package.
RUN apt-get update && apt-get install -y --no-install-recommends \
    libffi-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Set environment variables
ENV UVICORN_LOOP=asyncio
ENV PYTHONPATH=/app

# Copy installed Python packages from the builder stage
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Copy your application code (respecting .dockerignore)
COPY . .

# Ensure start.sh has execution permissions
RUN chmod +x start.sh

# Create an empty 'uploads' directory if it's guaranteed to be needed and might not be in source
# RUN mkdir /app/uploads

# Expose port
EXPOSE 12010

# Use start.sh script to launch the application
ENTRYPOINT ["./start.sh"] 