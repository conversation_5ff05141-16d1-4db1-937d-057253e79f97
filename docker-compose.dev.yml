# 定义服务
services:
  # 应用服务配置
  app:
    # 使用当前目录的 Dockerfile 构建镜像
    build:
      context: .
      dockerfile: docker/Dockerfile
    # 指定镜像名称和标签
    image: office_plugin:dev
    # 端口映射 - 宿主机端口:容器端口
    ports:
      - "12010:12010"
    # 环境变量配置
    environment:
      # 应用环境标识
      APP_MODE: dev
      # 加密用户token所需密钥
      SECRET_KEY: sk-hdgw873grfgw78rghwsegrf7283t4gds8
      # 文件上传配置
      UPLOAD_DIRECTORY: uploads
      MAX_FILE_SIZE: 52428800 # 50MB in bytes
      # 数据库配置
      DB_HOST: mysql # 使用服务名作为主机名
      DB_PORT: 3306 # 容器内部端口
      DB_USER: gedi
      DB_PASSWORD: <EMAIL>
      DB_NAME: gedi
      # 图数据库配置 (TuGraph)
      GDB_HOST: ***************
      GDB_USER: esol_backend
      GDB_PASSWORD: esol@TuGraph

      # LLM Configurations (YAML string)
      LLM_CONFIGURATIONS_RAW: |-
          - name: "150服务器"
            model: "qwen3-30b-a3b"
            base_url: "http://***************:8090/v1"
            api_key: "gpustack_2f8e57153c016cbf_1a67126940377ba3c174726d73a444d4"
            temperature: 0.6
            max_tokens: 4096
            stream: false
            weight: 50

      # 静态文件访问配置
      STATIC_URL: http://***************:12010/static/
      # 日志配置
      LOG_LEVEL: INFO
      LOG_DIR: logs
      LOG_FORMAT: human  # 强制使用人类可读的日志格式
    # 依赖服务配置 - 确保 MySQL 完全启动后再启动应用
    depends_on:
      mysql:
        condition: service_healthy

  # MySQL 数据库服务配置
  mysql:
    # 使用自定义的 MySQL Dockerfile
    build:
      context: .
      dockerfile: docker/mysql.Dockerfile
    # 指定镜像名称和标签
    image: office_plugin_mysql:dev
    # 端口映射 - 使用3307避免与宿主机MySQL冲突
    ports:
      - "3307:3306"
    # MySQL 环境变量配置
    environment:
      - MYSQL_ROOT_PASSWORD=<EMAIL>
      - MYSQL_DATABASE=gedi
      - MYSQL_USER=gedi
      - MYSQL_PASSWORD=<EMAIL>
    # 数据卷配置
    volumes:
      # 持久化数据存储
      - mysql_data:/var/lib/mysql
      # 初始化SQL脚本
      - ./docker/init.sql:/docker-entrypoint-initdb.d/init.sql
    # 健康检查配置
    healthcheck:
      test: [ "CMD", "mysqladmin", "ping", "-h", "localhost" ]
      interval: 10s # 检查间隔
      timeout: 5s # 超时时间
      retries: 5 # 重试次数

# 定义数据卷
volumes:
  # MySQL 数据持久化卷
  mysql_data:
