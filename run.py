import uvicorn
import mysql.connector
import sys
from app.core.config import settings
from app.utils.database import DatabaseManager
from app.utils.llm import chat_with_llm  # For the new check_llm_connection
from app.core.logging_config import setup_logging, get_logger

# 设置标准输出和标准错误为无缓冲模式
sys.stdout.reconfigure(line_buffering=True)
sys.stderr.reconfigure(line_buffering=True)

# 初始化日志系统
setup_logging(app_mode=settings.APP_MODE, log_dir=settings.LOG_DIR)
logger = get_logger(__name__)


def _log_llm_config():
    """记录LLM服务配置信息"""
    logger.info(f"找到 {len(settings.LLM_CONFIGURATIONS)} 个可用的 LLM 服务")
    if settings.LLM_CONFIGURATIONS:
        for i, config in enumerate(settings.LLM_CONFIGURATIONS):
            logger.info(
                f"LLM 配置 #{i+1}: {config.get('name', 'N/A')} - "
                f"Model: {config.get('model', 'N/A')} - "
                f"Base URL: {config.get('base_url', 'N/A')}"
            )
    else:
        logger.warning("未在环境变量中找到任何LLM配置")


def check_database_connection():
    """检查数据库连接是否正常"""
    try:
        db = DatabaseManager(
            host=settings.DB_HOST,
            port=settings.DB_PORT,
            user=settings.DB_USER,
            password=settings.DB_PASSWORD,
            database=settings.DB_NAME
        )
        db.connect()

        # 使用 with 语句确保资源正确释放
        with db.get_cursor() as cursor:
            # 检查数据库版本
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()

            # 检查数据库状态
            cursor.execute("SELECT 1")
            result = cursor.fetchone()

            if result and version:
                logger.info("数据库连接成功")
                return True
            else:
                logger.error("数据库连接异常: 无法获取数据库信息")
                _log_db_config()
                return False

    except mysql.connector.Error as err:
        _log_db_config()
        if err.errno == mysql.connector.errorcode.ER_ACCESS_DENIED_ERROR:
            logger.error("数据库连接失败: 用户名或密码错误")
        elif err.errno == mysql.connector.errorcode.ER_BAD_DB_ERROR:
            logger.error("数据库连接失败: 数据库不存在")
        else:
            logger.error(f"数据库连接失败: {str(err)}")
        return False
    except Exception as e:
        _log_db_config()
        logger.error(f"数据库连接失败: {str(e)}", exc_info=True)
        return False
    finally:
        if 'db' in locals():
            db.disconnect()


def _log_db_config():
    """记录数据库配置信息"""
    logger.info(f"数据库连接地址: {settings.DB_HOST}")
    logger.info(f"数据库连接端口: {settings.DB_PORT}")
    logger.info(f"数据库连接用户名: {settings.DB_USER}")
    logger.info(f"数据库连接数据库: {settings.DB_NAME}")
    # 不记录密码到日志中


if __name__ == "__main__":
    logger.info("正在启动有解助手后端服务...")

    # 检查必要的服务连接
    db_ok = check_database_connection()

    _log_llm_config()

    try:
        logger.info("启动uvicorn服务器...")
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=12010,
            reload=False,
            loop="asyncio"
        )
    except Exception as e:
        logger.error(f"服务启动失败: {str(e)}", exc_info=True)
