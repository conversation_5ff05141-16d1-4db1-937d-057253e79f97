# Python specific
**/__pycache__/
*.py[cod]
.Python/
.ipynb_checkpoints/

# Version Control
.git/
.gitignore
.gitmodules

# IDE & Editor specific
.vscode/
.idea/
*.iml
*.sublime-project
*.sublime-workspace

# Virtual environments
venv/
.env/
venv*/
*.venv
.env*/

# Build artifacts & Distribution
dist/
build/
*.egg-info/
*.egg
*.whl

# Test related
tests/
# test/ # If you have a top-level 'test' dir not 'tests'
coverage/
.coverage
htmlcov/
.pytest_cache/

# Logs & Temporary files
*.log
*.tmp
*.bak
*.swp
*.swo
nohup.out

# Application specific
# Assuming app/ocr_utils/ is a directory you want to completely exclude
app/ocr_utils/

# Uploads: ignore contents. The 'uploads' directory itself will be copied if it exists.
# If you need to ensure 'uploads' directory is created empty even if not in source, 
# add 'RUN mkdir /app/uploads' to your Dockerfile's final stage.
uploads/*

# Static files:
# Ignore all files and directories directly under 'static/'
static/*
# Then, un-ignore 'admin' and 'downloads' directories (and their contents)
!static/admin/
!static/downloads/
