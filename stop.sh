#!/bin/bash

# 查找所有监听12010端口的进程ID
pids=$(lsof -ti:12010)

if [ -z "$pids" ]; then
    echo "没有找到监听12010端口的进程"
    exit 0
fi

# 遍历所有进程ID并尝试优雅停止
for pid in $pids; do
    echo "正在停止进程 $pid..."
    kill -15 $pid
done

# 等待5秒检查进程是否已经停止
sleep 2

# 再次检查是否还有进程在运行
remaining_pids=$(lsof -ti:12010)
if [ ! -z "$remaining_pids" ]; then
    echo "一些进程未能正常停止，正在强制终止..."
    for pid in $remaining_pids; do
        echo "强制终止进程 $pid..."
        kill -9 $pid
    done
fi

echo "所有相关进程已停止" 