import json
import os
from fastapi import APIRouter, HTTPException
from app.utils.doc_processor import docx_search
from app.utils.response import APIResponse, api_response
from app.docs_api.schemas import ProjectInfoRequest
from app.core.config import settings


router = APIRouter()


@router.post("/risk_analysis", summary="项目风险分析", response_model=None)
@api_response
async def risk_analysis(request: ProjectInfoRequest) -> APIResponse:
    """
    生成项目风险分析。

    Args:
        request: 包含用户名和项目名称的请求对象

    Returns:
        APIResponse: 包含风险分析列表的响应对象

    Raises:
        HTTPException: 当文档处理出错时抛出500错误
    """
    try:
        result_text = docx_search(request.username, request.project_name, '项目风险分析')
        # 如果厂家资料包含项目风险分析，则直接返回
        if result_text:
            return APIResponse(200, "检测到厂家资料已包含项目风险分析，直接返回", result_text)
        else:
            # 如果厂家资料不包含项目风险分析，则读取模板并返回
            RISK_ANALYSIS_TEMPLATE_PATH = os.path.join(settings.PROJECT_ROOT, 'documents', '项目风险分析.json')
            if os.path.exists(RISK_ANALYSIS_TEMPLATE_PATH):
                with open(RISK_ANALYSIS_TEMPLATE_PATH, 'r', encoding='utf-8') as f:
                    risk_analysis_template = json.load(f)
                    result = []
                    for item in risk_analysis_template['risk_list']:
                        result.append({"style": "2", "content": item['title']})
                        result.append({"style": "0", "content": item['content']})
                return APIResponse(200, "未检测到厂家资料中的项目风险分析，返回模板", result)
            else:   # 找不到模板
                return APIResponse(200, "厂家资料中未识别出项目风险分析，且项目风险分析模板不存在", None)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
