from fastapi import APIRouter, HTTPException
from app.utils.doc_processor import docx_search
from app.utils.llm import chat_with_llm
from app.utils.response import APIResponse, api_response
from app.docs_api.schemas import ProjectInfoRequest
from app.templates.prompt_templates import feasibility_summary_prompt, generate_feasibility_conclusion_prompt
router = APIRouter()


@router.post("/feasibility_conclusion", summary="生成项目可研结论")
@api_response
async def technical_feasibility(request: ProjectInfoRequest):
    """
    生成给定的项目可研结论。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **technical_feasibility**: 项目可研结论文本

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        # 检索厂家资料是否提供了项目可研结论章节
        raw_feasibility_conclusion = docx_search(
            request.username, request.project_name, '项目可研结论')

        if raw_feasibility_conclusion is None:
            raw_feasibility_conclusion = []  # 将 None 转换为空列表，防止迭代错误

        raw_feasibility_conclusion = [
            item for item in raw_feasibility_conclusion if item.get('content').strip() != '']

        # 如果项目可研结论为空，则根据项目背景、项目目标等生成
        if not raw_feasibility_conclusion:
            # 获取项目背景、项目目标、现状分析等信息
            background_text = docx_search(
                request.username, request.project_name, '项目背景', format='string')
            target_text = docx_search(
                request.username, request.project_name, '项目目标', format='string')
            status_text = docx_search(
                request.username, request.project_name, '现状分析', format='string')
            demand_text = docx_search(
                request.username, request.project_name, '业务需求', format='string')
            feature_text = docx_search(
                request.username, request.project_name, '功能需求', format='string')
            necessity_conclusion_text = docx_search(
                request.username, request.project_name, '必要性结论', format='string')

            # 整合信息，构建用于生成项目可研结论的上下文
            context = ""
            if background_text:
                context += "## 项目背景：" + background_text + "\n"
            if target_text:
                context += "## 项目目标：" + target_text + "\n"
            if status_text:
                context += "## 现状分析：" + status_text + "\n"
            if demand_text:
                context += "## 业务需求：" + demand_text + "\n"
            if feature_text:
                context += "## 功能需求：" + feature_text + "\n"
            if necessity_conclusion_text:
                context += "## 必要性结论：" + necessity_conclusion_text + "\n"

            if not context:  # 如果无法获取足够的内容
                return APIResponse(404, "厂家资料未提供项目可研结论章节，且缺乏足够信息生成项目可研结论", [{"style": "-5", "content": "缺乏足够信息生成项目可研结论"}])

            # 调用大模型生成项目可研结论
            result_text = chat_with_llm(
                generate_feasibility_conclusion_prompt + context)
            result_text = result_text.replace("\n\n", "\n").strip()
            return APIResponse(200, "厂家资料未提供项目可研结论章节，基于其他参考内容生成项目可研结论", [{"style": "0", "content": result_text}])

        else:
            # 仅对 `style == "0"` 的内容进行润色
            for item in raw_feasibility_conclusion:
                if item["style"] == "0":
                    original_content = item["content"]
                    refined_content = chat_with_llm(
                        feasibility_summary_prompt + original_content)
                    item["content"] = refined_content.replace(
                        "\n\n", "\n").strip()

            # 返回优化后的项目可研结论
            return APIResponse(200, "识别出项目可研结论并润色成功", raw_feasibility_conclusion)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
