# 本文件用于存放以下接口：
# - 生成现状分析 /current_status
# - 生成需求分析-业务需求 /demand/business_demand
# - 生成需求分析-功能需求 /demand/functional_demand
# - 生成需求分析-非功能需求 /demand/non_functional_demand
# - 生成需求分析-业务集成需求 /demand/business_integration_demand
# - 生成需求分析-安全需求 /demand/security_demand
# - 生成需求分析-用户规模 /demand/user_scale
# - 生成必要性结论 /necessity_conclusion

# 导入相关依赖
import os
from fastapi import APIRouter, HTTPException
from app.core.logging_config import get_logger
from app.utils.doc_processor import docx_search
from app.utils.llm import chat_with_llm
from app.utils.response import APIResponse, api_response
from app.utils.tugraph import search_function
from app.docs_api.schemas import ProjectInfoRequest, UserscaleRequest
from app.templates.prompt_templates import polish_current_prompt, extract_function_logical, extract_user_scope, polish_necessity_conclusion_prompt, generate_necessity_conclusion_prompt
import json
from app.core.config import settings
from app.utils.documents import get_template_path

router = APIRouter()
logger = get_logger(__name__)


@router.post("/current_status", summary="现状分析")
@api_response
async def current_status(request: ProjectInfoRequest):
    """
    生成给定的现状分析。
    """
    try:
        current_status = docx_search(request.username, request.project_name, '现状分析', format='string')
        if not current_status:
            raise HTTPException(status_code=404, detail="未找到厂家资料中的现状分析！")
        content = chat_with_llm(polish_current_prompt + "项目名称为：" + request.project_name + "\n原始段落为：" + current_status)
        content = content.replace('\n\n', '\n').strip()
        if not content:
            raise HTTPException(status_code=404, detail="现状分析内容为空，请检查厂家资料是否完整！")
        return APIResponse(200, "生成现状分析成功", [{"style": "0", "content": content}])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/demand/business_demand", summary="生成业务需求")
@api_response
async def requirement_generate(request: ProjectInfoRequest):
    """
    生成给定的业务需求。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **requirement**: 业务需求文本

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        requirement = docx_search(request.username, request.project_name, '业务需求')
        return APIResponse(200, "生成业务需求成功", requirement)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/demand/functional_demand", summary="生成功能需求")
@api_response
async def feature_generate(request: ProjectInfoRequest):
    """
    生成给定的功能需求。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **feature**: 功能需求文本

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        feature = docx_search(request.username, request.project_name, '功能需求')
        return APIResponse(200, "生成功能需求成功", feature)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/demand/performance_demand", summary="性能需求")
@api_response
async def performance_demand(request: ProjectInfoRequest):
    """
    生成给定的性能需求。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含以下字段:
        - username (str): 用户名
        - project_name (str): 项目名称

    Returns:
    - **APIResponse**: 包含以下字段:
        - code (int): 状态码，200表示成功
        - message (str): 响应消息
        - data (dict): 包含以下字段:
            - performance_demand (str): 性能需求文本内容

    Raises:
    - **HTTPException(500)**: 服务器内部错误
    """
    try:
        performance_demand = docx_search(request.username, request.project_name, '性能需求')

        if performance_demand:
            return APIResponse(200, "厂家资料中识别出性能需求，直接返回", performance_demand)
        else:
            # 如果性能需求为空，则返回模板，让用户自行修改
            performance_demand_template = open(get_template_path('性能需求模板.txt'), 'r', encoding='utf-8').read()
            return APIResponse(200, "未识别出性能需求，返回默认模板", [{"style": "0", "content": performance_demand_template}, {"style": "-5", "content": "请根据实际情况修改性能需求内容"}])

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成性能需求失败: {str(e)}")


@router.post("/demand/business_integration_demand", summary="生成业务集成需求")
@api_response
async def business_integration(request: ProjectInfoRequest):
    """
    生成给定的业务集成需求。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **result_txt**: 结果文本

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        # 识别厂家资料中的集成需求
        result_txt = docx_search(request.username, request.project_name, '业务集成需求', format='string')
        if not result_txt:
            result_txt = docx_search(request.username, request.project_name, '集成需求', format='string')
            if not result_txt:
                return APIResponse(200, "未识别出业务集成需求，请检查厂家资料是否完整！", [{"style": "-5", "content": "未识别出业务集成需求，请检查厂家资料是否完整！"}])
        # 抽取集成需求中的功能项和逻辑文件
        data_txt = chat_with_llm(extract_function_logical + "项目资料：" + result_txt)
        data_dict = json.loads(data_txt)
        # 提取功能项列表、逻辑文件列表
        function_list = data_dict.get("功能项", [])
        function_nodes = search_function(function_list)
        mark_txt = ""
        if function_list != function_nodes:
            mark_txt = "资料中识别出功能项如下：" + "、".join(function_list) + "。"
            if function_nodes:
                mark_txt += "\n图谱中查询功能项结果如下：" + "、".join(function_nodes) + "。\n"
            else:
                mark_txt += "\n图谱中未查询到相关功能项结点，请仔细审核！"
        logic_list = data_dict.get("逻辑文件", [])
        logic_nodes = search_function(function_list)
        if logic_list != logic_nodes:
            if not mark_txt:
                mark_txt = "资料中识别出逻辑文件如下：" + "、".join(logic_list) + "。"
            else:
                mark_txt += "\n资料中识别出逻辑文件如下：" + "、".join(logic_list) + "。"
            if logic_nodes:
                mark_txt += "\n图谱中查询逻辑文件结果如下：" + "、".join(function_nodes) + "。\n"
            else:
                mark_txt += "\n图谱中未查询到相关逻辑文件结点，请仔细审核！"
        result_txt = result_txt.replace('\n\n', '\n').strip()
        if mark_txt != "":
            return APIResponse(200, "生成业务集成需求成功", [{"style": "0", "content": result_txt}, {"style": "-5", "content": mark_txt}])
        else:
            return APIResponse(200, "生成业务集成需求成功", [{"style": "0", "content": result_txt}])
    except Exception as e:
        logger.error(f"生成业务集成需求失败：{e}")
        return APIResponse(500, "生成业务集成需求失败", data=str(e))


@router.post("/demand/security_demand", summary="安全需求")
@api_response
async def security_demand(request: ProjectInfoRequest):
    """
    生成给定的安全需求。
    """
    try:
        result_text = docx_search(request.username, request.project_name, '安全需求')
        if result_text:  # 如果word版厂家资料有安全需求，则直接返回
            return APIResponse(200, "生成安全需求成功", result_text)

        else:  # 如果没有，则使用模板
            # 读取模板
            security_demand_template_path = os.path.join(settings.PROJECT_ROOT, 'documents', '安全需求模板.txt')
            if not os.path.exists(security_demand_template_path):
                raise HTTPException(status_code=404, detail="文件 '安全需求模板.txt' 不存在")
            security_demand_template = open(security_demand_template_path, 'r', encoding='utf-8').read()

            # 读取excel版的厂家资料
            user_project_path = os.path.join(settings.PROJECT_ROOT, 'uploads', request.username, request.project_name)

            # 查找user_project_path中以_config.json结尾的文件
            config_files = [f for f in os.listdir(user_project_path) if f.endswith('_config.json')]

            # 如果存在，则读取config_files中"系统等保级别"的值
            if config_files:
                config_file_path = os.path.join(user_project_path, config_files[0])
                config_data = json.load(open(config_file_path, 'r', encoding='utf-8'))
                security_level = config_data.get("系统等保级别", "")
                if security_level:
                    # 如果厂家资料中存在SECURITY_LEVEL，则替换模板中的SECURITY_LEVEL
                    security_demand_template = security_demand_template.replace("SECURITY_LEVEL", security_level)

            return APIResponse(200, "生成安全需求成功", [{"style": "0", "content": security_demand_template}])

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/demand/user_scale", summary="生成用户规模需求")
@api_response
async def user_scale(request: UserscaleRequest):
    """
    生成给定的用户规模需求。

    Parameters:
    - **request**: UserscaleRequest 对象，包含用户名、项目名称和用户数量

    Returns:
    - **result_txt**: 结果文本

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:

        # 获取厂家资料原文中最大用户数
        result_txt = docx_search(request.username, request.project_name, '用户规模', format='string')
        compare_txt = docx_search(request.username, request.project_name, '性能需求', format='string')
        compare_data = None
        if compare_txt:
            compare_data = chat_with_llm(extract_user_scope + "项目资料：" + compare_txt)
        # 若厂家资料中未提及用户规模
        if not result_txt and not compare_data:
            result_txt = "使用该系统用户包括南方电网公司总部、各分子公司（涵盖省、地、区县、供电所、班组、个人）岗位员工及公司外部XX用户，约YY人。"
            if not request.user_number:
                # 读取excel版厂家资料中的相关配置
                user_project_path = os.path.join(settings.PROJECT_ROOT, 'uploads', request.username, request.project_name)
                config_files = [f for f in os.listdir(user_project_path) if f.endswith('_config.json')]
                if config_files:
                    config_file_path = os.path.join(user_project_path, config_files[0])
                    config_data = json.load(open(config_file_path, 'r', encoding='utf-8'))
                    request.user_number = config_data.get("system_user_number", "")
            if request.user_number:
                result_txt = result_txt.replace("YY", str(int(request.user_number)))
            mark_txt = "请确认该部分用户数！"
        # 若提及最大用户数
        elif result_txt and compare_data:
            result_data = chat_with_llm(extract_user_scope + "项目资料：" + result_txt)
            result_dict = json.loads(result_data)
            result_max = result_dict.get("最大用户数", "")
            compare_dict = json.loads(compare_data)
            compare_max = compare_dict.get("最大用户数", "")
            if result_max != compare_max:
                mark_txt = "资料中识别出最大用户数为" + result_max + "，与性能需求中识别出的最大用户数" + compare_max + "不一致，请仔细审核！"
        elif not result_txt and compare_data:
            result_txt = "使用该系统用户包括南方电网公司总部、各分子公司（涵盖省、地、区县、供电所、班组、个人）岗位员工及公司外部XX用户，约YY人。"
            compare_data = chat_with_llm(extract_user_scope + "项目资料：" + compare_txt)
            compare_dict = json.loads(compare_data)
            compare_max = compare_dict.get("最大用户数", "")
            result_txt = result_txt.replace("YY", compare_max)
            mark_txt = "请确认该部分用户数！"
        
        result_txt = result_txt.replace('\n\n', '\n').strip()

        if mark_txt:
            return APIResponse(200, "生成用户规模成功", [{"style": "0", "content": result_txt}, {"style": "-5", "content": mark_txt}])
        else:
            return APIResponse(200, "生成用户规模成功", [{"style": "0", "content": result_txt}])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/necessity_conclusion", summary="必要性结论")
@api_response
async def necessity_conclusion(request: ProjectInfoRequest):
    """
    生成必要性结论。
    """
    try:
        result_text = docx_search(request.username, request.project_name, '必要性结论')
        if not result_text:  # 如果厂家资料未提供必要性结论，则根据项目背景、项目目标、现状分析、业务需求四个部分要求大模型生成必要性结论
            background_text = docx_search(request.username, request.project_name, '项目背景', format='string')
            target_text = docx_search(request.username, request.project_name, '项目目标', format='string')
            status_text = docx_search(request.username, request.project_name, '现状分析', format='string')
            demand_text = docx_search(request.username, request.project_name, '业务需求', format='string')

            context = ""
            if background_text:
                context += "项目背景：" + background_text + "\n"
            if target_text:
                context += "项目目标：" + target_text + "\n"
            if status_text:
                context += "现状分析：" + status_text + "\n"
            if demand_text:
                context += "业务需求：" + demand_text + "\n"

            if not context:  # 实在找不到参考内容
                return APIResponse(404, "缺乏足够信息生成必要性结论", [{"style": "-5", "content": "缺乏足够信息生成必要性结论"}])

            # 调用大模型生成必要性结论
            result_text = chat_with_llm(generate_necessity_conclusion_prompt + context)
            
            result_txt = result_txt.replace('\n\n', '\n').strip()
            return APIResponse(200, "生成必要性结论成功", [{"style": "0", "content": result_text.replace("\n\n", "\n")}])
        else:
            # 若识别出必要性结论，则进行润色
            result_text = docx_search(request.username, request.project_name, '必要性结论', format='string')
            polished_text = chat_with_llm(polish_necessity_conclusion_prompt + result_text)
            polished_text = polished_text.replace('\n\n', '\n').strip()
            return APIResponse(200, "识别出必要性结论并润色成功", [{"style": "0", "content": polished_text.replace("\n\n", "\n")}])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
