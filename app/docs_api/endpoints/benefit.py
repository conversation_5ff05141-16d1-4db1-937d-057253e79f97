# 本文件用于存放以下接口：
# - 生成管理效益分析 /management_benefit
# - 生成经济效益分析 /economic_benefit
# - 生成社会效益分析 /social_benefit

# 导入相关依赖
from fastapi import APIRouter, HTTPException
from app.utils.doc_processor import docx_search
from app.utils.response import APIResponse, api_response
from app.docs_api.schemas import ProjectInfoRequest
from app.utils.llm import chat_with_llm
from app.templates.prompt_templates import generate_management_benefit_prompt, generate_economic_benefit_prompt, generate_social_benefit_prompt

router = APIRouter()

@router.post("/management_benefit", summary="生成管理效益分析")
@api_response
async def management_benefit(request: ProjectInfoRequest):
    """
    生成给定的管理效益分析。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **management_benefit**: 管理效益分析文本

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        # 首先查找是否已有管理效益分析
        management_benefit = docx_search(request.username, request.project_name, '管理效益分析')
        
        if not management_benefit:  # 如果没有提供管理效益分析，则基于其他部分生成
            # 从项目目标、业务需求、功能需求、技术架构、用户规模等部分获取信息
            target_text = docx_search(request.username, request.project_name, '项目目标', format='string')
            demand_text = docx_search(request.username, request.project_name, '业务需求', format='string')
            feature_text = docx_search(request.username, request.project_name, '功能需求', format='string')
            tech_architecture_text = docx_search(request.username, request.project_name, '技术架构', format='string')
            user_scale_text = docx_search(request.username, request.project_name, '用户规模', format='string')

            context = ""
            if target_text:
                context += "项目目标：" + target_text + "\n"
            if demand_text:
                context += "业务需求：" + demand_text + "\n"
            if feature_text:
                context += "功能需求：" + feature_text + "\n"
            if tech_architecture_text:
                context += "技术架构：" + tech_architecture_text + "\n"
            if user_scale_text:
                context += "用户规模：" + user_scale_text + "\n"

            if not context:  # 如果没有足够的信息生成管理效益分析
                return APIResponse(200, "缺乏足够信息生成管理效益分析", [{"style": "-5", "content": "缺乏足够信息生成管理效益分析"}])

            # 调用大模型生成管理效益分析
            result_text = chat_with_llm(generate_management_benefit_prompt + context)
            result_text = result_text.replace("\n\n", "\n").strip()
            return APIResponse(200, "生成管理效益分析成功", [{"style": "0", "content": result_text}])

        else:
            # 如果已经有管理效益分析，则返回已存在的内容
            return APIResponse(200, "识别管理效益分析成功", management_benefit)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/economic_benefit", summary="生成经济效益分析")
@api_response
async def economic_benefit(request: ProjectInfoRequest):
    """
    生成给定的经济效益分析。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **economic_benefit**: 经济效益分析文本

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        # 首先查找是否已有经济效益分析
        economic_benefit = docx_search(request.username, request.project_name, '经济效益分析')
        
        if not economic_benefit:  # 如果没有提供经济效益分析，则基于其他部分生成
            # 从项目目标、业务需求、功能需求、技术架构、用户规模等部分获取信息
            target_text = docx_search(request.username, request.project_name, '项目目标', format='string')
            demand_text = docx_search(request.username, request.project_name, '业务需求', format='string')
            feature_text = docx_search(request.username, request.project_name, '功能需求', format='string')
            tech_architecture_text = docx_search(request.username, request.project_name, '技术架构', format='string')
            user_scale_text = docx_search(request.username, request.project_name, '用户规模', format='string')

            context = ""
            if target_text:
                context += "项目目标：" + target_text + "\n"
            if demand_text:
                context += "业务需求：" + demand_text + "\n"
            if feature_text:
                context += "功能需求：" + feature_text + "\n"
            if tech_architecture_text:
                context += "技术架构：" + tech_architecture_text + "\n"
            if user_scale_text:
                context += "用户规模：" + user_scale_text + "\n"

            if not context:  # 如果没有足够的信息生成经济效益分析
                return APIResponse(200, "缺乏足够信息生成经济效益分析", [{"style": "-5", "content": "缺乏足够信息生成经济效益分析"}])

            # 调用大模型生成经济效益分析
            result_text = chat_with_llm(generate_economic_benefit_prompt + context)
            result_text = result_text.replace("\n\n", "\n").strip()
            return APIResponse(200, "生成经济效益分析成功", [{"style": "0", "content": result_text}])

        else:
            # 如果已经有经济效益分析，则返回已存在的内容
            return APIResponse(200, "识别经济效益分析成功", economic_benefit)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/social_benefit", summary="生成社会效益分析")
@api_response
async def social_benefit(request: ProjectInfoRequest):
    """
    生成给定的社会效益分析。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **social_benefit**: 社会效益分析文本

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        # 首先查找是否已有社会效益分析
        social_benefit = docx_search(request.username, request.project_name, '社会效益分析')
        
        if not social_benefit:  # 如果没有提供社会效益分析，则基于其他部分生成
            # 从项目目标、业务需求、功能需求、技术架构、用户规模等部分获取信息
            target_text = docx_search(request.username, request.project_name, '项目目标', format='string')
            demand_text = docx_search(request.username, request.project_name, '业务需求', format='string')
            feature_text = docx_search(request.username, request.project_name, '功能需求', format='string')
            tech_architecture_text = docx_search(request.username, request.project_name, '技术架构', format='string')
            user_scale_text = docx_search(request.username, request.project_name, '用户规模', format='string')

            context = ""
            if target_text:
                context += "项目目标：" + target_text + "\n"
            if demand_text:
                context += "业务需求：" + demand_text + "\n"
            if feature_text:
                context += "功能需求：" + feature_text + "\n"
            if tech_architecture_text:
                context += "技术架构：" + tech_architecture_text + "\n"
            if user_scale_text:
                context += "用户规模：" + user_scale_text + "\n"

            if not context:  # 如果没有足够的信息生成社会效益分析
                return APIResponse(200, "缺乏足够信息生成社会效益分析", [{"style": "-5", "content": "缺乏足够信息生成社会效益分析"}])

            # 调用大模型生成社会效益分析
            result_text = chat_with_llm(generate_social_benefit_prompt + context)
            result_text = result_text.replace("\n\n", "\n").strip()
            return APIResponse(200, "生成社会效益分析成功", [{"style": "0", "content": result_text}])

        else:
            # 如果已经有社会效益分析，则返回已存在的内容
            return APIResponse(200, "识别社会效益分析成功", social_benefit)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))