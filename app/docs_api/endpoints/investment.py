# 本文件用于存放以下接口：
# - 生成投资依据说明 /investment_justification
# - 生成总投资 /total_investment
# - 生成资金计划建议 /funding_plan
# 导入相关依赖
import os
import json
from fastapi import APIRouter, HTTPException
from app.core.config import Settings
from app.utils.doc_processor import docx_search, read_config, save_unit_ratio
from app.docs_api.schemas import ProjectInfoRequest
from app.utils.response import APIResponse, api_response
from app.core.logging_config import get_logger

logger = get_logger(__name__)

router = APIRouter()
UPLOAD_DIRECTORY = Settings().UPLOAD_DIRECTORY


def get_config(username:str, project_name:str, table_content: list):
    """
    获取配置文件表格
    Args:
        username (str): 用户名
        project_name (str): 项目名称
        table_content (list): 表格内容
    Returns:
        table_content (list): 填充后的表格内容
    """
    try:
        # 读取config文件
        config = read_config(username, project_name)
        # 判断是否存在并填充默认值
        if 'project_type' not in config:
            config['project_type'] = '应用系统类'
        table_content.append(['项目类型', config['project_type'], '是'])
        if 'project_category' not in config:
            config['project_category'] = '信息系统建设与升级改造'
        table_content.append(['项目分类', config['project_category'], '是'])
        if 'system_type' not in config:
            config['system_type'] = '核心系统'
        table_content.append(['系统类型', config['system_type'], '是'])
        if 'change_stage' not in config:
            config['change_stage'] = '决策阶段'
        table_content.append(['变更阶段', config['change_stage'], '是'])
        if 'construction_nature' not in config:
            config['construction_nature'] = '升级改造'
        table_content.append(['建设性质', config['construction_nature'], '是'])
        if 'administrative' not in config:
            config['administrative'] = '广东'
        table_content.append(['行政区域', config['administrative'], '是'])
        if 'system_security_level' not in config:
            config['system_security_level'] = '二级（含互联网用户）'
        table_content.append(['系统等保级别', config['system_security_level'], '是'])
        if 'system_user_number' not in config:
            config['system_user_number'] = 5600
        table_content.append(['系统用户数量', config['system_user_number'], '是'])
        if 'system_deployment_mode' not in config:
            config['system_deployment_mode'] = '全网统一部署'
        table_content.append(['系统部署方式', config['system_deployment_mode'], '是'])
        if 'construction_cycle' not in config:
            config['construction_cycle'] = '一年内'
        table_content.append(['建设周期T（月）', config['construction_cycle'], '是'])
        if 'geographical_scope' not in config:
            config['geographical_scope'] = '多省'
        table_content.append(['地域范围', config['geographical_scope'], '是'])
        if 'demand_work_factor' not in config:
            config['demand_work_factor'] = '0.11'
        table_content.append(['需求分析工作量系数', config['demand_work_factor'], '是'])
        if 'demand_adjust_factor' not in config:
            config['demand_adjust_factor'] = '0.75'
        table_content.append(['需求分析调节系数', config['demand_adjust_factor'], '是'])
        if 'design_work_factor' not in config:
            config['design_work_factor'] = '0.11'
        table_content.append(['初步设计工作量系数', config['design_work_factor'], '是'])
        if 'design_adjust_factor' not in config:
            config['design_adjust_factor'] = '0.25'
        table_content.append(['设计调节系数', config['design_adjust_factor'], '是'])
        if 'develop_work_factor' not in config:
            config['develop_work_factor'] = '0.78'
        table_content.append(['开发工作量系数', config['develop_work_factor'], '是'])
        if 'intergrate_work_factor' not in config:
            config['intergrate_work_factor'] = '0.78'
        table_content.append(['集成工作量系数', config['intergrate_work_factor'], '是'])
        if 'intergrate_adjust_factor' not in config:
            if config['system_deployment_mode'] == '全网统一部署':
                config['intergrate_adjust_factor'] = '0.75'
            elif config['system_deployment_mode'] == '分省分级部署':
                config['intergrate_adjust_factor'] = '1'
            else:
                config['intergrate_adjust_factor'] = '0.5'
        table_content.append(['集成工作量调节系数', config['intergrate_adjust_factor'], '是'])
        return table_content
    except Exception as e:
        logger.error(f'获取配置文件表格失败：{e}')
        return table_content


def get_ratio(username:str, project_name:str, table_content: list):
    """
    获取建设单位比例
    Args:
        username (str): 用户名
        project_name (str): 项目名称
        table_content (list): 表格内容
    Returns:
        table_content (list): 填充后的表格内容
    """
    try:
        # 读取unit_ratio.json文件
        ratio_path = os.path.join(UPLOAD_DIRECTORY, username, project_name, 'unit_ratio.json')
        if not os.path.exists(ratio_path):
            config = read_config(username, project_name)
            if 'construction_unit' not in config:
                config['construction_unit'] = '广东电网公司'
            unit_list = config['construction_unit'].split('、')
            unit_ratio = save_unit_ratio(username, project_name, unit_list)
        else:
            with open(ratio_path, 'r', encoding='utf-8') as f:
                unit_ratio = json.load(f)
        # 填充表格内容
        for unit, ratio in unit_ratio.items():
            table_content.append([unit + "-开发、集成费分摊比例", ratio, '是'])
        return table_content
    except Exception as e:
        logger.error(f'获取建设单位比例失败：{e}')
        return table_content


def get_others(table_content: list):
    """
    获取其他参数
    Args:
        table_content (list): 表格内容
    Returns:
        table_content (list): 填充后的表格内容
    """
    try:
        table_content.append(['项目管理费率', '2.00%', '是'])
        table_content.append(['项目类型调整系数', '0.80', '是'])
        table_content.append(['要素复杂度因子复杂度级别-内部逻辑文件(ILF)', '10', '是'])
        table_content.append(['要素复杂度因子复杂度级别-外部接口文件(EIF)', '7', '是'])
        table_content.append(['要素复杂度因子复杂度级别-外部输入(EI)', '4', '是'])
        table_content.append(['要素复杂度因子复杂度级别-外部输出(EO)', '5', '是'])
        table_content.append(['要素复杂度因子复杂度级别-外部查询(EQ)', '4', '是'])
        table_content.append(['规模变更因子', '1.39', '是'])
        table_content.append(['功能点调整因子', '1', '是'])
        table_content.append(['通用度调整因子-已有基础，电力行业专有功能', '0.8', '是'])
        table_content.append(['功能点耗时率-综合技术平台类', '18.8', '是'])
        table_content.append(['功能点耗时率-应用系统类', '10.16', '是'])
        table_content.append(['功能点耗时率-应用集成类', '6.9', '是'])
        table_content.append(['功能点耗时率-分析决策类', '10.16', '是'])
        table_content.append(['功能点耗时率-移动应用类', '2.27', '是'])
        return table_content
    except Exception as e:
        logger.error(f'获取其他参数失败：{e}')
        return table_content


@router.post("/investment_justification", summary="生成投资依据说明")
@api_response
async def investment_justification(request: ProjectInfoRequest):
    """
    生成给定的生成投资依据说明。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **table_data**: 表格字典，包含文本列表、表格列表

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        username = request.username
        project_name = request.project_name
        content_list = docx_search(request.username, request.project_name, '投资依据说明')
        if not content_list:
            content_list = [{
                "style": "-5",
                "content": "厂家资料中未识别到投资依据说明，请确认该部分内容！"
            },
            {
                "style": "0",
                "content": "1、根据《南方电网有限责任公司信息化项目预算编制与计算方法（2024年修订版）》，开发费采用功能点估算法进行测算。\n2、人工单价取费原则：A类项目应用广东地区人工单价，开发费、集成费按2602元/人天，实施推广1952元/人天。\n3、开发、集成、需求分析、初步设计费用由试点单位计列，文件评审费用由公司总部统一列支，网一级部署系统的监理费、第三方测试费、安全检测费由公司总部统一列支。\n4、各单位费用分摊比例基于2022年期末固定资产净值进行计列。\n5、计算参数表："
            }]
            # 生成计算参数表内容
            table_content = [["参数名称", "参数值", "是否粘贴"]]
            # 获取config.json文件表格内容
            table_content = get_config(username, project_name, table_content)
            # 获取unit_ratio.json文件表格内容
            table_content = get_ratio(username, project_name, table_content)
            # 获取其他内容
            table_content = get_others(table_content)
            content_list.append({
                "style": "-3",
                "content": table_content
            })
        return APIResponse(200, "生成投资依据说明成功", content_list)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 生成总投资
@router.post("/total_investment", summary="生成总投资")
@api_response
async def total_investment(request: ProjectInfoRequest):
    """
    生成给定的总投资。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **table_data**: 表格字典，包含标题、文本列表、表格列表

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        content_list = docx_search(request.username, request.project_name, '总投资')
        if not content_list:
            return APIResponse(
                200,
                "未找到总投资相关内容",
                [{
                    "style": "0",
                    "content": f"注：1、具体各单位项目投资估算情况详见附件《{request.project_name}建设项目可行性研究估算书》。"
                }]
            )
        content_list = [item for item in content_list if item['style'] != '-3']
        return APIResponse(200, "生成总投资成功", content_list)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 生成资金计划建议
@router.post("/funding_plan", summary="生成资金计划建议")
@api_response
async def funding_plan(request: ProjectInfoRequest):
    """
    生成给定的资金计划建议。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **table_data**: 表格字典，包含标题、文本列表、表格列表

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        content_list = docx_search(request.username, request.project_name, '资金计划建议')
        if not content_list:
            return APIResponse(
                200,
                "未找到资金计划建议相关内容",
                [{
                    "style": "0",
                    "content": "项目资金计划需要根据年度下达投资及项目实际进度及时调整。"
                }]
            )
        content_list = [item for item in content_list if item['style'] != '-3']
        return APIResponse(200, "生成资金计划建议成功", content_list)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

