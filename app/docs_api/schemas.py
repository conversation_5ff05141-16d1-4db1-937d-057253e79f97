from pydantic import BaseModel, Field
from typing import List, Optional


class ProjectInfoRequest(BaseModel):
    # 定义用户项目名称类
    username: str = Field(..., description="用户名")
    project_name: str = Field(..., description="项目名称")


class InvestmentUnit(BaseModel):
    # 定义建设单位类
    username: str = Field(..., description="用户名")
    project_name: str = Field(..., description="项目名称")
    units: List[str] = Field(..., description="投资（建设）单位列表")


class DeploymentMethodRequest(BaseModel):
    # 定义部署模式类
    username: str = Field(..., description="用户名")
    project_name: str = Field(..., description="项目名称")
    deployment_mode: str = Field(..., description="部署模式")


class SystemLevelRequest(BaseModel):
    # 定义信息系统类型
    username: str = Field(..., description="用户名")
    project_name: str = Field(..., description="项目名称")
    system_level: str = Field(..., description="信息系统级别")


class UserscaleRequest(BaseModel):
    # 定义用户规模类
    username: str = Field(..., description="用户名")
    project_name: str = Field(..., description="项目名称")
    user_number: Optional[float] = Field(None, description="用户数量")


class ExcelRequest(BaseModel):
    # 定义表格请求类
    username: str = Field(..., description="用户名")
    project_name: str = Field(..., description="项目名称")
    start_row: Optional[int] = Field(5, description="起始行")
    start_col: Optional[int] = Field(1, description="起始列")
