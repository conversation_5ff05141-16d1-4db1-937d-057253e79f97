from fastapi import APIRouter
from app.docs_api.endpoints import cover, overview, status_and_necessity, investment, benefit, conclusion, proposal, risk, check_word
router = APIRouter()

router.include_router(cover.router, prefix="/cover", tags=["封面"])
router.include_router(overview.router, prefix="/overview", tags=["第一章 概述"])
router.include_router(status_and_necessity.router, prefix="/status_and_necessity", tags=["第二章 项目现状及必要性分析"])
router.include_router(proposal.router, prefix="/proposal", tags=["第三章 项目方案"])
router.include_router(investment.router, prefix="/investment", tags=["第四章 项目投资估算"])
router.include_router(benefit.router, prefix="/benefit", tags=["第五章 项目效益分析"])
router.include_router(risk.router, prefix="/risk", tags=["第六章 项目风险分析"])
router.include_router(conclusion.router, prefix="/conclusion", tags=['第七章 项目可研结论'])
router.include_router(check_word.router, prefix="/check", tags=['检查Word厂家资料'])
