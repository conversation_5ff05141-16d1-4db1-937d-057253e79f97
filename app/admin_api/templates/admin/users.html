{% extends "admin/base.html" %}

{% block title %}用户管理 - 有解助手管理后台{% endblock %}

{% block page_title %}用户管理{% endblock %}

{% block breadcrumb %}用户管理{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">用户列表</h3>
                <div class="card-tools">
                    <div class="input-group input-group-sm" style="width: 150px;">
                        <input type="text" id="userSearchInput" class="form-control float-right" placeholder="搜索...">
                        <div class="input-group-append">
                            <button type="button" class="btn btn-default">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /.card-header -->
            <div class="card-body table-responsive p-0">
                <table class="table table-hover text-nowrap" id="usersTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>姓名</th>
                            <th>邮箱</th>
                            <th>电话</th>
                            <th>部门</th>
                            <th>最后登录</th>
                            <th>管理员</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>{{ user.user_id }}</td>
                            <td>{{ user.username }}</td>
                            <td>{{ user.full_name | default_if_none }}</td>
                            <td>{{ user.email | default_if_none }}</td>
                            <td>{{ user.phone_number | default_if_none }}</td>
                            <td>{{ user.department | default_if_none }}</td>
                            <td>{{ user.last_login_time | timestamp_to_date | default_if_none }}</td>
                            <td>
                                {% if user.is_admin %}
                                <span class="badge bg-success">是</span>
                                {% else %}
                                <span class="badge bg-secondary">否</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-info view-user" data-userid="{{ user.user_id }}">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-warning edit-user" data-userid="{{ user.user_id }}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger delete-user" data-userid="{{ user.user_id }}" data-username="{{ user.username }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="9" class="text-center">暂无用户数据</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <!-- /.card-body -->
            <div class="card-footer clearfix">
                <button type="button" class="btn btn-primary float-right" id="addUserBtn">
                    <i class="fas fa-user-plus"></i> 添加用户
                </button>
            </div>
        </div>
        <!-- /.card -->
    </div>
</div>

<!-- 用户详情模态框 -->
<div class="modal fade" id="userDetailModal" tabindex="-1" role="dialog" aria-labelledby="userDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userDetailModalLabel">用户详情</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="user-detail-content">
                    <p><strong>用户名:</strong> <span id="detail-username"></span></p>
                    <p><strong>姓名:</strong> <span id="detail-fullname"></span></p>
                    <p><strong>邮箱:</strong> <span id="detail-email"></span></p>
                    <p><strong>电话:</strong> <span id="detail-phone"></span></p>
                    <p><strong>部门:</strong> <span id="detail-department"></span></p>
                    <p><strong>最后登录:</strong> <span id="detail-lastlogin"></span></p>
                    <p><strong>是否管理员:</strong> <span id="detail-isadmin"></span></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加用户模态框 -->
<div class="modal fade" id="addUserModal" tabindex="-1" role="dialog" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">添加用户</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="form-group">
                        <label for="add-username">用户名 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="add-username" required>
                    </div>
                    <div class="form-group">
                        <label for="add-password">密码 <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" id="add-password" required>
                    </div>
                    <div class="form-group">
                        <label for="add-fullname">姓名</label>
                        <input type="text" class="form-control" id="add-fullname">
                    </div>
                    <div class="form-group">
                        <label for="add-email">邮箱</label>
                        <input type="email" class="form-control" id="add-email">
                    </div>
                    <div class="form-group">
                        <label for="add-phone">电话</label>
                        <input type="text" class="form-control" id="add-phone">
                    </div>
                    <div class="form-group">
                        <label for="add-department">部门</label>
                        <input type="text" class="form-control" id="add-department">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="submitAddUser">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑用户模态框 -->
<div class="modal fade" id="editUserModal" tabindex="-1" role="dialog" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUserModalLabel">编辑用户</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="edit-userid">
                    <div class="form-group">
                        <label for="edit-username">用户名</label>
                        <input type="text" class="form-control" id="edit-username" readonly>
                    </div>
                    <div class="form-group">
                        <label for="edit-fullname">姓名</label>
                        <input type="text" class="form-control" id="edit-fullname">
                    </div>
                    <div class="form-group">
                        <label for="edit-email">邮箱</label>
                        <input type="email" class="form-control" id="edit-email">
                    </div>
                    <div class="form-group">
                        <label for="edit-phone">电话</label>
                        <input type="text" class="form-control" id="edit-phone">
                    </div>
                    <div class="form-group">
                        <label for="edit-department">部门</label>
                        <input type="text" class="form-control" id="edit-department">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="submitEditUser">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteUserModal" tabindex="-1" role="dialog" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteUserModalLabel">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要删除用户 <strong id="delete-username"></strong> 吗？此操作不可撤销。</p>
                <input type="hidden" id="delete-userid">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteUser">删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(function() {
    // 表格搜索功能
    $("#userSearchInput").on("keyup", function() {
        const value = $(this).val().toLowerCase();
        $("#usersTable tbody tr").filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });
    
    // 查看用户详情
    $(".view-user").on("click", function() {
        const userId = $(this).data("userid");
        const row = $(this).closest("tr");
        
        $("#detail-username").text(row.find("td:eq(1)").text());
        $("#detail-fullname").text(row.find("td:eq(2)").text());
        $("#detail-email").text(row.find("td:eq(3)").text());
        $("#detail-phone").text(row.find("td:eq(4)").text());
        $("#detail-department").text(row.find("td:eq(5)").text());
        $("#detail-lastlogin").text(row.find("td:eq(6)").text());
        $("#detail-isadmin").text(row.find("td:eq(7) .badge").hasClass("bg-success") ? "是" : "否");
        
        $("#userDetailModal").modal("show");
    });
    
    // 添加用户
    $("#addUserBtn").on("click", function() {
        $("#addUserModal").modal("show");
    });
    
    // 提交添加用户
    $("#submitAddUser").on("click", function() {
        // 获取表单值，将空字符串转换为 null
        const getValue = (id) => {
            const val = $(`#${id}`).val();
            return val && val.trim() ? val.trim() : null;
        };

        const userData = {
            username: getValue("add-username"),
            password: getValue("add-password"),
            full_name: getValue("add-fullname"),
            email: getValue("add-email"),
            phone_number: getValue("add-phone"),
            department: getValue("add-department")
        };
        
        // 验证必填字段
        if (!userData.username || !userData.password) {
            toastr.error("用户名和密码为必填项");
            return;
        }
        
        // 发送API请求
        fetch('/admin_api/users/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(userData),
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(err => {
                    throw err;
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.status_code === 201) {
                // 成功添加
                $("#addUserModal").modal("hide");
                // 显示成功提示
                toastr.success("用户添加成功");
                // 延迟刷新页面
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                // 显示错误
                toastr.error("添加用户失败: " + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            if (error.detail) {
                toastr.error("添加用户失败: " + error.detail);
            } else {
                toastr.error("添加用户请求失败");
            }
        });
    });
    
    // 编辑用户
    $(".edit-user").on("click", function() {
        const userId = $(this).data("userid");
        const row = $(this).closest("tr");
        
        $("#edit-userid").val(userId);
        $("#edit-username").val(row.find("td:eq(1)").text());
        $("#edit-fullname").val(row.find("td:eq(2)").text());
        $("#edit-email").val(row.find("td:eq(3)").text());
        $("#edit-phone").val(row.find("td:eq(4)").text());
        $("#edit-department").val(row.find("td:eq(5)").text());
        
        $("#editUserModal").modal("show");
    });
    
    // 提交编辑用户
    $("#submitEditUser").on("click", function() {
        const userId = $("#edit-userid").val();
        
        // Construct the data payload WITHOUT user_id
        const userData = {
            full_name: $("#edit-fullname").val(),
            email: $("#edit-email").val(),
            phone_number: $("#edit-phone").val(),
            department: $("#edit-department").val()
        };
        
        // Construct the URL with user_id in the path
        const updateUrl = `/admin_api/users/${userId}`;
        
        // 发送API请求 - with retry logic
        const maxRetries = 2;
        let retryCount = 0;
        
        function fetchWithRetry() {
            fetch(updateUrl, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(userData),
            })
            .then(response => {
                // Check if response is ok (status 200-299)
                if (response.ok) {
                    return response.json();
                } else if (response.status === 500 && retryCount < maxRetries) {
                    // Retry on 500 error with exponential backoff
                    retryCount++;
                    console.log(`更新请求返回500，正在重试 (${retryCount}/${maxRetries})...`);
                    const backoffDelay = retryCount * 500; // 500ms, 1000ms
                    setTimeout(fetchWithRetry, backoffDelay);
                    return null; // Return null to skip the next then() block
                } else {
                    // If not OK and not retrying, parse the error JSON
                    return response.json().then(errorData => {
                        // Throw an error object containing the parsed data
                        throw { status: response.status, data: errorData }; 
                    });
                }
            })
            .then(data => {
                // Skip if we're retrying
                if (data === null) return;
                
                // This block only runs if response.ok was true
                if (data.status_code === 200) { 
                    // 成功更新
                    $("#editUserModal").modal("hide");
                    toastr.success("用户信息更新成功");
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    // Should not happen if response.ok was true, but handle defensively
                    toastr.error(`更新用户失败: ${data.message || '未知错误'}`);
                }
            })
            .catch(error => {
                // Skip if we're retrying
                if (error === null) return;
                
                console.error('更新请求错误:', error);
                $("#editUserModal").modal("hide");
                // Handle thrown error object or network error
                let errorMsg = "更新用户请求失败，请检查网络连接";
                if (error.status && error.data) {
                    // Use detail for 422 errors, fallback to message
                    errorMsg = `更新用户失败 (状态 ${error.status}): ${error.data.detail || error.data.message || '无法处理的请求'}`;
                }
                toastr.error(errorMsg);
            });
        }
        
        // Start the fetch with retry logic
        fetchWithRetry();
    });
    
    // 删除用户
    $(".delete-user").on("click", function() {
        const userId = $(this).data("userid");
        const username = $(this).data("username");
        
        $("#delete-userid").val(userId);
        $("#delete-username").text(username);
        
        $("#deleteUserModal").modal("show");
    });
    
    // 确认删除用户
    $("#confirmDeleteUser").on("click", function() {
        const userId = $("#delete-userid").val();
        const username = $("#delete-username").text();
        
        // 显示加载中提示
        $("#confirmDeleteUser").prop("disabled", true).html('<i class="fas fa-spinner fa-spin"></i> 删除中...');
        
        // 发送API请求 - 使用通过用户名删除API
        fetch(`/admin_api/users/by-username/${username}`, {
            method: 'DELETE',
            headers: {
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            // 无论成功与否，先隐藏模态框
            $("#deleteUserModal").modal("hide");
            
            // 恢复按钮状态
            $("#confirmDeleteUser").prop("disabled", false).html('删除');
            
            // 打印详细日志用于调试
            console.log("删除用户API响应:", data);
            
            if (data.status_code === 200) {
                // 成功删除，使用Toastr显示成功消息
                toastr.success(`用户 ${username} 已成功删除`);
                // 延迟刷新页面
                setTimeout(() => {
                    location.reload();
                }, 2000);
            } else {
                // 显示错误提示，使用API返回的原始消息
                toastr.error(`删除用户失败: ${data.message || '未知错误'}`);
            }
        })
        .catch(error => {
            console.error('删除请求错误:', error);
            $("#deleteUserModal").modal("hide");
            $("#confirmDeleteUser").prop("disabled", false).html('删除');
            // 显示网络错误
            toastr.error("删除用户请求失败，请检查网络连接");
        });
    });
});
</script>
{% endblock %} 