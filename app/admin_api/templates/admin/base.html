<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 防止缓存 -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>{% block title %}有解助手管理后台{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="/static/admin/css/bootstrap.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="/static/admin/css/all.min.css">
    <!-- Roboto Font -->
    <link rel="stylesheet" href="/static/admin/css/google-fonts-roboto.css">
    <!-- Toastr -->
    <link rel="stylesheet" href="/static/admin/css/toastr.min.css">
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="/static/admin/css/sweetalert2.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="/static/admin/css/adminlte.min.css">
    <!-- Custom admin CSS -->
    <link href="/static/admin/css/admin.css?v={{ now }}" rel="stylesheet">
    {% block extra_css %}{% endblock %}
    
    <style>
        /* Source Sans Pro 字体的基本样式 */
        @font-face {
            font-family: 'Source Sans Pro';
            font-style: normal;
            font-weight: 300;
            src: local('Source Sans Pro Light'), local('SourceSansPro-Light'),
                 url('/static/admin/webfonts/source-sans-pro-light.woff2') format('woff2');
        }
        @font-face {
            font-family: 'Source Sans Pro';
            font-style: normal;
            font-weight: 400;
            src: local('Source Sans Pro Regular'), local('SourceSansPro-Regular'),
                 url('/static/admin/webfonts/source-sans-pro-regular.woff2') format('woff2');
        }
        @font-face {
            font-family: 'Source Sans Pro';
            font-style: italic;
            font-weight: 400;
            src: local('Source Sans Pro Italic'), local('SourceSansPro-Italic'),
                 url('/static/admin/webfonts/source-sans-pro-italic.woff2') format('woff2');
        }
        @font-face {
            font-family: 'Source Sans Pro';
            font-style: normal;
            font-weight: 700;
            src: local('Source Sans Pro Bold'), local('SourceSansPro-Bold'),
                 url('/static/admin/webfonts/source-sans-pro-bold.woff2') format('woff2');
        }
    </style>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
    <div class="wrapper">
        <!-- Navbar -->
        <nav class="main-header navbar navbar-expand navbar-white navbar-light">
            <!-- Left navbar links -->
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
                </li>
                <li class="nav-item d-none d-sm-inline-block">
                    <a href="/admin/dashboard" class="nav-link">仪表盘</a>
                </li>
            </ul>

            <!-- Right navbar links -->
            <ul class="navbar-nav ml-auto">
                <li class="nav-item dropdown">
                    <a class="nav-link" data-toggle="dropdown" href="#">
                        <i class="far fa-user"></i> <span id="admin-name">管理员</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right">
                        <a href="#" class="dropdown-item" id="logout-btn">
                            <i class="fas fa-sign-out-alt mr-2"></i> 退出登录
                        </a>
                    </div>
                </li>
            </ul>
        </nav>
        <!-- /.navbar -->

        <!-- Main Sidebar Container -->
        <aside class="main-sidebar sidebar-dark-primary elevation-4">
            <!-- Brand Logo -->
            <a href="/admin/dashboard" class="brand-link">
                <span class="brand-text font-weight-light ml-3">有解助手管理系统</span>
            </a>

            <!-- Sidebar -->
            <div class="sidebar">
                <!-- Sidebar Menu -->
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                        <li class="nav-item">
                            <a href="/admin/dashboard" class="nav-link {% if active_page == 'dashboard' %}active{% endif %}">
                                <i class="nav-icon fas fa-chart-bar"></i>
                                <p>仪表盘</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/admin/users" class="nav-link {% if active_page == 'users' %}active{% endif %}">
                                <i class="nav-icon fas fa-users"></i>
                                <p>用户管理</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/admin/projects" class="nav-link {% if active_page == 'projects' %}active{% endif %}">
                                <i class="nav-icon fas fa-folder"></i>
                                <p>项目管理</p>
                            </a>
                        </li>
                    </ul>
                </nav>
                <!-- /.sidebar-menu -->
            </div>
            <!-- /.sidebar -->
        </aside>

        <!-- Content Wrapper. Contains page content -->
        <div class="content-wrapper">
            <!-- Content Header (Page header) -->
            <div class="content-header">
                <div class="container-fluid">
                    <div class="row mb-2">
                        <div class="col-sm-6">
                            <h1 class="m-0">{% block page_title %}{% endblock %}</h1>
                        </div>
                        <div class="col-sm-6">
                            <ol class="breadcrumb float-sm-right">
                                <li class="breadcrumb-item"><a href="/admin/dashboard">首页</a></li>
                                <li class="breadcrumb-item active">{% block breadcrumb %}{% endblock %}</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /.content-header -->

            <!-- Main content -->
            <section class="content">
                <div class="container-fluid">
                    <!-- Alert messages -->
                    {% if message %}
                    <div class="alert alert-{{ message_type|default('info') }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    {% endif %}

                    {% if error_message %}
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {{ error_message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    {% endif %}

                    <!-- Main content block -->
                    {% block content %}{% endblock %}
                </div>
            </section>
            <!-- /.content -->
        </div>
        <!-- /.content-wrapper -->

        <!-- Footer -->
        <footer class="main-footer">
            <div class="float-right d-none d-sm-block">
                <b>Version</b> 1.0.0
            </div>
            <strong>Copyright &copy; 2025 有解助手.</strong> All rights reserved.
        </footer>
    </div>
    <!-- ./wrapper -->

    <!-- jQuery -->
    <script src="/static/admin/js/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="/static/admin/js/bootstrap.bundle.min.js"></script>
    <!-- Toastr -->
    <script src="/static/admin/js/toastr.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="/static/admin/js/sweetalert2.all.min.js"></script>
    <!-- AdminLTE App -->
    <script src="/static/admin/js/adminlte.min.js"></script>
    
    <script>
        // 检查jQuery是否正确加载
        if (typeof jQuery === 'undefined') {
            console.error('jQuery 没有加载! 请检查本地jQuery文件是否存在。');
        } else {
            console.log('jQuery版本:', jQuery.fn.jquery);
        }
        
        // 从localStorage中获取管理员信息并显示
        document.addEventListener("DOMContentLoaded", function() {
            const adminName = localStorage.getItem('admin_full_name') || localStorage.getItem('admin_username') || '管理员';
            document.getElementById('admin-name').textContent = adminName;
            
            // 退出登录处理
            document.getElementById('logout-btn').addEventListener('click', function(e) {
                e.preventDefault();
                // 清除本地存储中的令牌和用户信息
                localStorage.removeItem('admin_token');
                localStorage.removeItem('admin_username');
                localStorage.removeItem('admin_full_name');
                
                // 清除cookie中的令牌
                document.cookie = "admin_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
                
                // 重定向到登录页面
                window.location.href = '/admin/login';
            });
            
            // 初始化Bootstrap组件
            try {
                // 初始化工具提示
                $('[data-toggle="tooltip"]').tooltip();
                
                // 初始化所有模态框
                $('.modal').modal({show: false});
                
                console.log('Bootstrap组件初始化成功');
            } catch(e) {
                console.error('Bootstrap组件初始化失败:', e);
            }
        });

        // Toastr配置
        if (typeof toastr !== 'undefined') {
            toastr.options = {
                "closeButton": true,
                "progressBar": true,
                "positionClass": "toast-top-right",
                "showDuration": "300",
                "hideDuration": "1000",
                "timeOut": "5000",
                "extendedTimeOut": "2000",
                "showEasing": "swing",
                "hideEasing": "linear",
                "showMethod": "fadeIn",
                "hideMethod": "fadeOut",
                "tapToDismiss": true,
                // 加宽通知
                "toastClass": "toast custom-wide-toast"
            };
            
            // 添加自定义CSS样式，增加通知宽度
            const style = document.createElement('style');
            style.innerHTML = `
                .custom-wide-toast {
                    width: 400px !important;
                    max-width: 400px !important;
                }
                .toast-message {
                    font-size: 14px;
                    word-wrap: break-word;
                }
            `;
            document.head.appendChild(style);
        }
    </script>
    
    <!-- Custom admin JS (最后加载，防止与其他库冲突) -->
    <script src="/static/admin/js/admin.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
