<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 简化版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .login-title {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
            font-size: 24px;
            font-weight: 500;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .login-btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .login-btn:hover {
            background: #5a6fd8;
        }
        
        .login-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .message {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1 class="login-title">管理员登录</h1>
        
        <form id="loginForm">
            <div class="form-group">
                <label class="form-label" for="username">用户名</label>
                <input type="text" id="username" class="form-input" required>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="password">密码</label>
                <input type="password" id="password" class="form-input" required>
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">登录</button>
        </form>
        
        <div id="message"></div>
    </div>

    <script>
        // 显示消息
        function showMessage(text, type = 'info') {
            const messageDiv = document.getElementById('message');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = text;
            messageDiv.style.display = 'block';
        }
        
        // 清除消息
        function clearMessage() {
            const messageDiv = document.getElementById('message');
            messageDiv.style.display = 'none';
        }
        
        // 设置按钮状态
        function setButtonState(disabled, text) {
            const btn = document.getElementById('loginBtn');
            btn.disabled = disabled;
            btn.textContent = text;
        }
        
        // 登录函数
        function login(username, password) {
            console.log('开始登录:', username);
            
            setButtonState(true, '登录中...');
            showMessage('正在登录，请稍候...', 'info');
            
            // 创建请求
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/admin_api/simple_login', true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    setButtonState(false, '登录');
                    
                    console.log('响应状态:', xhr.status);
                    console.log('响应内容:', xhr.responseText);
                    
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            console.log('解析响应:', response);
                            
                            if (response.success) {
                                showMessage('登录成功，正在跳转...', 'success');
                                
                                // 保存token
                                if (response.data && response.data.access_token) {
                                    localStorage.setItem('admin_token', response.data.access_token);
                                    localStorage.setItem('admin_username', response.data.username);
                                    localStorage.setItem('admin_full_name', response.data.full_name);
                                    
                                    // 跳转到管理界面
                                    setTimeout(() => {
                                        window.location.href = '/admin/redirect';
                                    }, 1000);
                                } else {
                                    showMessage('登录响应格式错误', 'error');
                                }
                            } else {
                                showMessage(response.message || '登录失败', 'error');
                            }
                        } catch (e) {
                            console.error('解析响应失败:', e);
                            showMessage('响应解析失败', 'error');
                        }
                    } else {
                        showMessage(`请求失败 (${xhr.status})`, 'error');
                    }
                }
            };
            
            xhr.onerror = function() {
                console.error('网络错误');
                setButtonState(false, '登录');
                showMessage('网络连接错误', 'error');
            };
            
            // 发送请求
            const data = JSON.stringify({
                username: username,
                password: password
            });
            
            console.log('发送数据:', data);
            xhr.send(data);
        }
        
        // 表单提交处理
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            clearMessage();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            
            if (!username || !password) {
                showMessage('请输入用户名和密码', 'error');
                return;
            }
            
            login(username, password);
        });
        
        // 页面加载完成
        console.log('简化登录页面加载完成');
    </script>
</body>
</html>
