{% extends "admin/base.html" %}

{% block title %}仪表盘 - 有解助手管理后台{% endblock %}

{% block page_title %}仪表盘{% endblock %}

{% block breadcrumb %}仪表盘{% endblock %}

{% block content %}
<!-- 统计信息卡片 -->
<div class="row">
    <div class="col-lg-3 col-6">
        <!-- 用户数统计 -->
        <div class="small-box bg-info">
            <div class="inner">
                <h3>{{ user_count }}</h3>
                <p>注册用户</p>
            </div>
            <div class="icon">
                <i class="fas fa-users"></i>
            </div>
            <a href="/admin/users" class="small-box-footer">
                更多信息 <i class="fas fa-arrow-circle-right"></i>
            </a>
        </div>
    </div>
    <!-- ./col -->
    <div class="col-lg-3 col-6">
        <!-- 项目数统计 -->
        <div class="small-box bg-success">
            <div class="inner">
                <h3>{{ project_count }}</h3>
                <p>总项目数</p>
            </div>
            <div class="icon">
                <i class="fas fa-folder"></i>
            </div>
            <a href="/admin/projects" class="small-box-footer">
                更多信息 <i class="fas fa-arrow-circle-right"></i>
            </a>
        </div>
    </div>
    <!-- ./col -->
    <div class="col-lg-3 col-6">
        <!-- 使用存储统计 -->
        <div class="small-box bg-warning">
            <div class="inner">
                <h3>{{ (total_storage / (1024*1024)) | round(2) }} MB</h3>
                <p>存储使用量</p>
            </div>
            <div class="icon">
                <i class="fas fa-hdd"></i>
            </div>
            <a href="#" class="small-box-footer">
                详情 <i class="fas fa-arrow-circle-right"></i>
            </a>
        </div>
    </div>
    <!-- ./col -->
</div>
<!-- /.row -->

<!-- 主要内容卡片 -->
<div class="row">
    <!-- 活跃用户列表 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-user-clock mr-1"></i>
                    活跃用户
                </h3>
            </div>
            <!-- /.card-header -->
            <div class="card-body p-0">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>用户名</th>
                            <th>姓名</th>
                            <th>最后登录</th>
                            <th>项目数</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in top_users %}
                        <tr>
                            <td>{{ user.username }}</td>
                            <td>{{ user.full_name | default_if_none }}</td>
                            <td>{{ user.last_login_time | default_if_none }}</td>
                            <td>{{ user.project_count }}</td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="4" class="text-center">暂无数据</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <!-- /.card-body -->
        </div>
        <!-- /.card -->
    </div>
    <!-- /.col -->

    <!-- 最近项目列表 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-folder-open mr-1"></i>
                    最近创建的项目
                </h3>
            </div>
            <!-- /.card-header -->
            <div class="card-body p-0">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>用户名</th>
                            <th>项目名称</th>
                            <th>创建时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for project in recent_projects %}
                        <tr>
                            <td>{{ project.username }}</td>
                            <td>{{ project.project_name }}</td>
                            <td>{{ project.created_time }}</td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="3" class="text-center">暂无数据</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <!-- /.card-body -->
        </div>
        <!-- /.card -->
    </div>
    <!-- /.col -->
</div>
<!-- /.row -->

{% endblock %}

{% block extra_js %}
<script>
    // 仪表盘特定的JavaScript
    $(function() {
        // 这里可以添加仪表盘特定的JavaScript逻辑

        // 最近创建的项目时间戳格式化
        $('div.card:has(h3.card-title:contains("最近创建的项目")) table tbody tr').each(function() {
            var $td = $(this).find('td:nth-child(3)');
            var timestamp = parseInt($td.text());
            if (!isNaN(timestamp)) {
                var date = new Date(timestamp * 1000); // JS中时间戳是毫秒
                var Y = date.getFullYear();
                var M = (date.getMonth() + 1).toString().padStart(2, '0');
                var D = date.getDate().toString().padStart(2, '0');
                var h = date.getHours().toString().padStart(2, '0');
                var m = date.getMinutes().toString().padStart(2, '0');
                var s = date.getSeconds().toString().padStart(2, '0');
                var formatted = Y + '-' + M + '-' + D + ' ' + h + ':' + m + ':' + s;
                $td.text(formatted);
            }
        });
    });
</script>
{% endblock %}
