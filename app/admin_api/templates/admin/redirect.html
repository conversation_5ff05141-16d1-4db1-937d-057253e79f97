<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>重定向中...</title>
    <!-- <PERSON><PERSON> Font -->
    <link rel="stylesheet" href="/static/admin/css/google-fonts-roboto.css">
    <style>
        /* Source Sans Pro 字体的基本样式 */
        @font-face {
            font-family: 'Source Sans Pro';
            font-style: normal;
            font-weight: 300;
            src: local('Source Sans Pro Light'), local('SourceSansPro-Light'),
                 url('/static/admin/webfonts/source-sans-pro-light.woff2') format('woff2');
        }
        @font-face {
            font-family: 'Source Sans Pro';
            font-style: normal;
            font-weight: 400;
            src: local('Source Sans Pro Regular'), local('SourceSansPro-Regular'),
                 url('/static/admin/webfonts/source-sans-pro-regular.woff2') format('woff2');
        }
        @font-face {
            font-family: 'Source Sans Pro';
            font-style: italic;
            font-weight: 400;
            src: local('Source Sans Pro Italic'), local('SourceSansPro-Italic'),
                 url('/static/admin/webfonts/source-sans-pro-italic.woff2') format('woff2');
        }
        @font-face {
            font-family: 'Source Sans Pro';
            font-style: normal;
            font-weight: 700;
            src: local('Source Sans Pro Bold'), local('SourceSansPro-Bold'),
                 url('/static/admin/webfonts/source-sans-pro-bold.woff2') format('woff2');
        }
        
        body {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            background-color: #f5f5f5;
            margin: 0;
            font-family: 'Source Sans Pro', sans-serif;
        }
        .redirect-container {
            text-align: center;
            padding: 20px;
        }
        .spinner {
            width: 40px;
            height: 40px;
            margin: 20px auto;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4e73df;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="redirect-container">
        <h2>登录成功</h2>
        <p>正在跳转到管理后台...</p>
        <div class="spinner"></div>
        <p id="manual-link" style="display: none;">
            如果没有自动跳转，请 <a href="/admin/dashboard">点击这里</a>
        </p>
    </div>

    <script>
        // 在页面加载完成后立即执行
        document.addEventListener('DOMContentLoaded', function() {
            // 显示手动链接
            setTimeout(function() {
                document.getElementById('manual-link').style.display = 'block';
            }, 2000); // Keep manual link as a fallback

            // 检查令牌
            const token = localStorage.getItem('admin_token') || getCookie('admin_token');
            console.log("准备跳转，令牌状态:", !!token);

            // Perform a single, reliable redirect
            window.location.replace('/admin/dashboard');
        });

        // 获取Cookie值
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }
    </script>
</body>
</html> 