<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>有解助手 - 管理员登录</title>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="/static/admin/css/all.min.css">
    <!-- <PERSON><PERSON> Font -->
    <link rel="stylesheet" href="/static/admin/css/google-fonts-roboto.css">
    <!-- Bootstrap 4 CSS -->
    <link rel="stylesheet" href="/static/admin/css/bootstrap.min.css">
    
    <style>
        /* Source Sans Pro 字体的基本样式 */
        @font-face {
            font-family: 'Source Sans Pro';
            font-style: normal;
            font-weight: 300;
            src: local('Source Sans Pro Light'), local('SourceSansPro-Light'),
                 url('/static/admin/webfonts/source-sans-pro-light.woff2') format('woff2');
        }
        @font-face {
            font-family: 'Source Sans Pro';
            font-style: normal;
            font-weight: 400;
            src: local('Source Sans Pro Regular'), local('SourceSansPro-Regular'),
                 url('/static/admin/webfonts/source-sans-pro-regular.woff2') format('woff2');
        }
        @font-face {
            font-family: 'Source Sans Pro';
            font-style: italic;
            font-weight: 400;
            src: local('Source Sans Pro Italic'), local('SourceSansPro-Italic'),
                 url('/static/admin/webfonts/source-sans-pro-italic.woff2') format('woff2');
        }
        @font-face {
            font-family: 'Source Sans Pro';
            font-style: normal;
            font-weight: 700;
            src: local('Source Sans Pro Bold'), local('SourceSansPro-Bold'),
                 url('/static/admin/webfonts/source-sans-pro-bold.woff2') format('woff2');
        }
        
        body {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5;
            background-image: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            margin: 0;
            padding: 0;
        }
        .login-container {
            max-width: 400px;
            width: 90%;
            padding: 30px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .login-logo {
            text-align: center;
            margin-bottom: 25px;
        }
        .login-logo h2 {
            color: #4e73df;
            font-weight: 600;
        }
        .login-form {
            margin-top: 20px;
            width: 100%;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-control {
            width: 100%;
            padding: 12px 15px;
            font-size: 14px;
            line-height: 1.5;
            color: #495057;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #ced4da;
            border-radius: 4px;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            box-sizing: border-box;
        }
        .form-control:focus {
            color: #495057;
            background-color: #fff;
            border-color: #4e73df;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        .btn-login {
            width: 100%;
            padding: 12px 15px;
            background-color: #4e73df;
            border-color: #4e73df;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            border: 1px solid #4e73df;
            cursor: pointer;
            transition: all 0.15s ease-in-out;
            box-sizing: border-box;
        }
        .btn-login:hover {
            background-color: #2e59d9;
            border-color: #2e59d9;
        }
        .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        .form-check-input {
            margin-right: 8px;
            margin-top: 0;
        }
        .form-check-label {
            margin-bottom: 0;
            font-weight: normal;
        }
        .alert {
            margin-top: 15px;
        }

        /* 确保在小屏幕上也能正确显示 */
        @media (max-width: 576px) {
            .login-container {
                width: 95%;
                padding: 20px;
                margin: 20px 0;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
            <div class="login-logo">
                <h2>有解助手</h2>
                <p>管理系统</p>
            </div>
            
            {% if error_message %}
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ error_message }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            {% endif %}

            <!-- 动态错误提示容器 -->
            <div id="login-alerts"></div>

            <form class="login-form" id="loginForm" method="post" action="/admin/login">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" class="form-control" id="username" name="username" placeholder="请输入用户名" required>
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" class="form-control" id="password" name="password" placeholder="请输入密码" required>
                </div>
                <div class="form-group form-check">
                    <input type="checkbox" class="form-check-input" id="rememberMe">
                    <label class="form-check-label" for="rememberMe">记住我</label>
                </div>
                <button class="btn btn-primary btn-login" type="submit">
                    <i class="fas fa-sign-in-alt mr-2"></i>登录
                </button>
            </form>
    </div>

    <!-- jQuery -->
    <script src="/static/admin/js/jquery.min.js"></script>
    <!-- Bootstrap 4 JS Bundle -->
    <script src="/static/admin/js/bootstrap.bundle.min.js"></script>
    <script>
        // 检测是否支持fetch API
        function supportsFetch() {
            return 'fetch' in window;
        }

        // 使用XMLHttpRequest的备用登录方法（带重试机制）
        function loginWithXHR(username, password, alertContainer, retryCount = 0) {
            const maxRetries = 2; // 最多重试2次
            const xhr = new XMLHttpRequest();
            // 使用调试端点，因为它工作正常
            xhr.open('POST', '/admin_api/admin_login_debug', true);

            // 设置更兼容内网环境的请求头
            xhr.setRequestHeader('Content-Type', 'application/json; charset=utf-8');
            xhr.setRequestHeader('Accept', 'application/json');
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');

            // 设置超时时间（30秒）
            xhr.timeout = 30000;

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    // 移除加载提示
                    alertContainer.innerHTML = '';

                    if (xhr.status === 200) {
                        try {
                            const data = JSON.parse(xhr.responseText);
                            console.log("XHR登录成功，API响应:", data);

                            if (data.code === 200) {
                                // 显示成功提示
                                const successAlert = document.createElement('div');
                                successAlert.className = 'alert alert-success';
                                successAlert.setAttribute('role', 'alert');
                                successAlert.innerHTML = '登录成功，正在跳转...';
                                alertContainer.appendChild(successAlert);

                                // 存储token和用户信息
                                document.cookie = `admin_token=${data.data.access_token}; path=/; max-age=604800`;
                                localStorage.setItem('admin_token', data.data.access_token);
                                localStorage.setItem('admin_username', data.data.username);
                                localStorage.setItem('admin_full_name', data.data.full_name || data.data.username);

                                // 跳转
                                setTimeout(function() {
                                    window.location.href = '/admin/redirect';
                                }, 800);
                            } else {
                                showError(alertContainer, data.message || '登录失败，请检查用户名和密码');
                            }
                        } catch (parseError) {
                            console.error('XHR JSON解析错误:', parseError);
                            showError(alertContainer, 'XHR响应解析失败: ' + parseError.message);
                        }
                    } else {
                        console.error('XHR请求失败:', {
                            status: xhr.status,
                            statusText: xhr.statusText,
                            readyState: xhr.readyState,
                            responseText: xhr.responseText,
                            responseURL: xhr.responseURL
                        });

                        // 如果是连接错误且还有重试次数，则重试
                        if (xhr.status === 0 && retryCount < maxRetries) {
                            console.log(`连接失败，正在重试 (${retryCount + 1}/${maxRetries})...`);
                            setTimeout(() => {
                                loginWithXHR(username, password, alertContainer, retryCount + 1);
                            }, 1000 * (retryCount + 1)); // 递增延迟
                            return;
                        }

                        let errorMsg = `XHR请求失败: ${xhr.status}`;
                        if (xhr.status === 0) {
                            errorMsg = `网络连接错误：请求被中断或服务器无响应 (已重试${retryCount}次)`;
                        } else if (xhr.statusText) {
                            errorMsg += ` ${xhr.statusText}`;
                        }

                        showError(alertContainer, errorMsg);
                    }
                }
            };

            xhr.onerror = function() {
                console.error('XHR网络错误:', {
                    readyState: xhr.readyState,
                    status: xhr.status,
                    statusText: xhr.statusText,
                    retryCount: retryCount
                });

                // 网络错误也尝试重试
                if (retryCount < maxRetries) {
                    console.log(`网络错误，正在重试 (${retryCount + 1}/${maxRetries})...`);
                    setTimeout(() => {
                        loginWithXHR(username, password, alertContainer, retryCount + 1);
                    }, 1000 * (retryCount + 1));
                    return;
                }

                alertContainer.innerHTML = '';
                showError(alertContainer, `网络连接错误：无法连接到服务器 (已重试${retryCount}次)，请检查网络连接或联系管理员`);
            };

            xhr.ontimeout = function() {
                alertContainer.innerHTML = '';
                console.error('XHR请求超时');
                showError(alertContainer, '请求超时：服务器响应时间过长，请稍后重试');
            };

            const requestData = JSON.stringify({
                username: username,
                password: password
            });

            console.log('发送登录请求到调试端点:', requestData);
            xhr.send(requestData);
        }

        // 显示错误信息的通用函数
        function showError(alertContainer, message) {
            const errorAlert = document.createElement('div');
            errorAlert.className = 'alert alert-danger alert-dismissible fade show';
            errorAlert.setAttribute('role', 'alert');
            errorAlert.innerHTML = `
                ${message}
                <br><small>如果问题持续存在，请联系系统管理员</small>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            `;
            alertContainer.appendChild(errorAlert);
        }

        // 测试基本连接
        function testConnection() {
            console.log('测试基本连接');
            const xhr = new XMLHttpRequest();
            xhr.open('GET', '/admin_api/test_connection', true);

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    console.log('连接测试响应:', {
                        status: xhr.status,
                        responseText: xhr.responseText
                    });
                }
            };

            xhr.send();
        }

        // 测试调试端点
        function testDebugEndpoint() {
            console.log('测试调试端点');
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/admin_api/admin_login_debug', true);
            xhr.setRequestHeader('Content-Type', 'application/json');

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    console.log('调试端点响应:', {
                        status: xhr.status,
                        responseText: xhr.responseText
                    });
                }
            };

            xhr.send(JSON.stringify({test: 'debug'}));
        }

        // 备用的表单提交方式（传统POST）
        function fallbackFormSubmit() {
            console.log('使用备用表单提交方式');
            const form = document.getElementById('loginForm');
            form.action = '/admin_api/admin_login';
            form.method = 'POST';
            form.submit();
        }

        // 登录表单提交处理
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            // 显示加载提示
            const loadingAlert = document.createElement('div');
            loadingAlert.className = 'alert alert-info';
            loadingAlert.setAttribute('role', 'alert');
            loadingAlert.innerHTML = '正在登录，请稍候...';

            const alertContainer = document.getElementById('login-alerts');
            alertContainer.innerHTML = '';  // 清除之前的提示
            alertContainer.appendChild(loadingAlert);

            // 使用调试端点进行登录请求（因为原端点有问题）
            console.log('使用调试端点进行登录请求');
            loginWithXHR(username, password, alertContainer);
        });
    </script>
</body>
</html>
