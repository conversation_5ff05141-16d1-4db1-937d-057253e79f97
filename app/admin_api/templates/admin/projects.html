{% extends "admin/base.html" %}

{% block title %}项目管理 - 有解助手管理后台{% endblock %}

{% block page_title %}项目管理{% endblock %}

{% block breadcrumb %}项目管理{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">项目列表</h3>
                <div class="card-tools">
                    <div class="input-group input-group-sm" style="width: 150px;">
                        <input type="text" id="projectSearchInput" class="form-control float-right" placeholder="搜索...">
                        <div class="input-group-append">
                            <button type="button" class="btn btn-default">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /.card-header -->
            <div class="card-body table-responsive p-0">
                <table class="table table-hover text-nowrap" id="projectsTable">
                    <thead>
                        <tr>
                            <th>用户名</th>
                            <th>姓名</th>
                            <th>项目名称</th>
                            <th>创建时间</th>
                            <th>大小</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% set has_projects = false %}
                        {% for user_projects in user_projects_list %}
                            {% for project in user_projects.projects %}
                                {% set has_projects = true %}
                                <tr>
                                    <td>{{ user_projects.username }}</td>
                                    <td>{{ user_projects.full_name | default_if_none('N/A') }}</td>
                                    <td>{{ project.project_name }}</td>
                                    <td>{{ project.created_time | timestamp_to_date }}</td>
                                    <td>{{ project.size_bytes | default(0) | filesizeformat }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-info view-project-btn" 
                                                    data-username="{{ user_projects.username }}" 
                                                    data-project-name="{{ project.project_name }}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger delete-project-btn" 
                                                    data-username="{{ user_projects.username }}" 
                                                    data-project-name="{{ project.project_name }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        {% endfor %}
                        
                        {% if not has_projects %}
                        <tr>
                            <td colspan="6" class="text-center">暂无项目数据</td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
            <!-- /.card-body -->
        </div>
        <!-- /.card -->
    </div>
</div>

<!-- 项目统计卡片 -->
<div class="row">
    {% for user_projects in user_projects_list %}
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-user mr-1"></i>
                    {{ user_projects.username }}
                </h3>
                <div class="card-tools">
                    <span class="badge bg-primary">{{ user_projects.project_count }} 个项目</span>
                </div>
            </div>
            <div class="card-body">
                <p>总存储: {{ user_projects.total_size_bytes | default(0) | filesizeformat }}</p>
                <ul class="list-group list-group-flush">
                    {% for project in user_projects.projects %}
                    <li class="list-group-item px-0">
                        {{ project.project_name }} 
                        <span class="float-right">{{ project.size_bytes | default(0) | filesizeformat }}</span>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- 项目详情模态框 -->
<div class="modal fade" id="projectDetailsModal" tabindex="-1" role="dialog" aria-labelledby="projectDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="projectDetailsModalLabel">项目详情</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="project-details-content">
                    <p><strong>项目名称:</strong> <span id="projectName"></span></p>
                    <p><strong>用户名:</strong> <span id="projectOwner"></span></p>
                    <p><strong>创建时间:</strong> <span id="projectCreated"></span></p>
                    <p><strong>状态:</strong> <span id="projectStatus"></span></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteProjectModal" tabindex="-1" role="dialog" aria-labelledby="deleteProjectModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteProjectModalLabel">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要删除用户 <strong id="delete-username"></strong> 的项目 <strong id="delete-projectname"></strong> 吗？此操作不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteProject">删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 检查jQuery是否正确加载
if (typeof jQuery === 'undefined') {
    console.error('jQuery 没有加载!');
    setTimeout(function() {
        alert('系统错误：页面功能可能无法正常工作，请刷新页面或联系管理员。');
    }, 2000);
} else {
    console.log('jQuery版本:', jQuery.fn.jquery);
}

$(function() {
    // 添加调试信息，确认脚本已加载
    console.log("项目管理页面脚本已加载");
    
    // 检查模态框元素是否存在
    if($("#projectDetailsModal").length) {
        console.log("项目详情模态框元素存在");
    } else {
        console.error("项目详情模态框元素不存在!");
    }
    
    if($("#deleteProjectModal").length) {
        console.log("删除确认模态框元素存在");
    } else {
        console.error("删除确认模态框元素不存在!");
    }
    
    // 确保jQuery和Bootstrap可用，如果不可用，提供备用方法
    var hasBootstrap = (typeof $.fn !== 'undefined' && typeof $.fn.modal !== 'undefined');
    
    if (!hasBootstrap) {
        console.warn('Bootstrap modal 功能不可用，将使用备用方法');
    }
    
    // 项目表格搜索功能
    $("#projectSearchInput").on("keyup", function() {
        const value = $(this).val().toLowerCase();
        $("#projectsTable tbody tr").filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });
    
    // 确保模态框已初始化
    try {
        if (hasBootstrap) {
            $("#projectDetailsModal").modal({show: false});
            $("#deleteProjectModal").modal({show: false});
            console.log("模态框已初始化");
        }
    } catch(e) {
        console.error("模态框初始化失败:", e);
    }
    
    // 模态框背景点击关闭功能
    $(document).on('click', '.modal-backdrop', function() {
        console.log("模态框背景被点击");
        closeAllModals();
    });
    
    // 关闭所有模态框的函数
    function closeAllModals() {
        // 移除背景遮罩
        $('.modal-backdrop').remove();
        
        if (hasBootstrap) {
            // 使用Bootstrap API
            try {
                $('.modal').modal('hide');
            } catch(e) {
                console.error("关闭模态框失败:", e);
                // 降级到DOM API
                fallbackCloseModals();
            }
        } else {
            // 使用DOM API
            fallbackCloseModals();
        }
        
        console.log("所有模态框已关闭");
    }
    
    // 使用DOM API关闭模态框的备用方法
    function fallbackCloseModals() {
        // 关闭所有模态框
        $('.modal').each(function() {
            $(this).css('display', 'none');
            $(this).removeClass('show');
        });
        
        // 移除body的modal-open类
        $('body').removeClass('modal-open');
    }
    
    // 使用DOM API显示模态框的备用方法
    function fallbackShowModal(modalId) {
        // 关闭所有可能已打开的模态框
        fallbackCloseModals();
        
        // 显示指定的模态框
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'block';
            modal.classList.add('show');
            document.body.classList.add('modal-open');
            
            // 创建背景遮罩
            const backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop fade show';
            document.body.appendChild(backdrop);
            
            console.log("模态框已显示(DOM API):", modalId);
            return true;
        }
        
        console.error("找不到模态框元素:", modalId);
        return false;
    }
    
    // 查看项目详情 - 使用更直接的方法绑定事件
    $(document).on("click", ".view-project-btn", function() {
        console.log("查看按钮被点击");
        const username = $(this).data("username");
        const projectName = $(this).data("project-name");
        console.log("查看项目:", username, projectName);
        
        // 获取项目详细信息
        $.ajax({
            url: `/admin_api/projects/details?username=${encodeURIComponent(username)}&project_name=${encodeURIComponent(projectName)}`,
            method: 'GET',
            success: function(response) {
                console.log("获取项目详情成功:", response);
                if (response.status_code === 200) {
                    const projectDetails = response.data;
                    
                    // 更新模态框内容
                    $("#projectName").text(projectDetails.project_name);
                    $("#projectOwner").text(projectDetails.username);
                    $("#projectCreated").text(formatDateTime(projectDetails.created_time));
                    $("#projectStatus").text(projectDetails.status || '正常');
                    
                    if (hasBootstrap) {
                        try {
                            // 使用Bootstrap API
                            $("#projectDetailsModal").modal('show');
                            console.log("模态框已显示(Bootstrap)");
                        } catch(e) {
                            console.error("Bootstrap显示模态框失败:", e);
                            // 降级到DOM API
                            fallbackShowModal('projectDetailsModal');
                        }
                    } else {
                        // 使用DOM API
                        fallbackShowModal('projectDetailsModal');
                    }
                } else {
                    console.error("API返回错误:", response.message);
                    alert('获取项目详情失败: ' + (response.message || '未知错误'));
                }
            },
            error: function(xhr, status, error) {
                console.error("AJAX请求失败:", status, error);
                alert('获取项目详情失败: ' + error);
            }
        });
    });
    
    // 删除项目
    $(document).on("click", ".delete-project-btn", function() {
        console.log("删除按钮被点击");
        const username = $(this).data("username");
        const projectName = $(this).data("project-name");
        console.log("删除项目:", username, projectName);
        
        // 更新确认对话框内容
        $("#delete-username").text(username);
        $("#delete-projectname").text(projectName);
        
        if (hasBootstrap) {
            try {
                // 使用Bootstrap API
                $("#deleteProjectModal").modal('show');
                console.log("删除确认模态框已显示(Bootstrap)");
            } catch(e) {
                console.error("Bootstrap显示删除模态框失败:", e);
                // 降级到DOM API
                fallbackShowModal('deleteProjectModal');
            }
        } else {
            // 使用DOM API
            fallbackShowModal('deleteProjectModal');
        }
    });
    
    // 确认删除项目
    $(document).on("click", "#confirmDeleteProject", function() {
        console.log("确认删除按钮被点击");
        const username = $("#delete-username").text();
        const projectName = $("#delete-projectname").text();
        console.log("确认删除项目:", username, projectName);
        
        // 发送删除请求
        $.ajax({
            url: `/admin_api/projects/delete?username=${encodeURIComponent(username)}&project_name=${encodeURIComponent(projectName)}`,
            method: 'DELETE',
            success: function(response) {
                console.log("删除项目成功:", response);
                
                // 关闭确认对话框
                closeAllModals();
                
                // 显示成功消息
                alert('项目删除成功');
                
                // 刷新页面
                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            },
            error: function(xhr, status, error) {
                console.error("删除项目AJAX请求失败:", status, error);
                
                if (xhr.status === 404) {
                    // 如果项目不存在，直接刷新页面
                    alert('项目不存在，页面将刷新');
                    closeAllModals();
                    
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    alert('删除项目失败: ' + error);
                }
            }
        });
    });
    
    // 关闭模态框的处理
    $(document).on('click', '[data-dismiss="modal"]', function() {
        console.log("关闭按钮被点击");
        closeAllModals();
    });
});

// 格式化日期时间
function formatDateTime(timestamp) {
    if (!timestamp) return '-';
    const date = new Date(timestamp * 1000);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}
</script>
{% endblock %}
