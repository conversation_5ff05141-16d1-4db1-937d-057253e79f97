from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel, EmailStr, Field
from typing import List, Optional
from app.utils.database import DatabaseManager
from app.core.config import settings
from app.utils.response import APIResponse, api_response
from app.common.endpoints.auth import hash_password, delete_user as auth_delete_user
from app.core.logging_config import get_logger
import time

router = APIRouter()
logger = get_logger(__name__)


def get_db():
    db = DatabaseManager(
        host=settings.DB_HOST,
        user=settings.DB_USER,
        port=settings.DB_PORT,
        password=settings.DB_PASSWORD,
        database=settings.DB_NAME
    )
    db.connect()
    return db


class UserResponse(BaseModel):
    user_id: int
    username: str
    email: Optional[EmailStr] = None
    phone_number: Optional[str] = None
    department: Optional[str] = None
    full_name: Optional[str] = None
    registration_time: str
    last_login_time: str
    is_admin: Optional[bool] = None


class UserCreate(BaseModel):
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    email: Optional[EmailStr] = Field(None, description="电子邮箱地址")
    phone_number: Optional[str] = Field(None, description="电话号码")
    department: Optional[str] = Field(None, description="部门")
    full_name: Optional[str] = Field(None, description="全名")


class UserUpdate(BaseModel):
    email: Optional[EmailStr] = Field(None, description="电子邮箱地址")
    phone_number: Optional[str] = Field(None, description="电话号码")
    department: Optional[str] = Field(None, description="部门")
    full_name: Optional[str] = Field(None, description="全名")
    is_admin: Optional[bool] = Field(None, description="是否为管理员")
    password: Optional[str] = Field(None, description="密码，如果需要修改")


@router.get("/", summary="获取所有用户", response_model=List[UserResponse])
@api_response
async def get_all_users():
    """
    获取系统中的所有用户。
    
    Returns:
        List[UserResponse]: 包含所有用户信息的列表
    """
    db = get_db()
    query = """
        SELECT user_id, username, email, phone_number, 
               department, full_name, is_admin, 
               DATE_FORMAT(registration_time, '%Y-%m-%d %H:%i:%s') as registration_time,
               DATE_FORMAT(last_login_time, '%Y-%m-%d %H:%i:%s') as last_login_time
        FROM users
    """
    try:
        result = db.execute_query(query)
        db.disconnect()
        
        # 确保我们得到的是字典列表
        return APIResponse(200, "获取用户列表成功", result if result else [])
    except Exception as e:
        db.disconnect()
        logger.error(f"获取用户列表失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取用户列表失败: {str(e)}")


@router.get("/{user_id}", summary="获取指定用户", response_model=UserResponse)
@api_response
async def get_user(user_id: int):
    """
    获取指定ID的用户信息。
    
    Args:
        user_id (int): 用户ID
        
    Returns:
        UserResponse: 用户详细信息
        
    Raises:
        HTTPException: 如果用户不存在或查询失败
    """
    db = get_db()
    query = """
        SELECT user_id, username, email, phone_number, 
               department, full_name, 
               DATE_FORMAT(registration_time, '%Y-%m-%d %H:%i:%s') as registration_time,
               DATE_FORMAT(last_login_time, '%Y-%m-%d %H:%i:%s') as last_login_time
        FROM users
        WHERE user_id = %s
    """
    try:
        result = db.execute_query(query, (user_id,))
        db.disconnect()
        
        if not result:
            raise HTTPException(status_code=404, detail=f"用户ID {user_id} 不存在")
            
        return APIResponse(200, "获取用户信息成功", result[0])
    except HTTPException:
        raise
    except Exception as e:
        db.disconnect()
        raise HTTPException(status_code=500, detail=f"获取用户信息失败: {str(e)}")


@router.post("/", summary="创建新用户", response_model=UserResponse)
@api_response
async def create_user(user: UserCreate):
    """
    创建新用户。
    
    Args:
        user (UserCreate): 要创建的用户信息
        
    Returns:
        UserResponse: 创建的用户信息
        
    Raises:
        HTTPException: 如果用户名已存在或创建失败
    """
    db = get_db()
    
    # 检查用户名是否已存在
    check_query = "SELECT 1 FROM users WHERE username = %s"
    try:
        result = db.execute_query(check_query, (user.username,))
        if result:
            db.disconnect()
            raise HTTPException(status_code=409, detail="用户名已存在")
        
        # 哈希密码
        hashed_password = hash_password(user.password)
        
        # 动态构建插入查询和参数
        columns = ["username", "password"]
        values = [user.username, hashed_password]
        placeholders = ["%s", "%s"]
        
        if user.email:
            columns.append("email")
            values.append(user.email)
            placeholders.append("%s")
        if user.phone_number:
            columns.append("phone_number")
            values.append(user.phone_number)
            placeholders.append("%s")
        if user.department:
            columns.append("department")
            values.append(user.department)
            placeholders.append("%s")
        if user.full_name:
            columns.append("full_name")
            values.append(user.full_name)
            placeholders.append("%s")
            
        insert_query = f"""
            INSERT INTO users ({', '.join(columns)}) 
            VALUES ({', '.join(placeholders)})
        """
        params = tuple(values)
        
        # 添加日志记录 (保持日志记录以供观察)
        logger.debug(f"Executing Dynamic SQL: {insert_query}")
        logger.debug(f"With Dynamic Params: {params}")

        db.execute_update(insert_query, params)
        logger.debug("User insertion successful (db.execute_update returned).")
        
        # 获取新创建的用户信息
        get_query = """
            SELECT user_id, username, email, phone_number, 
                   department, full_name, is_admin, # 确保查询 is_admin
                   DATE_FORMAT(registration_time, '%%Y-%%m-%%d %%H:%%i:%%s') as registration_time, # 保留转义 %
                   DATE_FORMAT(last_login_time, '%%Y-%%m-%%d %%H:%%i:%%s') as last_login_time   # 保留转义 %
            FROM users
            WHERE username = %(username)s # 使用命名参数
        """
        
        logger.debug(f"Executing get_query with named param: {get_query}")
        query_params = {'username': user.username}
        logger.debug(f"With named params: {query_params}")
        query_result = db.execute_query(get_query, query_params)
        logger.debug(f"Get query result: {query_result}")

        if not query_result:
            db.disconnect()
            logger.error("Error: Failed to fetch newly created user.")
            raise HTTPException(status_code=500, detail="创建用户失败: 无法检索新用户信息")

        new_user = query_result[0]
        logger.debug(f"Fetched new_user data: {new_user}")
        
        # 手动处理 is_admin (数据库可能返回 0/1/None)
        if 'is_admin' in new_user:
            db_is_admin = new_user['is_admin']
            new_user['is_admin'] = bool(db_is_admin) if db_is_admin is not None else None
            logger.debug(f"Processed is_admin: {new_user['is_admin']}")
        else:
             new_user['is_admin'] = None # 确保字段存在
             logger.debug("is_admin field missing in query result, setting to None")
             
        # 尝试创建响应对象
        try:
            response_data = UserResponse(**new_user)
            logger.debug(f"Successfully created UserResponse object: {response_data}")
            api_response_obj = APIResponse(201, "用户创建成功", response_data.dict())
            logger.debug(f"Successfully created APIResponse object: {api_response_obj}")
            db.disconnect() # 只有完全成功才断开连接
            return api_response_obj
        except Exception as serialization_error:
            db.disconnect()
            logger.error(f"Error during response serialization: {serialization_error}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"创建用户失败: 序列化响应时出错 - {serialization_error}")
            
    except HTTPException:
        # db.disconnect() # 在 HTTPException 情况下可能需要根据情况决定是否断开
        raise
    except Exception as e:
        db.disconnect()
        logger.error(f"Caught final exception block: {str(e)}", exc_info=True)
        # 提供更详细的错误信息
        raise HTTPException(status_code=500, detail=f"创建用户失败: 内部服务器错误 - {str(e)}")


@router.put("/{user_id}", summary="更新用户信息", response_model=UserResponse)
@api_response
async def update_user(user_id: int, user: UserUpdate):
    """
    更新指定用户的信息。
    
    Args:
        user_id (int): 要更新的用户ID
        user (UserUpdate): 更新的用户信息
        
    Returns:
        UserResponse: 更新后的用户信息
        
    Raises:
        HTTPException: 如果用户不存在或更新失败
    """
    db = get_db()
    try:
        # Use a single cursor context for the whole operation
        with db.get_cursor() as cursor: 
            # 检查用户是否存在
            check_query = "SELECT 1 FROM users WHERE user_id = %s"
            cursor.execute(check_query, (user_id,))
            result = cursor.fetchone()
            if not result:
                raise HTTPException(status_code=404, detail=f"用户ID {user_id} 不存在")
            
            # 构建更新查询
            update_parts = []
            params = []
            
            if user.email is not None:
                update_parts.append("email = %s")
                params.append(user.email)
            if user.phone_number is not None:
                update_parts.append("phone_number = %s")
                params.append(user.phone_number)
            if user.department is not None:
                update_parts.append("department = %s")
                params.append(user.department)
            if user.full_name is not None:
                update_parts.append("full_name = %s")
                params.append(user.full_name)
            if user.is_admin is not None:
                update_parts.append("is_admin = %s")
                params.append(user.is_admin)
            if user.password is not None:
                update_parts.append("password = %s")
                params.append(hash_password(user.password))
            
            if not update_parts:
                # Still need to fetch user data if no update
                get_query_no_update = """
                    SELECT user_id, username, email, phone_number, department, full_name, is_admin,
                           DATE_FORMAT(registration_time, '%%Y-%%m-%%d %%H:%%i:%%s') as registration_time, # Escape %
                           DATE_FORMAT(last_login_time, '%%Y-%%m-%%d %%H:%%i:%%s') as last_login_time   # Escape %
                    FROM users WHERE user_id = %(user_id)s # Use named param
                """
                query_params_no_update = {'user_id': user_id}
                cursor.execute(get_query_no_update, query_params_no_update) # Use named param
                existing_user_result = cursor.fetchone()
                if not existing_user_result:
                     # This case is less likely now but kept for robustness
                     raise HTTPException(status_code=404, detail=f"用户ID {user_id} 在尝试获取信息时未找到") 
                # Explicitly convert is_admin before returning
                db_is_admin = existing_user_result.get('is_admin')
                existing_user_result['is_admin'] = bool(db_is_admin) if db_is_admin is not None else None
                return APIResponse(200, "没有需要更新的信息", existing_user_result)
            
            # 执行更新
            update_query = f"UPDATE users SET {', '.join(update_parts)} WHERE user_id = %s"
            params.append(user_id)
            cursor.execute(update_query, tuple(params))
            # Commit is handled automatically by the context manager exiting without error
            
            # Add a small delay to ensure the database transaction is fully committed
            time.sleep(0.1)  # 100ms delay
            
            # 获取更新后的用户信息
            get_query = """
                SELECT user_id, username, email, phone_number, department, full_name, is_admin,
                       DATE_FORMAT(registration_time, '%%Y-%%m-%%d %%H:%%i:%%s') as registration_time, # Escape %
                       DATE_FORMAT(last_login_time, '%%Y-%%m-%%d %%H:%%i:%%s') as last_login_time   # Escape %
                FROM users WHERE user_id = %(user_id)s # Use named param
            """
            query_params_updated = {'user_id': user_id}
            cursor.execute(get_query, query_params_updated) # Use named param
            result = cursor.fetchone() # Use fetchone() as we expect one row
            if not result:
                # If we get here after a successful update and commit, something is very wrong.
                logger.critical(f"CRITICAL: User ID {user_id} not found via SELECT immediately after UPDATE/COMMIT on same cursor.")

                # Add additional debug info for diagnosis
                logger.debug(f"Debug: Trying to requery with direct SQL to see if data is actually there")
                cursor.execute("SELECT * FROM users WHERE user_id = %s", (user_id,))
                debug_result = cursor.fetchone()
                logger.debug(f"Debug: Raw user data found: {debug_result}")

                raise HTTPException(status_code=500, detail=f"更新后未能检索到用户ID {user_id}，数据库状态异常")
            
            updated_user_dict = result
            
            # Explicitly convert is_admin from DB int (0/1) to bool/None
            db_is_admin = updated_user_dict.get('is_admin')
            updated_user_dict['is_admin'] = bool(db_is_admin) if db_is_admin is not None else None
                
            # Context manager handles commit on successful exit
            # Return the modified dict inside the 'with' block
            return APIResponse(200, "用户信息更新成功", updated_user_dict)

    except HTTPException as http_exc:
        # Re-raise specific HTTP exceptions
        # Rollback is handled automatically by the context manager on exception
        raise http_exc
    except Exception as e:
        # Catch generic exceptions and return 500
        # Rollback is handled automatically by the context manager on exception
        logger.error(f"Error updating user {user_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"更新用户信息时发生内部错误")


@router.delete("/{user_id}", summary="删除用户")
@api_response
async def delete_user(user_id: int):
    """
    删除指定的用户。
    
    Args:
        user_id (int): 要删除的用户ID
        
    Returns:
        dict: 操作成功消息
        
    Raises:
        HTTPException: 如果用户不存在或删除失败
    """
    db = get_db()
    
    # 检查用户是否存在
    check_query = "SELECT 1 FROM users WHERE user_id = %s"
    try:
        result = db.execute_query(check_query, (user_id,))
        if not result:
            db.disconnect()
            raise HTTPException(status_code=404, detail=f"用户ID {user_id} 不存在")
        
        # 执行删除
        delete_query = "DELETE FROM users WHERE user_id = %s"
        db.execute_update(delete_query, (user_id,))
        db.disconnect()
        
        return APIResponse(200, f"用户ID {user_id} 已成功删除", None)
    except HTTPException:
        raise
    except Exception as e:
        db.disconnect()
        raise HTTPException(status_code=500, detail=f"删除用户失败: {str(e)}")


@router.delete("/by-username/{username}", summary="通过用户名删除用户")
async def delete_user_by_username(username: str):
    """
    通过用户名删除用户，并清理用户文件。
    Manually returns JSONResponse or raises HTTPException.
    
    Args:
        username (str): 要删除的用户名
        
    Returns:
        JSONResponse: On success, contains status_code=200 and message.
        
    Raises:
        HTTPException: 如果删除过程中发生错误
    """
    try:
        # Call the helper function from auth module
        await auth_delete_user(username)
        
        # If the helper function completes without raising an exception, it's a success
        logger.info(f"Successfully initiated deletion for user {username}. Returning JSONResponse.")
        # Manually construct the successful JSONResponse
        return JSONResponse(
            status_code=200,
            content={
                "status_code": 200,
                "message": "用户删除成功",
                "data": None
            }
        )

    except HTTPException as e:
        # If the helper raised an HTTPException (e.g., user not found if modified), re-raise it
        logger.warning(f"HTTPException during deletion of {username}: {e.detail}")
        raise e # FastAPI will handle this exception
    except Exception as e:
        # Catch any other exceptions from the helper
        logger.error(f"Generic exception during deletion of {username}: {str(e)}", exc_info=True)
        # Raise a standard HTTPException for FastAPI to handle
        raise HTTPException(status_code=500, detail=f"删除用户时发生内部错误: {str(e)}")
