from fastapi import APIRouter, Request, Depends, status
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from pathlib import Path
import time
from datetime import datetime
from app.utils.database import DatabaseManager
from app.core.config import settings
import app.admin_api.endpoints.users as users_api
from app.admin_api.endpoints.auth import get_current_admin_user
from app.common.endpoints.auth import SECRET_KEY, ALGORITHM
from jose import jwt, JWTError
from app.admin_api.endpoints.projects import scan_projects

router = APIRouter()

# 获取模板目录的绝对路径
TEMPLATES_DIR = Path(__file__).parent.parent / "templates"
templates = Jinja2Templates(directory=str(TEMPLATES_DIR))


def get_db():
    """获取数据库连接"""
    db = DatabaseManager(
        host=settings.DB_HOST,
        user=settings.DB_USER,
        port=settings.DB_PORT,
        password=settings.DB_PASSWORD,
        database=settings.DB_NAME
    )
    db.connect()
    return db


def get_template_context(request: Request, **kwargs):
    """获取通用模板上下文"""
    context = {"request": request, "now": int(time.time())}
    context.update(kwargs)
    return context


def timestamp_to_date(timestamp):
    """将时间戳转换为可读日期格式"""
    if not timestamp:
        return None
    try:
        dt_object = datetime.fromtimestamp(int(float(timestamp)))
        return dt_object.strftime('%Y-%m-%d %H:%M:%S')
    except (ValueError, TypeError):
        return str(timestamp)


def format_filesize(size_bytes):
    """将字节转换为可读的文件大小格式"""
    if size_bytes is None:
        return "0 B"
    size_bytes = int(size_bytes)
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024**2:
        return f"{size_bytes/1024:.1f} KB"
    elif size_bytes < 1024**3:
        return f"{size_bytes/1024**2:.1f} MB"
    else:
        return f"{size_bytes/1024**3:.1f} GB"


def default_if_none(value, default=''):
    """如果值为None或空字符串则返回默认值"""
    return default if value is None else value


# 过滤器和全局变量定义
templates.env.filters["timestamp_to_date"] = timestamp_to_date
templates.env.filters["filesizeformat"] = format_filesize
templates.env.filters["default_if_none"] = default_if_none


# 添加管理员会话检查
async def check_admin_auth(request: Request):
    """检查管理员会话是否有效"""
    # 从cookie获取令牌
    admin_token = request.cookies.get('admin_token')

    # 如果cookie中没有令牌，尝试从Authorization头获取
    if not admin_token:
        auth_header = request.headers.get('Authorization', '')
        if auth_header.startswith('Bearer '):
            admin_token = auth_header.replace('Bearer ', '')

    if not admin_token:
        return False

    # 这里可以添加验证token的逻辑
    try:
        # 解码并验证令牌
        payload = jwt.decode(admin_token, SECRET_KEY, algorithms=[ALGORITHM])

        # 检查令牌是否过期
        exp = payload.get("exp")
        if not exp or datetime.fromtimestamp(exp) < datetime.utcnow():
            return False

        # 验证是否是管理员
        is_admin = payload.get("is_admin", False)
        if not is_admin:
            return False

        return True
    except JWTError:
        return False
    except Exception:
        return False


@router.get("/", response_class=HTMLResponse, include_in_schema=False)
async def admin_root(request: Request):
    """管理后台首页，如果未登录则显示登录页面，否则显示仪表盘"""
    is_authenticated = await check_admin_auth(request)
    
    if not is_authenticated:
        return templates.TemplateResponse(
            "admin/login.html", 
            get_template_context(request)
        )
    
    # 直接调用dashboard函数的逻辑，而不是重定向
    db = get_db()
    
    try:
        # 1. 获取用户总数
        user_count_query = "SELECT COUNT(*) as count FROM users"
        user_count_result = db.execute_query(user_count_query)
        user_count = user_count_result[0]['count'] if user_count_result else 0
        
        # 2. 调用项目API获取项目列表和统计信息
        user_projects_list = scan_projects()
        
        # 计算项目总数和存储总量
        project_count = sum(up['project_count'] for up in user_projects_list)
        total_storage = sum(up['total_size_bytes'] for up in user_projects_list)
        
        # 3. 获取活跃用户数据（按项目数量排序）
        top_users = []
        for user_projects in user_projects_list:
            # 查询用户的最后登录时间
            last_login_query = "SELECT last_login_time FROM users WHERE username = %s"
            last_login_result = db.execute_query(last_login_query, (user_projects['username'],))
            last_login_time = last_login_result[0]['last_login_time'] if last_login_result else None

            full_name_query = "SELECT full_name FROM users WHERE username = %s"
            full_name_result = db.execute_query(full_name_query, (user_projects['username'],))
            full_name = full_name_result[0]['full_name'] if full_name_result else None

            user_info = {
                'username': user_projects['username'],
                'full_name': full_name,
                'project_count': user_projects['project_count'],
                'total_size': user_projects['total_size_bytes'],
                'last_login_time': last_login_time
            }
            top_users.append(user_info)
        
        # 按项目数量排序，取前5个
        top_users.sort(key=lambda x: x['project_count'], reverse=True)
        top_users = top_users[:5]
        
        # 4. 获取最近创建的项目
        recent_projects = []
        for user_projects in user_projects_list:
            for project in user_projects['projects']:
                recent_projects.append({
                    'username': user_projects['username'],
                    'project_name': project['project_name'],
                    'created_time': project['created_time'],
                    'size_bytes': project['size_bytes']
                })
        
        # 按创建时间排序，取前10个
        recent_projects.sort(key=lambda x: x['created_time'], reverse=True)
        recent_projects = recent_projects[:10]
        
        # 5. 创建模拟的活动记录
        recent_activities = []
        # 添加最近的项目创建记录
        for project in recent_projects[:5]:
            activity = {
                'time': project['created_time'],
                'username': project['username'],
                'action': f"创建了项目 {project['project_name']}"
            }
            recent_activities.append(activity)
        
        return templates.TemplateResponse(
            "admin/dashboard.html", 
            get_template_context(
                request,
                active_page="dashboard",
                user_count=user_count,
                project_count=project_count,
                total_storage=total_storage,
                top_users=top_users,
                recent_projects=recent_projects,
                recent_activities=recent_activities
            )
        )
    except Exception as e:
        return templates.TemplateResponse(
            "admin/dashboard.html", 
            get_template_context(
                request,
                active_page="dashboard",
                user_count=0,
                project_count=0,
                total_storage=0,
                top_users=[],
                recent_projects=[],
                recent_activities=[],
                error_message=f"加载仪表盘数据失败: {str(e)}"
            )
        )
    finally:
        db.disconnect()


@router.get("/login", response_class=HTMLResponse, include_in_schema=False)
async def admin_login_page(request: Request):
    """管理员登录页面"""
    return templates.TemplateResponse(
        "admin/login.html",
        get_template_context(request)
    )

@router.get("/simple", response_class=HTMLResponse, include_in_schema=False)
async def simple_login_page(request: Request):
    """简化的管理员登录页面"""
    return templates.TemplateResponse(
        "admin/simple_login.html",
        get_template_context(request)
    )


@router.post("/login", response_class=RedirectResponse)
async def admin_login_handler(request: Request):
    """处理管理员登录表单提交"""
    # 该函数仅处理表单提交，实际登录逻辑在API中处理
    return RedirectResponse(url="/admin/dashboard")


@router.get("/redirect", response_class=HTMLResponse)
async def admin_redirect(request: Request):
    """中转重定向页面，解决跳转问题"""
    return templates.TemplateResponse(
        "admin/redirect.html",
        get_template_context(request)
    )


async def create_admin_user(db):
    """确保存在一个管理员用户"""
    try:
        # 检查是否已存在管理员用户
        query = "SELECT COUNT(*) as count FROM users WHERE username = 'admin'"
        result = db.execute_query(query)
        count = result[0]['count'] if result else 0

        if count == 0:
            # 创建管理员用户
            from app.common.endpoints.auth import hash_password
            password = hash_password("<EMAIL>")  # 使用正确的密码

            insert_query = """
                INSERT INTO users (
                    username, password, email, phone_number, department, full_name, is_admin
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                "admin", password, "<EMAIL>",
                "12345678900", "管理部门", "系统管理员", 1  # 添加 is_admin = 1
            )

            db.execute_update(insert_query, params)
        
        return True
    except Exception as e:
        return False


@router.get("/dashboard", response_class=HTMLResponse, include_in_schema=False)
async def admin_dashboard(request: Request, admin_user: dict = Depends(get_current_admin_user)):
    """管理后台仪表盘"""
    db = get_db()

    try:
        # 1. 获取用户总数
        user_count_query = "SELECT COUNT(*) as count FROM users"
        user_count_result = db.execute_query(user_count_query)
        user_count = user_count_result[0]['count'] if user_count_result else 0

        # 2. 调用项目API获取项目列表和统计信息
        user_projects_list = scan_projects()

        # 计算项目总数和存储总量
        project_count = sum(up['project_count'] for up in user_projects_list)
        total_storage = sum(up['total_size_bytes']
                            for up in user_projects_list)

        # 3. 获取活跃用户数据（按项目数量排序）
        top_users = []
        for user_projects in user_projects_list:
            # 查询用户的最后登录时间
            last_login_query = "SELECT last_login_time FROM users WHERE username = %s"
            last_login_result = db.execute_query(
                last_login_query, (user_projects['username'],))
            last_login_time = last_login_result[0]['last_login_time'] if last_login_result else None

            full_name_query = "SELECT full_name FROM users WHERE username = %s"
            full_name_result = db.execute_query(
                full_name_query, (user_projects['username'],))
            full_name = full_name_result[0]['full_name'] if full_name_result else None

            user_info = {
                'username': user_projects['username'],
                'full_name': full_name,
                'project_count': user_projects['project_count'],
                'total_size': user_projects['total_size_bytes'],
                'last_login_time': last_login_time
            }
            top_users.append(user_info)

        # 按项目数量排序，取前5个
        top_users.sort(key=lambda x: x['project_count'], reverse=True)
        top_users = top_users[:5]

        # 4. 获取最近创建的项目
        recent_projects = []
        for user_projects in user_projects_list:
            for project in user_projects['projects']:
                recent_projects.append({
                    'username': user_projects['username'],
                    'project_name': project['project_name'],
                    'created_time': project['created_time'],
                    'size_bytes': project['size_bytes']
                })

        # 按创建时间排序，取前10个
        recent_projects.sort(key=lambda x: x['created_time'], reverse=True)
        recent_projects = recent_projects[:10]

        # 5. 创建模拟的活动记录
        recent_activities = []
        # 添加最近的项目创建记录
        for project in recent_projects[:5]:
            activity = {
                'time': project['created_time'],
                'username': project['username'],
                'action': f"创建了项目 {project['project_name']}"
            }
            recent_activities.append(activity)

        return templates.TemplateResponse(
            "admin/dashboard.html",
            get_template_context(
                request,
                active_page="dashboard",
                user_count=user_count,
                project_count=project_count,
                total_storage=total_storage,
                top_users=top_users,
                recent_projects=recent_projects,
                recent_activities=recent_activities
            )
        )
    except Exception as e:
        return templates.TemplateResponse(
            "admin/dashboard.html",
            get_template_context(
                request,
                active_page="dashboard",
                user_count=0,
                project_count=0,
                total_storage=0,
                top_users=[],
                recent_projects=[],
                recent_activities=[],
                error_message=f"加载仪表盘数据失败: {str(e)}"
            )
        )
    finally:
        db.disconnect()


@router.get("/users", response_class=HTMLResponse, include_in_schema=False)
async def admin_users_page(request: Request, admin_user: dict = Depends(get_current_admin_user)):
    """用户管理页面"""
    try:
        # 初始化数据库连接并确保存在管理员用户
        db = get_db()
        await create_admin_user(db)
        db.disconnect()

        # 获取所有用户数据
        users_response = await users_api.get_all_users()
        
        users_data = []
        # 解析JSON响应
        if hasattr(users_response, 'body'):
            # 处理JSONResponse对象
            import json
            response_json = json.loads(users_response.body.decode('utf-8'))
            if 'data' in response_json:
                users_data = response_json['data']
        # 兼容之前的方式
        elif hasattr(users_response, 'data'):
            users_data = users_response.data
        elif isinstance(users_response, dict) and 'data' in users_response:
            users_data = users_response['data']
        
        # 确保用户数据是列表
        if not isinstance(users_data, list):
            users_data = [users_data] if users_data else []

        return templates.TemplateResponse(
            "admin/users.html",
            get_template_context(
                request,
                active_page="users",
                users=users_data
            )
        )
    except Exception as e:
        return templates.TemplateResponse(
            "admin/users.html",
            get_template_context(
                request,
                active_page="users",
                users=[],
                error_message=f"加载用户数据失败: {str(e)}"
            )
        )


@router.get("/projects", response_class=HTMLResponse, include_in_schema=False)
async def admin_projects_page(request: Request, admin_user: dict = Depends(get_current_admin_user)):
    """项目管理页面"""
    try:
        # 调用项目API获取所有项目列表
        user_projects_list = scan_projects()

        # 对项目按时间排序，将最新的项目排在前面
        for user_projects in user_projects_list:
            user_projects['projects'].sort(
                key=lambda x: x['created_time'], reverse=True)

        # 对用户按项目数量排序
        user_projects_list.sort(key=lambda x: x['project_count'], reverse=True)

        # 格式化时间戳
        for user_projects in user_projects_list:
            for project in user_projects['projects']:
                # 这里我们使用timestamp_to_date过滤器来在模板中处理时间
                pass

        return templates.TemplateResponse(
            "admin/projects.html",
            get_template_context(
                request,
                active_page="projects",
                user_projects_list=user_projects_list
            )
        )
    except Exception as e:
        return templates.TemplateResponse(
            "admin/projects.html",
            get_template_context(
                request,
                active_page="projects",
                user_projects_list=[],
                error_message=f"加载项目数据失败: {str(e)}"
            )
        )
