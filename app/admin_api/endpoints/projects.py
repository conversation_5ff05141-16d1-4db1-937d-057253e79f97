from fastapi import APIRouter, HTTPException, Query, Request
from fastapi.responses import JSONResponse
from typing import List, Dict, Any, Optional
import os
import time
import json
from pathlib import Path
from app.utils.response import APIResponse, api_response
from app.core.config import settings
from app.utils.database import DatabaseManager
from app.core.logging_config import get_logger

router = APIRouter()
logger = get_logger(__name__)

# 从配置中获取上传目录
UPLOAD_DIRECTORY = settings.UPLOAD_DIRECTORY
ROOT_DIRECTORY = settings.PROJECT_ROOT


def get_db():
    db = DatabaseManager(
        host=settings.DB_HOST,
        user=settings.DB_USER,
        port=settings.DB_PORT,
        password=settings.DB_PASSWORD,
        database=settings.DB_NAME
    )
    db.connect()
    return db


def get_file_size(file_path):
    """获取文件大小"""
    try:
        return os.path.getsize(file_path)
    except Exception:
        return 0


def get_directory_size(directory_path):
    """递归获取目录总大小"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(directory_path):
        for filename in filenames:
            file_path = os.path.join(dirpath, filename)
            if os.path.isfile(file_path):
                total_size += get_file_size(file_path)
    return total_size


def get_file_list(directory_path):
    """获取目录中的文件列表"""
    files = []
    if not os.path.exists(directory_path):
        return files
        
    try:
        for item in os.listdir(directory_path):
            item_path = os.path.join(directory_path, item)
            if os.path.isfile(item_path):
                files.append({
                    "name": item,
                    "path": os.path.relpath(item_path, UPLOAD_DIRECTORY),
                    "size": get_file_size(item_path),
                    "modified_time": int(os.path.getmtime(item_path)),
                    "created_time": int(os.path.getctime(item_path))
                })
    except Exception as e:
        logger.error(f"获取文件列表失败: {str(e)}", exc_info=True)

    return files


def scan_projects():
    """扫描上传目录获取所有用户项目, 包含用户名和姓名"""
    user_projects_list = []
    db = None
    
    if not os.path.exists(UPLOAD_DIRECTORY):
        logger.warning(f"上传目录不存在: {UPLOAD_DIRECTORY}")
        return user_projects_list
    
    try:
        db = get_db()
        # 遍历uploads目录下的所有用户目录
        for username in os.listdir(UPLOAD_DIRECTORY):
            user_dir = os.path.join(UPLOAD_DIRECTORY, username)
            
            # 跳过非目录项
            if not os.path.isdir(user_dir):
                continue
            
            # 查询用户的全名
            full_name = "N/A" # Default if not found
            user_query = "SELECT full_name FROM users WHERE username = %s"
            user_result = db.execute_query(user_query, (username,))
            if user_result:
                full_name = user_result[0].get('full_name', 'N/A')
            else:
                logger.warning(f"Warning: User '{username}' found in uploads but not in database.")

            projects = []
            # 遍历用户目录下的所有项目目录
            for project_name in os.listdir(user_dir):
                project_dir = os.path.join(user_dir, project_name)
                
                # 跳过非目录项
                if not os.path.isdir(project_dir):
                    continue
                    
                # 计算项目大小和创建时间
                size_bytes = get_directory_size(project_dir)
                created_time = int(os.path.getctime(project_dir))
                
                # 获取项目中的文件数量
                file_count = 0
                for _, _, files in os.walk(project_dir):
                    file_count += len(files)
                
                # 创建项目信息 (username is still needed for other parts)
                project = {
                    "project_name": project_name,
                    "username": username, 
                    "size_bytes": size_bytes,
                    "created_time": created_time,
                    "file_count": file_count
                }
                
                # 尝试读取项目配置文件(如果存在)
                config_path = os.path.join(project_dir, "config.json")
                if os.path.exists(config_path):
                    try:
                        with open(config_path, 'r', encoding='utf-8') as f:
                            project["config"] = json.load(f)
                    except Exception as e:
                        logger.error(f"读取项目配置失败: {str(e)}", exc_info=True)
                
                projects.append(project)
            
            # 按创建时间排序
            projects.sort(key=lambda x: x["created_time"], reverse=True)
            
            # 计算用户项目总大小
            total_size = sum(p["size_bytes"] for p in projects)
            
            # 添加用户项目列表, 包含姓名
            user_projects_list.append({
                "username": username,
                "full_name": full_name, # Add full_name here
                "project_count": len(projects),
                "total_size_bytes": total_size,
                "projects": projects
            })
    except Exception as e:
        logger.error(f"Error scanning projects: {e}", exc_info=True)
        # Depending on desired behavior, maybe raise or return partial list
    finally:
        if db:
            db.disconnect()
    
    return user_projects_list


@router.get("/list", summary="获取所有项目列表")
@api_response
async def get_all_projects():
    """
    获取所有用户的项目列表。
    
    返回:
    - 包含所有用户项目信息的列表
    """
    try:
        user_projects = scan_projects()
        return APIResponse(200, "获取项目列表成功", data=user_projects)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目列表失败: {str(e)}")


@router.get("/details", summary="获取项目详情")
async def get_project_details(username: str = Query(...), project_name: str = Query(...)):
    """
    获取项目详情。
    Manually returns JSONResponse on success, raises HTTPException on error.
    
    参数:
    - username: 用户名
    - project_name: 项目名称
    
    返回:
    - JSONResponse: On success, contains status_code=200, message, and project data.
    
    Raises:
    - HTTPException: 如果项目不存在或获取详情时发生错误。
    """
    try:
        project_dir = os.path.join(UPLOAD_DIRECTORY, username, project_name)
        
        if not os.path.isdir(project_dir): # Check if it's a directory
            raise HTTPException(status_code=404, detail="项目不存在或不是有效目录")
        
        # Calculate size and created time
        size_bytes = get_directory_size(project_dir)
        created_time = int(os.path.getctime(project_dir))
        
        # Get file list (limited to 100)
        files = []
        file_count = 0
        # Use try-except specifically around file operations if needed
        try:
            for root, _, filenames in os.walk(project_dir):
                for filename in filenames:
                    if file_count >= 100: break
                    file_path = os.path.join(root, filename)
                    if os.path.isfile(file_path):
                        rel_path = os.path.relpath(file_path, project_dir)
                        files.append({
                            "name": filename,
                            "path": rel_path,
                            "size": get_file_size(file_path),
                            "modified_time": int(os.path.getmtime(file_path)),
                            "created_time": int(os.path.getctime(file_path))
                        })
                        file_count += 1
                if file_count >= 100: break
        except Exception as file_error:
             logger.error(f"Error listing files in {project_dir}: {file_error}", exc_info=True)
             # Decide if this is fatal or just return partial data
             # For now, let's treat it as fatal for consistency
             raise HTTPException(status_code=500, detail=f"获取项目文件列表时出错: {file_error}")

        # Prepare project info
        project_info = {
            "project_name": project_name,
            "username": username,
            "size_bytes": size_bytes,
            "created_time": created_time,
            "file_count": file_count,
            "files": files
        }
        
        # Try reading config.json (optional)
        config_path = os.path.join(project_dir, "config.json")
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    project_info["config"] = json.load(f)
            except Exception as config_error:
                logger.error(f"读取项目配置失败 ({config_path}): {config_error}", exc_info=True)
                # Non-fatal, just ignore or add a note
                project_info["config_error"] = str(config_error)
        
        # Success: Manually return JSONResponse
        return JSONResponse(
            status_code=200,
            content={
                "status_code": 200,
                "message": "获取项目详情成功",
                "data": project_info
            }
        )

    except HTTPException as http_exc:
        # Re-raise specific HTTPExceptions (like 404 or the one from file listing)
        raise http_exc
    except Exception as e:
        # Catch any other unexpected errors
        logger.error(f"Unexpected error getting details for {username}/{project_name}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取项目详情时发生内部错误: {str(e)}")


@router.delete("/delete", summary="删除项目")
@api_response
async def delete_project(username: str = Query(...), project_name: str = Query(...)):
    """
    删除指定项目。
    
    参数:
    - username: 用户名
    - project_name: 项目名称
    
    返回:
    - 删除结果
    """
    try:
        project_dir = os.path.join(UPLOAD_DIRECTORY, username, project_name)
        
        if not os.path.exists(project_dir):
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 删除项目目录
        import shutil
        shutil.rmtree(project_dir)
        
        # 同时删除可能存在的静态文件目录
        static_dir = os.path.join(ROOT_DIRECTORY, "static", username, project_name)
        if os.path.exists(static_dir):
            shutil.rmtree(static_dir)
        
        return APIResponse(200, "删除项目成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除项目失败: {str(e)}")
