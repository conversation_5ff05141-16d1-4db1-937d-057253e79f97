from fastapi import APIRouter, HTTPException, Depends, Request, <PERSON>ie, status
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel, Field
from typing import Optional
from app.utils.database import DatabaseManager
from app.core.config import settings
from app.utils.response import APIResponse, api_response
from app.common.endpoints.auth import hash_password, verify_password, create_access_token, SECRET_KEY, ALGORITHM
from app.core.logging_config import get_logger
from jose import jwt, JWTError
from datetime import datetime, timedelta, timezone

router = APIRouter()
logger = get_logger(__name__)

# Setup templates
templates = Jinja2Templates(directory="app/admin_api/templates")

def get_db():
    """获取数据库连接"""
    db = DatabaseManager(
        host=settings.DB_HOST,
        user=settings.DB_USER,
        port=settings.DB_PORT,
        password=settings.DB_PASSWORD,
        database=settings.DB_NAME
    )
    db.connect()
    return db


class AdminLogin(BaseModel):
    username: str = Field(..., description="管理员用户名")
    password: str = Field(..., description="管理员密码")


async def is_admin_user(username: str, db):
    """检查用户是否是管理员"""
    query = """
        SELECT user_id, is_admin 
        FROM users 
        WHERE username = %s
    """
    result = db.execute_query(query, (username,))
    
    if result and len(result) > 0:
        return result[0].get('is_admin', 0) == 1
    return False


@router.options("/admin_login")
async def admin_login_options():
    """处理预检请求"""
    from fastapi.responses import Response
    return Response(
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, X-Requested-With, Accept",
            "Access-Control-Max-Age": "86400"
        }
    )

@router.get("/test_connection", summary="测试连接")
async def test_connection():
    """最简单的连接测试"""
    from fastapi.responses import JSONResponse
    return JSONResponse(
        content={"status": "ok", "message": "连接正常"},
        headers={
            "Content-Type": "application/json; charset=utf-8",
            "Access-Control-Allow-Origin": "*"
        }
    )

@router.post("/admin_login_debug", summary="管理员登录调试版本")
async def admin_login_debug(request: Request):
    """调试版本的登录端点，包含完整登录逻辑"""
    logger.info("收到调试登录请求")

    db = None
    try:
        # 读取请求体
        body = await request.body()
        logger.info(f"请求体: {body}")

        # 检查是否是登录请求
        import json
        try:
            login_data = json.loads(body)

            # 如果包含用户名和密码，执行登录逻辑
            if 'username' in login_data and 'password' in login_data:
                username = login_data.get('username')
                password = login_data.get('password')

                if not username or not password:
                    raise HTTPException(status_code=400, detail="用户名和密码不能为空")

                logger.info(f"执行登录逻辑: username={username}")

                # 数据库连接
                db = get_db()
                logger.info("数据库连接成功")

                # 查询用户信息
                logger.info("开始查询用户信息")
                query = """
                    SELECT user_id, password, full_name, username, is_admin
                    FROM users
                    WHERE username = %s
                """
                result = db.execute_query(query, (username,))
                logger.info(f"查询结果: {len(result) if result else 0} 条记录")

                if not result:
                    logger.warning(f"用户名错误: {username}")
                    if db:
                        db.disconnect()
                    from fastapi.responses import JSONResponse
                    return JSONResponse(
                        content={"code": 401, "message": "用户名错误"},
                        status_code=401
                    )

                user_data = result[0]
                user_id = user_data['user_id']
                stored_password = user_data['password']
                full_name = user_data['full_name']
                username = user_data['username']
                is_admin = user_data['is_admin']

                # 验证密码
                logger.info("开始验证密码")
                if not verify_password(password, stored_password):
                    logger.warning(f"密码错误，用户: {username}")
                    if db:
                        db.disconnect()
                    from fastapi.responses import JSONResponse
                    return JSONResponse(
                        content={"code": 401, "message": "密码错误"},
                        status_code=401
                    )

                # 验证是否是管理员
                logger.info(f"验证管理员权限: is_admin={is_admin}")
                if is_admin != 1:
                    logger.warning(f"非管理员账户，用户: {username}")
                    if db:
                        db.disconnect()
                    from fastapi.responses import JSONResponse
                    return JSONResponse(
                        content={"code": 403, "message": "非管理员账户，禁止访问"},
                        status_code=403
                    )

                # 生成访问令牌
                logger.info("生成访问令牌")
                access_token = create_access_token(
                    data={
                        "sub": str(user_id),
                        "username": username,
                        "full_name": full_name,
                        "is_admin": True
                    }
                )

                logger.info(f"管理员登录成功: {username}")

                # 关闭数据库连接
                if db:
                    db.disconnect()

                # 返回成功响应
                from fastapi.responses import JSONResponse
                return JSONResponse(
                    content={
                        "code": 200,
                        "message": "管理员登录成功",
                        "data": {
                            "full_name": full_name,
                            "username": username,
                            "access_token": access_token
                        }
                    },
                    headers={
                        "Content-Type": "application/json; charset=utf-8",
                        "Access-Control-Allow-Origin": "*"
                    }
                )
            else:
                # 普通调试请求
                from fastapi.responses import JSONResponse
                return JSONResponse(
                    content={"status": "debug", "message": "调试端点工作正常"},
                    headers={
                        "Content-Type": "application/json; charset=utf-8",
                        "Access-Control-Allow-Origin": "*"
                    }
                )

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            from fastapi.responses import JSONResponse
            return JSONResponse(
                content={"code": 400, "message": "请求格式错误"},
                status_code=400
            )

    except Exception as e:
        logger.error(f"调试端点错误: {str(e)}", exc_info=True)
        if db:
            try:
                db.disconnect()
            except:
                pass
        from fastapi.responses import JSONResponse
        return JSONResponse(
            content={"code": 500, "message": f"登录过程中发生错误: {str(e)}"},
            status_code=500
        )

@router.post("/admin_login", summary="管理员登录")
async def admin_login(request: Request):
    """
    管理员登录接口

    参数:
    - username: 管理员用户名
    - password: 管理员密码

    返回:
    - access_token: 管理员访问令牌
    - username: 用户名
    - full_name: 管理员姓名
    """
    logger.info("收到管理员登录请求")

    db = None
    try:
        # 手动解析请求体
        body = await request.body()
        logger.info(f"请求体: {body}")

        import json
        try:
            login_data = json.loads(body)
            username = login_data.get('username')
            password = login_data.get('password')

            if not username or not password:
                raise HTTPException(status_code=400, detail="用户名和密码不能为空")

            logger.info(f"解析登录数据成功: username={username}")
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            raise HTTPException(status_code=400, detail="请求格式错误")

        db = get_db()
        logger.info("数据库连接成功")
        # 查询用户信息
        logger.info("开始查询用户信息")
        query = """
            SELECT user_id, password, full_name, username, is_admin
            FROM users
            WHERE username = %s
        """
        result = db.execute_query(query, (username,))
        logger.info(f"查询结果: {len(result) if result else 0} 条记录")

        if not result:
            logger.warning(f"用户名错误: {username}")
            if db:
                db.disconnect()
            raise HTTPException(status_code=401, detail="用户名错误")
        
        user_id = result[0]['user_id']
        stored_password = result[0]['password']
        full_name = result[0]['full_name']
        username = result[0]['username']
        is_admin = result[0].get('is_admin', 0)
        
        # 验证密码
        logger.info("开始验证密码")
        if not verify_password(password, stored_password):
            logger.warning(f"密码错误，用户: {username}")
            if db:
                db.disconnect()
            raise HTTPException(status_code=401, detail="密码错误")

        # 验证是否是管理员
        logger.info(f"验证管理员权限: is_admin={is_admin}")
        if is_admin != 1:
            logger.warning(f"非管理员账户，用户: {username}")
            if db:
                db.disconnect()
            raise HTTPException(status_code=403, detail="非管理员账户，禁止访问")
        
        # 更新最后登录时间
        update_query = "UPDATE users SET last_login_time = %s WHERE user_id = %s"
        update_params = (datetime.now(), user_id)
        db.execute_update(update_query, update_params)
        
        # 创建访问令牌
        token_data = {
            "sub": str(user_id),
            "username": username,
            "full_name": full_name,
            "is_admin": True
        }
        access_token = create_access_token(token_data)
        
        # 关闭数据库连接
        db.disconnect()

        logger.info(f"管理员登录成功: {username}")

        from fastapi.responses import JSONResponse

        # 创建自定义响应对象，确保中文字符正确编码
        response_data = {
            "code": 200,
            "message": "管理员登录成功",
            "data": {
                "full_name": full_name,
                "username": username,
                "access_token": access_token
            }
        }

        # 使用JSONResponse确保正确的编码，添加内网环境友好的头部
        return JSONResponse(
            content=response_data,
            headers={
                "Content-Type": "application/json; charset=utf-8",
                "Cache-Control": "no-cache, no-store, must-revalidate",
                "Pragma": "no-cache",
                "Expires": "0",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, X-Requested-With"
            }
        )
    except HTTPException as he:
        logger.warning(f"HTTP异常: {he.detail}")
        if db:
            try:
                db.disconnect()
            except:
                pass
        raise he
    except Exception as e:
        logger.error(f"管理员登录失败: {str(e)}", exc_info=True)
        if db:
            try:
                db.disconnect()
            except:
                pass
        raise HTTPException(status_code=500, detail=f"登录过程中发生错误: {str(e)}")
    finally:
        # 确保数据库连接被关闭
        if db:
            try:
                db.disconnect()
            except Exception as e:
                logger.error(f"关闭数据库连接时出错: {str(e)}")


@router.post("/simple_login")
async def simple_login(request: Request):
    """最简单的登录端点"""
    try:
        logger.info("=== 简单登录端点 ===")

        # 读取请求体
        body = await request.body()
        body_str = body.decode('utf-8')
        logger.info(f"请求体: {body_str}")

        # 解析JSON
        import json
        data = json.loads(body_str)
        username = data.get('username', '').strip()
        password = data.get('password', '').strip()

        logger.info(f"用户名: {username}, 密码长度: {len(password)}")

        if not username or not password:
            return {"success": False, "message": "用户名和密码不能为空"}

        # 使用现有的数据库连接
        db = get_db()



        # 查询用户
        query = "SELECT user_id, password, full_name, username, is_admin FROM users WHERE username = %s"
        result = db.execute_query(query, (username,))

        if not result:
            db.disconnect()
            return {"success": False, "message": "用户名或密码错误"}

        user = result[0]

        # 验证密码
        if not verify_password(password, user['password']):
            db.disconnect()
            return {"success": False, "message": "用户名或密码错误"}

        # 检查管理员权限
        if user['is_admin'] != 1:
            db.disconnect()
            return {"success": False, "message": "无管理员权限"}

        # 生成token
        token = create_access_token(data={
            "sub": str(user['user_id']),
            "username": user['username'],
            "full_name": user['full_name'],
            "is_admin": True
        })

        db.disconnect()

        logger.info("登录成功")
        return {
            "success": True,
            "message": "登录成功",
            "data": {
                "username": user['username'],
                "full_name": user['full_name'],
                "access_token": token
            }
        }

    except Exception as e:
        logger.error(f"登录异常: {str(e)}", exc_info=True)
        return {"success": False, "message": f"登录失败: {str(e)}"}


async def upgrade_user_to_admin(username: str):
    """将用户升级为管理员"""
    db = get_db()
    try:
        # 查询用户是否存在
        query = "SELECT user_id FROM users WHERE username = %s"
        result = db.execute_query(query, (username,))
        
        if not result:
            db.disconnect()
            return False, "用户不存在"
        
        # 更新用户为管理员
        update_query = "UPDATE users SET is_admin = 1 WHERE username = %s"
        db.execute_update(update_query, (username,))
        
        db.disconnect()
        return True, "用户已成功升级为管理员"
    except Exception as e:
        db.disconnect()
        return False, str(e)


@router.post("/make_admin", summary="将用户设为管理员")
@api_response
async def make_admin(username: str):
    """
    将指定用户设为管理员
    
    参数:
    - username: 要升级为管理员的用户名
    
    返回:
    - 操作结果
    """
    success, message = await upgrade_user_to_admin(username)
    
    if success:
        return APIResponse(200, message)
    else:
        raise HTTPException(status_code=500, detail=message)


# Exception for redirection
credentials_exception = HTTPException(
    status_code=status.HTTP_307_TEMPORARY_REDIRECT,
    detail="Not authenticated",
    headers={"Location": "/admin/login"},
)

async def get_current_admin_user(admin_token: Optional[str] = Cookie(None)):
    """
    Dependency to verify the admin token from the cookie and check admin status.
    Redirects to login page if verification fails.
    """
    if admin_token is None:
        logger.warning("Admin token cookie missing, redirecting to login.")
        raise credentials_exception
    
    try:
        payload = jwt.decode(admin_token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("username")
        user_id: str = payload.get("sub")
        exp: int = payload.get("exp")
        
        if username is None or user_id is None:
            logger.warning("Token payload missing username or sub, redirecting.")
            raise credentials_exception

        if exp is None or datetime.fromtimestamp(exp, timezone.utc) < datetime.now(timezone.utc):
             logger.warning("Token expired, redirecting.")
             raise credentials_exception

    except JWTError as e:
        logger.warning(f"JWT Error: {e}, redirecting.")
        raise credentials_exception
    except Exception as e:
         logger.warning(f"Token validation error: {e}, redirecting.")
         raise credentials_exception

    db = None
    try:
        db = get_db()
        query = "SELECT is_admin FROM users WHERE user_id = %s"
        result = db.execute_query(query, (user_id,))
        if not result or not result[0]['is_admin']:
            logger.warning(f"User ID {user_id} not found or not admin, redirecting.")
            raise credentials_exception

        return payload
    except Exception as db_error:
        logger.error(f"DB error checking admin status: {db_error}", exc_info=True)
        raise credentials_exception
    finally:
        if db:
            db.disconnect()
