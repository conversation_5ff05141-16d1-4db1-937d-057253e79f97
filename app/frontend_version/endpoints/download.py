from fastapi import APIRouter, HTTPException
from fastapi.responses import FileResponse
from app.frontend_version.utils import version_manager

router = APIRouter()


@router.get("/{version}")
async def download_version(version: str):
    """
    下载指定版本的前端安装包
    """
    try:
        version_info = version_manager.get_version_info(version)
        if not version_info:
            raise HTTPException(status_code=404, detail="Version not found")
        
        file_path = version_manager.get_file_path(version)
        if not file_path:
            raise HTTPException(status_code=404, detail="Installation file not available")
        
        return FileResponse(
            path=file_path,
            filename=version_info["file_name"],
            media_type="application/octet-stream"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 