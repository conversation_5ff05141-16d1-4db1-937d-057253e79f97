from fastapi import APIRouter, HTTPException, Request
from fastapi.templating import Jinja2Templates
from app.frontend_version.schemas import (
    VersionCheckRequest,
    VersionCheckResponse,
    VersionHistoryResponse
)
from app.frontend_version.utils import version_manager
import os
from app.core.config import settings

router = APIRouter()
templates = Jinja2Templates(directory=os.path.join(
    settings.PROJECT_ROOT, "app", "templates"))


@router.post("/check", response_model=VersionCheckResponse)
async def check_version(request: VersionCheckRequest):
    """
    检查前端版本是否需要更新
    """
    try:
        return version_manager.check_update(request.current_version)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/history", response_model=VersionHistoryResponse)
async def get_version_history():
    """
    获取版本历史记录
    """
    try:
        return {"versions": version_manager.get_version_history()}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 版本历史页面的处理函数 - 现在只用于/downloads路由
async def version_history_page(request: Request):
    """
    版本历史页面
    """
    try:
        versions = version_manager.get_version_history()
        return templates.TemplateResponse(
            "downloads/version_history.html",
            {"request": request, "versions": versions}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
