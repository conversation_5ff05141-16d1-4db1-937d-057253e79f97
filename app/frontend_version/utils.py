import json
import os
from datetime import datetime
from typing import Optional, Dict, List
import semver
from app.core.config import settings
from app.core.logging_config import get_logger

logger = get_logger(__name__)

class VersionManager:
    def __init__(self):
        self.version_file = os.path.join(settings.FRONTEND_PACKAGES_DIR, "version_info.json")
        self.packages_dir = settings.FRONTEND_PACKAGES_DIR
        self._load_version_info()

    def _load_version_info(self) -> None:
        """加载版本信息文件"""
        if not os.path.exists(self.version_file):
            self._create_default_version_file()
        
        with open(self.version_file, 'r', encoding='utf-8') as f:
            self.version_info = json.load(f)

    def _create_default_version_file(self) -> None:
        """创建默认版本信息文件。file_name 字段支持任意后缀（如 .exe、.zip 等），请与实际上传文件一致。"""
        # 获取当前日期作为默认发布日期
        current_date = datetime.now().strftime("%Y-%m-%d")
        
        default_info = {
            "latest_version": "1.0.0",
            "versions": [
                {
                    "version": "1.0.0",
                    "changelog": "初始版本",
                    "file_name": "office_plugin_v1.0.0.exe",
                    "release_date": current_date
                }
            ]
        }
        
        os.makedirs(os.path.dirname(self.version_file), exist_ok=True)
        with open(self.version_file, 'w', encoding='utf-8') as f:
            json.dump(default_info, f, ensure_ascii=False, indent=4)

    def get_latest_version(self) -> str:
        """获取最新版本号，兼容新旧两种字段名"""
        # 兼容旧版本使用current_version的情况
        if "latest_version" in self.version_info:
            return self.version_info["latest_version"]
        elif "current_version" in self.version_info:
            return self.version_info["current_version"]
        else:
            # 如果两个字段都不存在，返回默认版本
            return "1.0.0"

    def get_latest_version_info(self) -> Optional[Dict]:
        """获取最新版本信息"""
        latest_version = self.get_latest_version()
        return self.get_version_info(latest_version)

    def get_version_info(self, version: str) -> Optional[Dict]:
        """获取指定版本信息"""
        for v in self.version_info["versions"]:
            if v["version"] == version:
                return v
        return None

    def check_update(self, current_version: str) -> Dict:
        """检查是否需要更新"""
        latest_version_str = self.get_latest_version()
        latest_version_info = self.get_latest_version_info()
        
        if not latest_version_info:
            return {
                "update_available": False,
                "current_version": current_version,
                "latest_version": current_version
            }

        try:
            # 使用语义化版本比较
            update_available = semver.compare(latest_version_str, current_version) > 0
        except Exception as e:
            logger.error(f"版本比较出错: {e}", exc_info=True)
            # 版本格式错误时，默认不更新
            update_available = False
        
        # 安全地获取字段，防止缺少字段导致报错
        return {
            "update_available": update_available,
            "current_version": current_version,
            "latest_version": latest_version_str,
            "changelog": latest_version_info.get("changelog") if update_available else None
        }

    def _get_file_last_modified(self, file_path: str) -> str:
        """获取文件的最后修改时间，格式化为完整日期时间格式"""
        try:
            if os.path.exists(file_path):
                # 获取文件的创建时间和修改时间，使用较早的时间作为发布时间
                # Windows和Linux的文件时间属性有差异，这样可以增加兼容性
                c_timestamp = os.path.getctime(file_path)
                m_timestamp = os.path.getmtime(file_path)
                
                # 使用较早的那个时间
                timestamp = min(c_timestamp, m_timestamp)
                dt = datetime.fromtimestamp(timestamp)
                
                # 返回完整日期时间格式
                return dt.strftime("%Y-%m-%d %H:%M:%S")
        except Exception as e:
            logger.error(f"获取文件时间出错: {e}", exc_info=True)
        return "未知"

    def get_version_history(self) -> List[Dict]:
        """获取版本历史"""
        versions = self.version_info["versions"]
        latest_version = self.get_latest_version()
        
        for version in versions:
            # 添加是否为最新版本的标记
            version["is_latest"] = (version["version"] == latest_version)
            
            # 优先使用JSON中的release_date字段，如果不存在则回退到文件时间
            if "release_date" not in version or not version["release_date"]:
                # 当release_date字段不存在或为空时，尝试从文件时间获取
                file_path = self.get_file_path(version["version"])
                if file_path:
                    version["release_date"] = self._get_file_last_modified(file_path)
                else:
                    version["release_date"] = "未知"
        
        return versions

    def get_file_path(self, version: str) -> Optional[str]:
        """获取指定版本的文件路径。安装包应直接放在 frontend_packages 目录下，file_name 字段与实际文件名一致。"""
        version_data = self.get_version_info(version)
        if not version_data:
            return None
        
        file_name = version_data.get("file_name")
        if not file_name:
            return None
            
        file_path = os.path.join(
            self.packages_dir,
            file_name
        )
        
        return file_path if os.path.exists(file_path) else None


# 创建全局版本管理器实例
version_manager = VersionManager()
