from pydantic import BaseModel
from datetime import datetime
from typing import Optional, List


class VersionInfo(BaseModel):
    version: str
    release_date: Optional[datetime] = None
    changelog: str
    file_name: Optional[str] = None
    is_latest: Optional[bool] = False


class VersionConfig(BaseModel):
    latest_version: str
    versions: List[VersionInfo]


class VersionCheckRequest(BaseModel):
    current_version: str


class VersionCheckResponse(BaseModel):
    update_available: bool
    current_version: str
    latest_version: str
    changelog: Optional[str] = None


class VersionHistoryResponse(BaseModel):
    versions: List[VersionInfo] 