"""
通用数据模型定义
包含所有模块共用的请求和响应模型
"""

from typing import Any, Dict, Optional, List, Union
from pydantic import BaseModel, Field
from datetime import datetime


class BaseRequest(BaseModel):
    """基础请求模型"""
    pass


class BaseResponse(BaseModel):
    """基础响应模型"""
    status_code: int = Field(..., description="HTTP状态码")
    message: str = Field(..., description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")


class SuccessResponse(BaseResponse):
    """成功响应模型"""
    data: Optional[Any] = Field(None, description="响应数据")
    
    class Config:
        json_schema_extra = {
            "example": {
                "status_code": 200,
                "message": "操作成功",
                "data": {"result": "success"},
                "timestamp": "2024-01-01T12:00:00"
            }
        }


class ErrorDetail(BaseModel):
    """错误详情模型"""
    field: Optional[str] = Field(None, description="错误字段")
    message: str = Field(..., description="错误消息")
    code: Optional[str] = Field(None, description="错误代码")


class ErrorResponse(BaseResponse):
    """错误响应模型"""
    errors: Optional[List[ErrorDetail]] = Field(None, description="详细错误信息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "status_code": 400,
                "message": "请求参数错误",
                "errors": [
                    {
                        "field": "username",
                        "message": "用户名不能为空",
                        "code": "REQUIRED_FIELD"
                    }
                ],
                "timestamp": "2024-01-01T12:00:00"
            }
        }


class PaginationMeta(BaseModel):
    """分页元数据模型"""
    page: int = Field(..., description="当前页码", ge=1)
    page_size: int = Field(..., description="每页数量", ge=1, le=100)
    total: int = Field(..., description="总记录数", ge=0)
    total_pages: int = Field(..., description="总页数", ge=0)
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")


class PaginatedResponse(SuccessResponse):
    """分页响应模型"""
    data: List[Any] = Field(..., description="数据列表")
    meta: PaginationMeta = Field(..., description="分页信息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "status_code": 200,
                "message": "获取数据成功",
                "data": [{"id": 1, "name": "示例"}],
                "meta": {
                    "page": 1,
                    "page_size": 10,
                    "total": 100,
                    "total_pages": 10,
                    "has_next": True,
                    "has_prev": False
                },
                "timestamp": "2024-01-01T12:00:00"
            }
        }


class FileInfo(BaseModel):
    """文件信息模型"""
    filename: str = Field(..., description="文件名")
    size: int = Field(..., description="文件大小（字节）")
    content_type: str = Field(..., description="文件类型")
    upload_time: datetime = Field(..., description="上传时间")
    file_path: Optional[str] = Field(None, description="文件路径")


class ProjectInfo(BaseModel):
    """项目信息模型"""
    project_name: str = Field(..., description="项目名称")
    project_type: Optional[str] = Field(None, description="项目类型")
    description: Optional[str] = Field(None, description="项目描述")
    created_time: Optional[datetime] = Field(None, description="创建时间")
    updated_time: Optional[datetime] = Field(None, description="更新时间")


class UserInfo(BaseModel):
    """用户信息模型"""
    user_id: Optional[int] = Field(None, description="用户ID")
    username: str = Field(..., description="用户名")
    full_name: Optional[str] = Field(None, description="全名")
    email: Optional[str] = Field(None, description="邮箱")
    department: Optional[str] = Field(None, description="部门")
    is_admin: Optional[bool] = Field(False, description="是否为管理员")


class TokenInfo(BaseModel):
    """令牌信息模型"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field("bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间（秒）")


class LoginResponse(SuccessResponse):
    """登录响应模型"""
    data: Dict[str, Union[UserInfo, TokenInfo]] = Field(..., description="登录信息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "status_code": 200,
                "message": "登录成功",
                "data": {
                    "user": {
                        "username": "testuser",
                        "full_name": "测试用户",
                        "email": "<EMAIL>"
                    },
                    "token": {
                        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                        "token_type": "bearer",
                        "expires_in": 604800
                    }
                },
                "timestamp": "2024-01-01T12:00:00"
            }
        }


class DocumentContent(BaseModel):
    """文档内容模型"""
    style: str = Field(..., description="样式标识")
    content: str = Field(..., description="文档内容")


class DocumentResponse(SuccessResponse):
    """文档生成响应模型"""
    data: List[DocumentContent] = Field(..., description="文档内容列表")
    
    class Config:
        json_schema_extra = {
            "example": {
                "status_code": 200,
                "message": "文档生成成功",
                "data": [
                    {
                        "style": "0",
                        "content": "这是生成的文档内容..."
                    }
                ],
                "timestamp": "2024-01-01T12:00:00"
            }
        }


class ExcelData(BaseModel):
    """Excel数据模型"""
    content_list: List[List[Any]] = Field(..., description="Excel内容二维列表")
    mark_list: Optional[List[Dict[str, Any]]] = Field(None, description="备注信息列表")
    merged_list: Optional[List[Dict[str, Any]]] = Field(None, description="合并单元格信息列表")


class ExcelResponse(SuccessResponse):
    """Excel生成响应模型"""
    data: ExcelData = Field(..., description="Excel数据")
    
    class Config:
        json_schema_extra = {
            "example": {
                "status_code": 200,
                "message": "表格生成成功",
                "data": {
                    "content_list": [
                        ["序号", "项目名称", "金额"],
                        [1, "示例项目", 1000000]
                    ],
                    "mark_list": [],
                    "merged_list": []
                },
                "timestamp": "2024-01-01T12:00:00"
            }
        }


class VersionInfo(BaseModel):
    """版本信息模型"""
    version: str = Field(..., description="版本号")
    release_date: datetime = Field(..., description="发布日期")
    description: Optional[str] = Field(None, description="版本描述")
    download_url: Optional[str] = Field(None, description="下载链接")
    file_size: Optional[int] = Field(None, description="文件大小")


class VersionResponse(SuccessResponse):
    """版本信息响应模型"""
    data: Union[VersionInfo, List[VersionInfo]] = Field(..., description="版本信息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "status_code": 200,
                "message": "获取版本信息成功",
                "data": {
                    "version": "1.0.0",
                    "release_date": "2024-01-01T12:00:00",
                    "description": "初始版本",
                    "download_url": "/downloads/app-v1.0.0.exe",
                    "file_size": 52428800
                },
                "timestamp": "2024-01-01T12:00:00"
            }
        }
