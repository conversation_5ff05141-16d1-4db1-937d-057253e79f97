# --coding:utf-8--
from app.templates.prompt_templates import expand_paragraph_prompt, shorten_paragraph_prompt, polish_paragraph_prompt
import json
import re
import random
import requests
from app.core.config import settings
import aiohttp
from typing import Optional, Dict, Any, Tuple
import asyncio
from app.core.logging_config import get_logger, log_llm_call

logger = get_logger(__name__)


def extract_json(input_text):
    """
    从Markdown文本中提取JSON数据。
    Args:
        markdown_text (str): 包含JSON数据的Markdown文本
    Returns:
        list: 提取到的JSON数据列表
    """
    try:
        input_text = input_text.strip()
        return [json.loads(input_text)]
    except Exception as e:
        # 如果直接解析失败，则尝试从Markdown中提取JSON，
        pattern = re.compile(
            r"```(json)?\s*([\s\S]*?)\s*```", flags=re.IGNORECASE)
        matches = pattern.findall(input_text)
        json_data_list = []
        for match in matches:
            try:
                # 解析找到的JSON内容
                json_data = json.loads(match[1].strip())
                json_data_list.append(json_data)
            except Exception as e:
                logger.error(f"无法解析JSON数据: {e}")
                logger.info(input_text)
                advice = f"大模型返回结果无法解析：{input_text[:500]}"
                return [{"description": {"score": 0, "advices": advice}}]
        if json_data_list == []:
            # 如果没有匹配到Markdown中的数据，则尝试匹配第一个“{”和最后一个“}”包裹起来的部分
            try:
                # 正则表达式：匹配以 { 开始，以 } 结尾的内容，支持换行和嵌套
                pattern = r'\{(?:[^{}]|(?R))*\}'
                matches = re.findall(pattern, input_text)
                for match in matches:
                    try:
                        json_data = json.loads(match.strip())
                        json_data_list.append(json_data)
                    except Exception as e:
                        logger.error(f"无法解析JSON数据: {e}")
                        logger.error(f"Json数据：{match}")
                        logger.info(input_text)
                        continue
            except Exception as e:
                logger.error(f"无法解析JSON数据: {e}")
                logger.info(input_text)
                advice = f"大模型返回结果无法解析：{input_text[:500]}"

        return json_data_list


def remove_thinking_content(content: str) -> str:
    """
    从LLM返回的内容中删除<think></think>标签包裹的思考内容。

    Args:
        content (str): LLM返回的原始内容

    Returns:
        str: 删除了思考内容的文本
    """
    if content is None:
        return None

    # 使用正则表达式删除<think>...</think>部分（包括标签本身）
    cleaned_content = re.sub(r'<think>[\s\S]*?</think>', '', content)
    return cleaned_content


# 创建一个全局会话对象用于复用连接
_session = None


def get_session():
    """返回一个全局共享的requests会话对象，用于连接复用"""
    global _session
    if _session is None:
        _session = requests.Session()
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=24,  # 连接池连接数
            pool_maxsize=64,      # 连接池最大连接数
            max_retries=2,        # 重试次数
            pool_block=False      # 连接池满时不阻塞，而是丢弃最旧的连接
        )
        _session.mount('http://', adapter)
        _session.mount('https://', adapter)
    return _session


def _validate_llm_config(config: Dict[str, Any], api_name: str) -> Tuple[bool, Optional[ValueError]]:
    """
    验证LLM配置的有效性

    参数:
    - config: LLM配置字典
    - api_name: API名称(用于日志)

    返回:
    - (is_valid, error): 一个元组，包含配置是否有效和错误(如果有)
    """
    required_fields = ["base_url", "api_key",
                       "model", "temperature", "max_tokens", "weight"]
    for field in required_fields:
        if field not in config:
            error = ValueError(f"API配置 ({api_name}) 缺少必要字段: {field}")
            return False, error
    if not isinstance(config.get("weight"), (int, float)) or config.get("weight") <= 0:
        error = ValueError(
            f"API配置 ({api_name}) 的 'weight' 必须是一个正数: {config.get('weight')}")
        return False, error
    return True, None


def _choose_llm_api_by_weight(configs: list[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
    """
    根据权重随机选择一个LLM API配置。
    会过滤掉无效的配置。

    参数:
    - configs: LLM配置字典列表

    返回:
    - 一个根据权重选中的LLM API配置字典，如果所有配置都无效或列表为空则返回None
    """
    valid_configs = []
    weights = []

    for config in configs:
        api_name = config.get("name", "Unnamed API")
        is_valid, validation_error = _validate_llm_config(config, api_name)
        if is_valid:
            valid_configs.append(config)
            # weight is validated to exist and be > 0
            weights.append(config["weight"])
        else:
            logger.warning(f"配置 {api_name} 无效，已从选择中排除: {validation_error}")

    if not valid_configs:
        logger.error("没有有效的LLM API配置可供选择。")
        return None

    # random.choices returns a list (k=1 means list of 1 element)
    chosen_config = random.choices(valid_configs, weights=weights, k=1)[0]
    return chosen_config


def _call_single_llm_api(config: Dict[str, Any], user_query: str) -> Tuple[bool, Optional[str], Optional[Exception]]:
    """
    调用单个LLM API

    参数:
    - config: LLM配置
    - user_query: 用户查询

    返回:
    - (success, content, error): 包含调用是否成功、内容和错误(如果有)的元组
    """
    api_name = config.get("name", "Unnamed API")

    is_valid, validation_error = _validate_llm_config(config, api_name)
    if not is_valid:
        return False, None, validation_error

    base_url = config["base_url"]
    api_key = config["api_key"]
    model = config["model"]
    temperature = config["temperature"]
    max_tokens = config["max_tokens"]

    full_url = f"{base_url.rstrip('/')}/chat/completions"
    logger.info(f"转发请求到 -> {full_url} (使用模型: {model}) for {api_name}")

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
    }

    payload = {
        "model": model,
        "messages": [{"role": "user", "content": user_query}],
        "temperature": temperature,
        "max_tokens": max_tokens,
        "stream": False  # Explicitly set stream to False
    }

    session = get_session()

    try:
        # 使用会话对象发送请求
        response = session.post(
            url=full_url, headers=headers, json=payload,
            timeout=(15, 600)  # 连接超时15秒，读取超时600秒
        )
        response.raise_for_status()
        response_data = response.json()

        content = response_data["choices"][0]["message"]["content"]
        # 删除content中的思考内容
        content = remove_thinking_content(content)
        if content is not None:
            logger.info(f"成功从 {api_name} 获取响应")
            return True, content, None
        else:
            err = ValueError(f"API ({api_name}) 返回了空内容")
            logger.warning(f"API ({api_name}) 返回的内容为空。响应数据: {response_data}")
            return False, None, err

    except Exception as e:
        # 简化的异常处理：捕获所有异常，记录基本信息
        error_message = str(e)
        error_type = type(e).__name__

        logger.warning(
            f"调用API ({api_name} at {full_url}) 失败: {error_type} - {error_message}")

        # 对某些常见异常类型添加简短说明以提高日志可读性
        if isinstance(e, requests.exceptions.JSONDecodeError):
            # It's possible 'response' is not defined if the session.post itself failed before a response was received.
            # So, we access response.text carefully.
            response_text_snippet = ""
            if 'response' in locals() and hasattr(response, 'text'):
                response_text_snippet = response.text[:200]
            logger.warning(
                f"API返回的响应无法解析为JSON。响应文本: {response_text_snippet}...")
        elif isinstance(e, (KeyError, IndexError, TypeError)):
            logger.warning(f"无法从API响应中提取所需数据")

        return False, None, e


def chat_with_llm(user_query: str) -> str:
    """
    随机调用一个配置好的LLM API的 /chat/completions 接口。
    会尝试所有配置的LLM API，直到有一个成功返回内容或全部失败。

    参数:
    - user_query (str): 用户的提问内容。

    返回:
    - 模型响应的 content (str).

    抛出:
    - ValueError: 如果没有配置LLM API。
    - Exception: 如果所有API尝试均失败，则抛出最后一次尝试遇到的相关错误。
    """
    llm_configs_list = list(settings.LLM_CONFIGURATIONS)  # Make a mutable copy

    if not llm_configs_list:
        logger.error("错误：LLM_CONFIGURATIONS 为空，没有可用的LLM API。")
        raise ValueError("错误：LLM_CONFIGURATIONS 为空，没有可用的LLM API。")

    # The number of attempts should be at most the number of available configs
    # We will try APIs one by one, selected by weight, until one succeeds or all are tried.
    # To avoid re-selecting the same failing API, we remove it from the list for subsequent attempts.

    original_config_count = len(llm_configs_list)
    last_exception = None

    for attempt_num in range(original_config_count):
        if not llm_configs_list:  # All configs have been tried or were invalid
            logger.warning("所有可用配置均已尝试或无效。")
            break

        llm_config = _choose_llm_api_by_weight(llm_configs_list)

        # No valid config could be chosen (e.g., all remaining are invalid)
        if not llm_config:
            logger.error("无法从剩余配置中选择有效的LLM API。")
            # This case should ideally be caught by the initial check or if all configs become invalid.
            # If last_exception is None, it means no API was even attempted.
            if not last_exception:
                last_exception = ValueError("没有有效的LLM API配置可供选择。")
            break

        api_name = llm_config.get("name", "Unnamed API")
        logger.info(
            f"尝试第 {attempt_num + 1}/{original_config_count} 次调用LLM API: {api_name}"
        )

        success, content, error = _call_single_llm_api(llm_config, user_query)

        if success:
            return content
        else:
            last_exception = error
            # Remove the failed/invalid config from the list for next attempts
            # This requires llm_configs_list to be a list of dicts that can be compared for removal.
            # A simple way is to remove by identity if the objects are the same,
            # or find by a unique identifier like 'name' if available and unique.
            # For simplicity, if names are unique, this works. Otherwise, need a more robust removal.
            # Assuming 'name' is sufficiently unique for this purpose or objects are distinct.
            try:
                llm_configs_list.remove(llm_config)
            except ValueError:
                # This might happen if the config was already removed or not found (e.g. if list was modified elsewhere)
                logger.warning(f"尝试移除配置 {api_name} 时未找到，可能已被移除。")

    # 如果执行到这里，说明所有API都失败了
    logger.error(f"所有 {original_config_count} 个LLM API尝试均失败。")
    if last_exception:
        raise last_exception
    else:
        raise Exception("所有LLM API尝试均失败，且未记录特定异常。这可能表示LLM配置存在问题。")


async def _async_call_single_llm_api(config: Dict[str, Any], user_query: str, session: aiohttp.ClientSession) -> Tuple[bool, Optional[str], Optional[Exception]]:
    """
    异步调用单个LLM API

    参数:
    - config: LLM配置
    - user_query: 用户查询
    - session: aiohttp会话

    返回:
    - (success, content, error): 包含调用是否成功、内容和错误(如果有)的元组
    """
    api_name = config.get("name", "Unnamed API")

    is_valid, validation_error = _validate_llm_config(config, api_name)
    if not is_valid:
        return False, None, validation_error

    base_url = config["base_url"]
    api_key = config["api_key"]
    model = config["model"]
    temperature = config["temperature"]
    max_tokens = config["max_tokens"]

    full_url = f"{base_url.rstrip('/')}/chat/completions"
    logger.info(f"转发请求到 -> {full_url} (使用模型: {model}) for {api_name}")

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
    }

    payload = {
        "model": model,
        "messages": [{"role": "user", "content": user_query}],
        "temperature": temperature,
        "max_tokens": max_tokens,
        "stream": False  # Explicitly set stream to False
    }

    try:
        # 使用timeout设置连接超时和总超时
        async with session.post(
            url=full_url, headers=headers, json=payload,
            timeout=aiohttp.ClientTimeout(total=180, connect=15)
        ) as response:
            response.raise_for_status()
            response_data = await response.json()

            content = response_data["choices"][0]["message"]["content"]
            # 删除content中的思考内容
            content = remove_thinking_content(content)
            if content is not None:
                logger.info(f"成功从 {api_name} 获取响应")
                return True, content, None
            else:
                err = ValueError(f"API ({api_name}) 返回了空内容")
                logger.warning(
                    f"API ({api_name}) 返回的内容为空。响应数据: {response_data}")
                return False, None, err

    except Exception as e:
        # 简化的异常处理：捕获所有异常，记录基本信息
        error_message = str(e)
        error_type = type(e).__name__

        logger.warning(
            f"异步调用API ({api_name} at {full_url}) 失败: {error_type} - {error_message}")

        # 对某些常见异常类型添加简短说明以提高日志可读性
        if isinstance(e, aiohttp.ContentTypeError):
            response_text_snippet = ""
            # For aiohttp.ContentTypeError, the response object might be 'e.history[-1]' if redirection happened,
            # or directly 'e' if it's a direct response error. Or it might be closed.
            # A safer way is to check if 'e.request_info' and 'e.history' exist.
            # However, accessing response text directly during an exception like this can be tricky.
            # Let's assume for now that if it's a ContentTypeError, we might not have easy access to the text
            # without potentially causing another exception if the response is already closed or malformed.
            # The error 'e' itself usually contains enough info.
            logger.warning(f"API返回的响应无法解析为JSON. Error: {e}")
        elif isinstance(e, (KeyError, IndexError, TypeError)):
            logger.warning(f"无法从API响应中提取所需数据")
        elif isinstance(e, aiohttp.ClientResponseError):
            logger.warning(f"HTTP响应错误，状态码: {getattr(e, 'status', 'unknown')}")

        return False, None, e


async def async_chat_with_llm(user_query: str) -> str:
    """
    异步版本的chat_with_llm，随机调用一个配置好的LLM API。

    参数:
    - user_query (str): 用户的提问内容。

    返回:
    - 模型响应的 content (str).

    抛出:
    - ValueError: 如果没有配置LLM API。
    - Exception: 如果所有API尝试均失败，则抛出最后一次尝试遇到的相关错误。
    """
    llm_configs_list = list(settings.LLM_CONFIGURATIONS)  # Make a mutable copy

    if not llm_configs_list:
        logger.error("错误：LLM_CONFIGURATIONS 为空，没有可用的LLM API。")
        raise ValueError("错误：LLM_CONFIGURATIONS 为空，没有可用的LLM API。")

    original_config_count = len(llm_configs_list)
    last_exception = None

    async with aiohttp.ClientSession() as session:
        for attempt_num in range(original_config_count):
            if not llm_configs_list:
                logger.warning("所有可用配置均已尝试或无效 (异步)。")
                break

            llm_config = _choose_llm_api_by_weight(llm_configs_list)

            if not llm_config:
                logger.error("无法从剩余配置中选择有效的LLM API (异步)。")
                if not last_exception:
                    last_exception = ValueError("没有有效的LLM API配置可供选择 (异步)。")
                break

            api_name = llm_config.get("name", "Unnamed API")
            logger.info(
                f"尝试第 {attempt_num + 1}/{original_config_count} 次调用LLM API (根据权重选择, 异步): {api_name}"
            )

            success, content, error = await _async_call_single_llm_api(llm_config, user_query, session)

            if success:
                return content
            else:
                last_exception = error
                try:
                    llm_configs_list.remove(llm_config)
                except ValueError:
                    logger.warning(f"尝试移除配置 {api_name} 时未找到 (异步)，可能已被移除。")

    # 如果执行到这里，说明所有API都失败了
    logger.error(f"所有 {original_config_count} 个LLM API尝试均失败 (异步)。")
    if last_exception:
        raise last_exception
    else:
        raise Exception("所有LLM API尝试均失败，且未记录特定异常。这可能表示LLM配置存在问题。")


async def distribute_queries_to_apis(queries: list) -> list:
    """
    使用多个API并行处理一批查询，平均分配查询到可用的API.
    确保返回结果的顺序与输入查询的顺序一致。

    Args:
        queries: 要处理的查询字符串列表.

    Returns:
        查询结果字符串列表，顺序与输入queries一致.
    """
    llm_configs_initial_copy = list(settings.LLM_CONFIGURATIONS)
    if not llm_configs_initial_copy:
        logger.error("LLM_CONFIGURATIONS 为空，没有可用的LLM API。")
        raise ValueError("没有可用的LLM API配置")

    num_queries = len(queries)
    # Note: The number of *available* APIs for selection might be less if some are invalid.
    # The _choose_llm_api_by_weight function handles filtering invalid configs.
    logger.info(f"开始使用配置中的API并行处理 {num_queries} 个查询 (按权重选择)...")

    # Pre-allocate results list to maintain order
    results = [None] * num_queries
    tasks = []

    async with aiohttp.ClientSession() as session:
        for i, query_content in enumerate(queries):
            if query_content is None:  # Handle cases where a query might be None
                # Construct a standard error response for None queries
                results[i] = json.dumps({
                    "description": {
                        "score": 0,
                        "advices": "查询内容为空"
                    }
                })
                continue

            # Select config by weight for each query.
            # This means for a batch of queries, different queries might hit different APIs based on weight.
            # If llm_configs_initial_copy is empty or all are invalid, _choose_llm_api_by_weight will return None.
            config = _choose_llm_api_by_weight(llm_configs_initial_copy)

            if not config:
                logger.error(f"查询索引 {i} 无法选择有效的LLM API，所有配置均无效或列表为空。")
                results[i] = json.dumps({
                    "description": {
                        "score": 0,
                        "advices": "API调用失败: 没有有效的LLM API可供选择。"
                    }
                })
                continue

            # Create a coroutine for each query and store it with its original index
            # Pass the original index `i` to the task wrapper
            task = asyncio.create_task(process_single_query_with_index(
                i, config, query_content, session))
            tasks.append(task)

        # Wait for all tasks to complete
        completed_tasks = await asyncio.gather(*tasks)

        # Populate results list based on original index
        for original_idx, success, content, error_info in completed_tasks:
            if success:
                results[original_idx] = content
            else:
                results[original_idx] = json.dumps({
                    "description": {
                        "score": 0,
                        "advices": f"API调用失败: {error_info}"
                    }
                })

    logger.info(f"完成 {num_queries} 个查询的处理。")
    return results


async def process_single_query_with_index(original_idx: int, config: Dict[str, Any], query_content: str, session: aiohttp.ClientSession):
    """
    Helper function to process a single query and return its result along with its original index.
    """
    api_name = config.get("name", "Unnamed API")
    logger.debug(f"查询索引 {original_idx} 使用API '{api_name}' 开始处理...")
    success, content, error = await _async_call_single_llm_api(config, query_content, session)
    if success:
        logger.debug(f"查询索引 {original_idx} 使用API '{api_name}' 处理成功。")
        return original_idx, True, content, None
    else:
        error_msg = str(error) if error else "未知错误"
        logger.warning(
            f"查询索引 {original_idx} 使用API '{api_name}' 处理失败: {error_msg}")
        return original_idx, False, None, error_msg


def process_text(text, option):
    """
    处理给定的文本，根据指定的选项进行扩写、缩写或润色。

    参数:
    text (str): 需要处理的文本
    option (str): 处理选项，可选值为：
        - "expand": 扩写文本
        - "shorten": 缩写文本
        - "polish": 润色文本

    返回:
    str: 经过处理后的文本

    注意:
    - 函数依赖于全局定义的提示词模板（expand_paragraph_prompt, 
      shorten_paragraph_prompt, polish_paragraph_prompt）
    - 如果提供了无效的option，函数将不会处理文本，直接返回原文

    """
    if option == "expand":  # 扩写文本
        user_query = expand_paragraph_prompt + text
    elif option == "shorten":  # 缩写文本
        user_query = shorten_paragraph_prompt + text
    elif option == "polish":  # 润色文本
        user_query = polish_paragraph_prompt + text
    else:
        return text  # 如果选项无效，返回原文

    return chat_with_llm(user_query).strip()  # 调用LLM处理文本并返回结果
