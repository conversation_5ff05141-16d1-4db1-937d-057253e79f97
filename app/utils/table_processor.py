import os
import re
import csv
import copy
import glob
import json
import pandas as pd
from docx import Document
from app.core.config import settings
from app.core.logging_config import get_logger

logger = get_logger(__name__)

# 从配置中获取上传目录
UPLOAD_DIRECTORY = settings.UPLOAD_DIRECTORY


def parse_table_data(docx_path, save2csv=True):
    """
    解析 docx 文档，提取表格信息

    :param docx_path: docx 文件的路径
    :return table_dict: 表格数据字典

    此函数执行以下操作：
    1. 读取 docx 文档
    2. 解析文档结构，提取表格内容
    3. 保存表格数据，返回表格数据字典
    """
    # 初始化
    doc = Document(docx_path)
    if not doc.tables:
        logger.error(docx_path + "文件中无表格！")
        return None
    try:
        table_dict = {}
        for index, table in enumerate(doc.tables):
            table_data = [[cell.text for cell in row.cells] for row in table.rows]
            table_dict[str(index)] = table_data
        if save2csv:
            # 保存表格数据文件
            base_path = os.path.splitext(docx_path)[0]
            table_path = f"{base_path}_table.json"
            with open(table_path, 'w', encoding='utf-8') as f:
                json.dump(table_dict, f, ensure_ascii=False, indent=4)
            logger.info(f"表格数据文件已保存至: {table_path}")
        return table_dict

    except Exception as e:
        logger.error(f"表格提取失败：{e}")
        raise ValueError("该文件提取表格失败！")


def parse_table_locations(docx_path):
    """
    从docx文档中解析表格定位信息

    Args:
        docx_path (str): docx 文件的路径
        save2csv (bool): 是否将解析结果保存为CSV文件，默认为True

    Returns:
        list: 包含所有表格单元格信息的列表，每个元素为[表格ID, 单元格内容]
    """
    # 验证文件格式
    if not docx_path.endswith('.docx'):
        raise ValueError("文件名必须以 .docx 结尾")

    # 初始化
    doc = Document(docx_path)
    table_data = []

    # 获取Word文档的主体部分
    body = doc._element.body
    # 用于追踪表格
    current_table_id = -1

    # 遍历所有块级元素
    for element in body.iter():
        # 检查元素的标签名
        tag = element.tag.split('}')[-1]

        if tag == 'tbl':
            # 遇到新表格，增加表格ID
            current_table_id += 1
            # 处理表格中的每个单元格
            for row in element.findall('.//w:tr', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}):
                for cell in row.findall('.//w:tc', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}):
                    # 提取单元格中的文本
                    cell_text = ''.join([t.text for t in cell.findall(
                        './/w:t', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})])
                    # 将表格单元格信息添加到结果中
                    table_data.append([current_table_id, cell_text.strip()])

    return table_data


def table_search(username, project_name, txt_content):
    """
    根据用户名、项目名和内容文本，从表格数据中搜索对应的表格
    :param username (str): 用户名
    :param project_name (str): 项目名
    :param txt_content (str): 要搜索的内容
    :return table_list (List): 表格数据 或 None
    """
    user_project_directory = os.path.join(UPLOAD_DIRECTORY, username, project_name)

    try:
        # 使用 glob 模块查找 _table.json 文件
        full_json_files = glob.glob(os.path.join(user_project_directory, '*_table.json'))
        if not full_json_files:
            logger.error("未找到该用户对应的项目解析文件！")
            return None
        full_path = full_json_files[0]
        with open(full_path, 'r', encoding='utf-8') as full_file:
            table_data = json.load(full_file)
        # 拆分文本内容，获取表索引
        table_id = re.findall(r'<table>(\d+)</table>', txt_content)
        return table_data[table_id[0]]
    except (IOError, json.JSONDecodeError) as e:
        logger.error(f"读取或解析文件时出错：{e}")
        return None


def delete_space(content):
    """
    删除Content列中的特殊字符。
    :param content: Content列的值
    :return: 预处理后的Content
    """
    # 使用正则表达式删除特殊字符
    return re.sub(r'[\\\ufeff\\\xa0\\\u3000]', 'nan', str(content))


def find_indices(txt_df, table_contents):
    """
    在txt.csv中查找table.csv中的Contents对应的start_index和end_index。
    :param txt_df: txt.csv的数据框
    :param table_contents: table.csv中的Contents列表
    :return: (start_index, end_index)
    """
    start_index = None
    end_index = None

    table_contents_str = ''.join(table_contents).replace(' ','')
    txt_content_list = txt_df['Content'].fillna('').tolist()

    # 遍历txt_df，查找匹配的Contents
    try:
        for i in range(len(txt_df)):
            j = i
            current_content = ""
            while j < len(txt_df) and (current_content + str(txt_content_list[j]).replace(" ", "")) in table_contents_str:
                current_content += str(txt_content_list[j]).replace(" ", "")
                if current_content == table_contents_str:
                    start_index = txt_df.at[i, 'Index']
                    end_index = txt_df.at[j, 'Index']
                    break
                j += 1

            if start_index is not None and end_index is not None:
                break
    except Exception as e:
        logger.error(f"获取定位索引失败{e}，表格内容为：{table_contents_str}")

    return start_index, end_index


def find_table_locations(docx_path, save2csv=True):
    """
    将txt.csv文件中属于同一个表格的段落合并，并用表格标记替换

    Args:
        docx_path (str): docx文件路径
        save2csv (bool): 是否保存为CSV文件，默认为True

    Returns:
        full_df (DataFrame): 更新后的段落数据
    """
    # 读取表格及全文的CSV文件
    base_path = os.path.splitext(docx_path)[0]
    try:
        # 解析文档，获取表格定位及表格数据
        parse_table_data(docx_path)
        table_df = pd.DataFrame(parse_table_locations(docx_path), columns=['TableID', 'Content'])
        full_df = pd.read_csv(f"{base_path}.csv")
        # 初始化
        txt_df = copy.deepcopy(full_df)
        results = []

        # 将Content列转换为字符串
        table_df['Content'] = table_df['Content'].astype(str)

        # 按TableID分组
        grouped = table_df.groupby('TableID')['Content'].apply(list).reset_index(name='Contents')

        # 遍历每个TableID
        for _, row in grouped.iterrows():
            table_id = row['TableID']
            table_contents = row['Contents']
            # 删除特殊符号，查找start_index和end_index
            table_contents_list = []
            for content in table_contents:
                if content in ['\ufeff', '\xa0', '\u3000']:
                    table_contents_list.append(delete_space(content))
                else:
                    table_contents_list.append(content)
            # 如果Contents为空，则直接跳过
            if table_contents_list == ['']:
                continue
            start_index, end_index = find_indices(txt_df, table_contents_list)
            if start_index is not None and end_index is not None:
                results.append({
                    'TableID': table_id,
                    'StartIndex': start_index,
                    'EndIndex': end_index
                })
            else:
                logger.error(f"未找到TableID {table_id} 对应的Contents: {table_contents_list}")

        # 合并table到full_df中
        for row in results:
            table_id = row['TableID']
            start_index = row['StartIndex']
            end_index = row['EndIndex']

            # 删除从start_index到end_index之间的记录
            full_df = full_df.drop(full_df[(full_df['Index'] >= start_index) & (full_df['Index'] <= end_index)].index)
            # 插入新的记录
            new_row = pd.DataFrame({
                'Index': start_index,
                'Level': -3,
                'Content': f'<table>{table_id}</table>'
            }, index=[0])
            full_df = pd.concat([full_df.iloc[:start_index], new_row, full_df.iloc[start_index:]]).reset_index(drop=True)

        # 重新排序Index并保存
        full_df = full_df.sort_values(by='Index').reset_index(drop=True)
        if save2csv:
            full_df.to_csv(f"{base_path}.csv", index=False)
            logger.info("csv表格已替换！")
        return full_df
    except Exception as e:
        logger.error(f"表格定位合并失败：{e}")
        return None


if __name__ == "__main__":
    # 测试代码
    from app.core.logging_config import get_logger
    logger = get_logger(__name__)

    # docx_path = '/home/<USER>/gedi/office_plugins/uploads/周嘉诚/人才/研究报告.docx'
    # parse_docx(docx_path)
    # logger.debug(docx_search('cure', '武汉大学', '项目背景'))
    # logger.debug(find_table_locations("/home/<USER>/office_plugins/uploads/cure/test/test1106.docx"))
    # docx_path = "/home/<USER>/office_plugins/uploads/cure/test1831/test1107.docx"
    # find_table_locations(docx_path, save2csv=True)
    content = "<table>2</table>\n软件需求\n<table>3</table>\n\n注：以上相关软硬件资源由南网云平台提供，本项目不安排采购。\n"
    logger.debug(table_search("cure", "test1831", content))
