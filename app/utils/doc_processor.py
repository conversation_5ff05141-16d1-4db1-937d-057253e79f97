# --coding:utf-8--
import json
from docx import Document  # 这里的 docx 实际上是 python-docx 包
import os
from docx.enum.style import WD_STYLE_TYPE
import glob
import csv
from app.utils.llm import chat_with_llm, extract_json
from app.utils.table_processor import find_table_locations, table_search
from app.utils.image_processor import find_image_locations
from app.core.config import settings
from app.templates.prompt_templates import identify_project_config_prompt
from app.core.logging_config import get_logger

logger = get_logger(__name__)

# 从配置中获取上传目录
UPLOAD_DIRECTORY = settings.UPLOAD_DIRECTORY
STATIC_URL = settings.STATIC_URL


def parse_txt_from_docx(docx_path, save2csv=True, debug_lists=False):
    """
    将docx文档解析成CSV格式的结构化数据，将有序列表转换为普通段落并保留序号

    Args:
        docx_path (str): docx 文件的路径
        save2csv (bool): 是否将解析结果保存为CSV文件，默认为True
        debug_lists (bool): 是否启用列表调试模式，默认为False

    Returns:
        list: 包含所有段落信息的列表，每个元素为[段落索引, 标题级别, 文本内容]
              - 级别: 正数表示标题级别，0表示普通段落
              - 有序列表会被转换为普通段落，序号作为文本内容的一部分

    Raises:
        ValueError: 当文件不是 .docx 格式时抛出
    """
    # 验证文件格式
    if not docx_path.endswith('.docx'):
        raise ValueError("文件名必须以 .docx 结尾")

    # 初始化文档对象
    doc = Document(docx_path)
    # 初始化结果列表
    paragraphs_data = []

    # 定义有序列表样式关键词（删除无序列表相关）
    ordered_list_keywords = ['list number', 'listnumber', '编号列表', '有序列表']
    general_list_keywords = ['list', 'listparagraph', '列表']

    # 获取Word文档的主体部分
    body = doc._element.body
    
    # 用于跟踪列表编号的计数器
    list_counters = {}  # {list_id: {level: counter}}

    # 遍历所有段落元素
    for para_index, paragraph in enumerate(body.findall('.//w:p', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})):
        # 提取文本内容
        text = ''.join([run.text for run in paragraph.findall(
            './/w:t', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})])

        # 初始化段落信息
        heading_level = 0
        is_ordered_list = False
        list_level = 0
        list_id = None
        
        # 方法1: 检查是否为有序列表项 - 查找编号属性 (XML方法)
        numPr = paragraph.find('.//w:numPr', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
        if numPr is not None:
            # 获取列表级别
            ilvl_elem = numPr.find('.//w:ilvl', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
            if ilvl_elem is not None:
                list_level = int(ilvl_elem.get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val', 0))
            
            # 获取编号ID
            numId_elem = numPr.find('.//w:numId', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
            if numId_elem is not None:
                list_id = numId_elem.get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val')
                is_ordered_list = True
                
            if debug_lists:
                logger.debug(f"XML方法发现有序列表项 - 段落 {para_index}: 级别 {list_level}, 列表ID {list_id}")
        
        # 方法2: 检查段落样式 (样式方法)
        pStyle = paragraph.find('.//w:pStyle', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
        if pStyle is not None:
            style_id = pStyle.get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val')
            try:
                style = doc.styles.get_by_id(style_id, WD_STYLE_TYPE.PARAGRAPH)
                if style:
                    style_name_lower = style.name.lower()
                    
                    if style.name.startswith('Heading'):
                        # 标题样式
                        heading_level = int(style.name.split()[-1])
                        if debug_lists:
                            logger.debug(f"发现标题 - 段落 {para_index}: 级别 {heading_level}")
                    
                    elif any(keyword in style_name_lower for keyword in ordered_list_keywords):
                        # 有序列表样式
                        is_ordered_list = True
                        # 从样式名称中提取级别
                        if 'number 2' in style_name_lower or 'list 2' in style_name_lower:
                            list_level = 1
                        elif 'number 3' in style_name_lower or 'list 3' in style_name_lower:
                            list_level = 2
                        else:
                            list_level = 0
                        # 使用样式ID作为列表ID
                        list_id = style_id
                        if debug_lists:
                            logger.debug(f"样式方法发现有序列表 - 段落 {para_index}: 级别 {list_level}, 样式 '{style.name}'")
                    
                    elif any(keyword in style_name_lower for keyword in general_list_keywords):
                        # 通用列表样式，默认为有序列表
                        is_ordered_list = True
                        list_id = style_id
                        if debug_lists:
                            logger.debug(f"样式方法发现通用列表 - 段落 {para_index}: 样式 '{style.name}'")
                            
            except KeyError:
                if debug_lists:
                    logger.debug(f"段落 {para_index} 样式ID未找到: {style_id}")

        # 处理有序列表：生成序号并合并到文本内容中
        # 只有非标题的段落才处理为有序列表，避免标题被错误添加序号
        if is_ordered_list and text.strip() and heading_level == 0:
            # 初始化列表计数器
            if list_id not in list_counters:
                list_counters[list_id] = {}
            if list_level not in list_counters[list_id]:
                list_counters[list_id][list_level] = 0

            # 重置更深层级的计数器
            for level in list(list_counters[list_id].keys()):
                if level > list_level:
                    del list_counters[list_id][level]

            # 增加当前级别的计数器
            list_counters[list_id][list_level] += 1
            current_number = list_counters[list_id][list_level]

            # 生成序号格式（根据级别使用不同格式）
            if list_level == 0:
                number_prefix = f"{current_number})"
            elif list_level == 1:
                number_prefix = f"{current_number})"
            else:
                number_prefix = f"{current_number})"

            # 将序号添加到文本内容前面
            text = f"{number_prefix} {text.strip()}"

            if debug_lists:
                logger.debug(f"转换有序列表为普通段落 - 段落 {para_index}: '{text}'")
        elif is_ordered_list and heading_level > 0 and debug_lists:
            # 调试信息：标题被识别为有序列表但不处理
            logger.debug(f"段落 {para_index} 既是标题又是有序列表，按标题处理: 级别 {heading_level}, 内容 '{text.strip()}'")

        # 确定最终的级别和类型
        if heading_level > 0:
            final_level = heading_level
        else:
            final_level = 0  # 所有非标题内容都作为普通段落

        # 将段落信息添加到结果列表
        paragraphs_data.append([para_index, final_level, text.strip()])
        
        if debug_lists and text.strip():
            logger.debug(f"  最终内容: {text.strip()}")

    # 如果需要保存为CSV文件
    if save2csv:
        import csv
        # 生成CSV文件路径（与docx文件同名同目录）
        csv_path = docx_path.rsplit('.', 1)[0] + '.csv'

        # 将结果写入CSV文件
        with open(csv_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['Index', 'Level', 'Content'])  # 写入表头
            writer.writerows(paragraphs_data)  # 写入数据

    return paragraphs_data


def parse_image_from_docx(docx_path, images_dir, images_tag, save2csv=True):
    """
    从docx文档中解析图片信息，并更新对应的CSV文件

    Args:
        docx_path (str): docx文件路径
        images_dir (str): 图片保存路径
        images_tag (str): 图片标签,将其与图片名称拼接即获得图片的URL
        save2csv (bool): 是否保存为CSV文件，默认为True

    Returns:
        list: 更新后的段落数据列表
    """
    # 获取图片位置信息
    image_locations = find_image_locations(docx_path, images_dir)

    # 构建图片索引到路径的映射
    image_index_map = {index: path for path, index in image_locations}

    # 读取现有的CSV文件
    csv_path = docx_path.rsplit('.', 1)[0] + '.csv'
    updated_data = []

    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        header = next(reader)  # 保存表头

        for row in reader:
            index, level, content = int(row[0]), row[1], row[2]
            # 如果该段落索引存在图片，且内容为空，则替换内容为图片标记
            if index in image_index_map and not content.strip():
                content = os.path.join(images_tag, image_index_map[index])
                level = -1
            updated_data.append([index, level, content])

    # 如果需要保存回CSV文件
    if save2csv:
        with open(csv_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(header)  # 写入表头
            writer.writerows(updated_data)

    return updated_data


def parse_table_from_docx(docx_path, save2csv=True):
    """
    从docx文档中解析表格信息，并更新对应的csv文件

    Args:
        docx_path (str): docx 文件的路径
        save2csv (bool): 是否将解析结果保存为CSV文件，默认为True

    Returns:
        full_df (DataFrame): 段落信息数据，每个元素为[段落索引, 标题级别, 文本内容]
    """
    # 验证文件格式
    if not docx_path.endswith('.docx'):
        raise ValueError("文件名必须以 .docx 结尾")
    # 调用table_processor函数
    paragraphs_data = find_table_locations(docx_path, save2csv=True)
    return paragraphs_data


def clear_content(content_list: list[dict])-> list[dict]:
    """
    清除docx_search列表中的style为0且content为空字符串的字典
    Args:
        content_list (list): 要处理的列表
    Returns:
        filtered_list: 处理后的列表
    """
    try:
        # 过滤掉style为0且content为空字符串的字典
        filtered_list = []
        if content_list is None:
            return content_list
        for item in content_list:
            if item['style'] in [0, '0'] and item['content'] in ["", "\n", "\n\n"]:
                continue
            filtered_list.append(item)
        # 检查列表中是否存在style为0的字典，若不存在则返回None
        if not any(item['style'] in [0, '0'] for item in filtered_list):
            logger.error("内容列表中正文为空！")
        return filtered_list
    except Exception as e:
        logger.error(f"清理内容列表发生错误: {str(e)}")
        return content_list


def docx_search(username, project_name, search_title, format='list'):
    """
    搜索文档中指定标题下的所有内容

    Args:
        username (str): 用户名
        project_name (str): 项目名称
        search_title (str): 要搜索的标题
        format (str): 返回格式，'string' 或 'list'，默认为 'list'

    Returns:
        clear_content (list): 当 format='list' 时返回字典列表，每个字典包含 'style' 和 'content' 键
        clear_txt (str): 当 format='string' 时返回字符串，所有内容以换行符连接
        None: 如果未找到匹配的标题或发生错误
    """
    user_project_directory = os.path.join(
        UPLOAD_DIRECTORY, username, project_name)

    try:
        # 查找user_project_directory目录下的所有csv文件
        csv_list = glob.glob(os.path.join(user_project_directory, '*.csv'))

        if not csv_list:
            raise ValueError("未找到CSV文件！请检查是否上传word版厂家资料！")

        csv_path = csv_list[0]

        # 存储搜索结果
        results = []
        found_title = False
        title_level = 0

        # 读取csv文件
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            next(reader)  # 跳过表头

            for row in reader:
                try:
                    index = int(row[0])
                    level = int(row[1])
                except ValueError:
                    logger.error(f"无效的整数值: {row[0]} 或 {row[1]}")
                    continue  # 跳过此行
                if row[1] == '-3':
                    row[2] = table_search(username, project_name, row[2])
                content = row[2]
                # 查找目标标题
                if not found_title and content == search_title and level > 0:
                    found_title = True
                    title_level = level
                    continue

                # 找到标题后，继续收集内容直到遇到同级或更高级的标题
                if found_title:
                    if level > 0 and level <= title_level:
                        break  # 遇到同级或更高级标题，停止收集
                    results.append(row)

        # 修改返回逻辑
        if not results:
            logger.error(f"在文档中未找到标题: {search_title}")
            return None

        if format == 'list':
            content_list = []
            for row in results:
                if row[2] == '':
                    continue
                content_list.append({"style": row[1], "content": row[2]})
            # 返回包含style和content的字典列表
            return clear_content(content_list)
        elif format == 'string':
            # 只提取文本内容并用换行符连接
            txt = ""
            for row in results:
                if type(row[2]) == list:
                    for inner_row in row[2]:
                        txt += ','.join(inner_row)
                        txt += '\n'
                else:
                    txt += row[2] + '\n'
            return txt.replace('\n\n', '\n')
        else:
            raise ValueError("format 参数必须是 'string' 或 'list'")

    except Exception as e:
        logger.warning(f"搜索文档时发生错误: {str(e)}")
        return None


def parse_config(username, project_name):
    """
    从doc/docx格式的厂家资料中解析项目必要参数

    Args:
        username (str): 用户名
        project_name (str): 项目名称

    Returns:
        dict: 解析出的项目配置
        None: 解析失败时返回
    """
    search_titles = ["项目范围", "非功能需求", "安全需求", "用户规模", "实施计划",
                     "部署方式", "系统部署方式及软硬件资源需求", "等级保护需求", "安全保护等级"]
    text_parts = []

    # 收集所有标题下的内容
    for title in search_titles:
        search_result = docx_search(
            username, project_name, title, format='string')
        if search_result:
            # 判断是否已经出现在了text_parts中
            if any(search_result in part for part in text_parts):
                continue
            if title == "非功能需求":
                # 考虑到非功能需求文字过多，只保留非功能需求中“其他要求”之前的字符
                search_result = search_result.split("其他要求\n")[0]

            text_parts.append(f"**{title}**\n{search_result}")

    # 检查是否有找到任何内容
    if not text_parts:
        logger.warning(f"在文档中未找到任何匹配的标题内容")
        return None

    # 用换行符连接所有内容
    text = "\n\n".join(text_parts)

    try:
        config_str = chat_with_llm(identify_project_config_prompt + text)
        project_config = extract_json(config_str)[0]
    except Exception as e:
        logger.error(f"LLM返回的配置不是有效的JSON格式: {e}")
        project_config = {
            "项目分类": "信息系统建设与升级改造",
            "建设周期": "一年内",
            "项目类型": "应用系统类",
            "建设性质": "升级改造",
            "系统等保级别": "三级",
            "系统用户数量": 0,
            "系统部署方式": "网一级部署模式"
        }
    finally:
        # 定义中文key到英文key的映射
        key_mapping = {
            "项目分类": "project_category",
            "建设周期": "construction_cycle",
            "项目类型": "project_type",
            "建设性质": "construction_nature",
            "系统等保级别": "system_security_level",
            "系统用户数量": "system_user_number",
            "系统部署方式": "system_deployment_mode"
        }

        # 创建新的配置字典，使用英文key
        formatted_config = {}
        for zh_key, en_key in key_mapping.items():
            if zh_key in project_config and project_config[zh_key] != "":
                formatted_config[en_key] = project_config[zh_key]
            else:
                # 如果找不到对应的中文key，使用默认值
                if en_key == "project_category":
                    formatted_config[en_key] = "信息系统建设与升级改造"
                elif en_key == "construction_cycle":
                    formatted_config[en_key] = "一年内"
                elif en_key == "project_type":
                    formatted_config[en_key] = "应用系统类"
                elif en_key == "construction_nature":
                    formatted_config[en_key] = "升级改造"
                elif en_key == "system_security_level":
                    formatted_config[en_key] = "三级"
                elif en_key == "system_user_number":
                    formatted_config[en_key] = 0
                elif en_key == "system_deployment_mode":
                    formatted_config[en_key] = "网一级部署模式"

        # 保存配置到json文件
        user_project_directory = os.path.join(
            UPLOAD_DIRECTORY, username, project_name)
        config_file = os.path.join(user_project_directory, "config.json")

        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(formatted_config, f, ensure_ascii=False, indent=4)
        logger.info(f"项目配置已保存至: {config_file}")
        return formatted_config


def save_unit_ratio(username, project_name, unit_list = [], unit_str = ""):
    """
    保存建设单位及其对应比例字典
    Args:
        username (str): 用户名
        project_name (str): 项目名称
        unit_list (list): 单位列表，每个元素为[单位名称, 比例]
    """
    try:
        # 如果只给列表，则unit_str按顿号拼接
        if unit_list and unit_str == "":
            unit_str = '、'.join(unit_list)
        # 如果只给字符串，则unit_list按字符串顿号分割
        if unit_str and unit_list == []:
            unit_list = unit_str.split('、')

        unit_dict = {}
        if len(unit_list) == 1:
            # 如果字典只有一个单位，则将其比例设置为100%
            unit_dict[unit_list[0]] = "100%"
        elif "广东电网公司" not in unit_list:
            unit_dict = {
                "公司总部": "0.66%",
                "广西电网公司": "30.66%",
                "云南电网公司": "34.48%",
                "贵州电网公司": "25.33%",
                "海南电网公司": "8.86%"
            }
        elif "深圳供电局" not in unit_list:
            unit_dict = {
                "公司总部": "0.34%",
                "广东电网公司": "48.36%",
                "广西电网公司": "15.83%",
                "云南电网公司": "17.81%",
                "贵州电网公司": "13.08%",
                "海南电网公司": "4.58%"
            }
        elif "南网超高压公司" not in unit_list:
            unit_dict = {
                "公司总部": "0.32%",
                "广东电网公司": "45.48%",
                "广西电网公司": "14.89%",
                "云南电网公司": "16.75%",
                "贵州电网公司": "12.31%",
                "海南电网公司": "4.30%",
                "深圳供电局": "5.95%"
            }
        else:
            unit_dict = {
                "公司总部": "0.30%",
                "南网超高压公司": "7.04%",
                "广东电网公司": "42.28%",
                "广西电网公司": "13.84%",
                "云南电网公司": "15.57%",
                "贵州电网公司": "11.44%",
                "海南电网公司": "4.00%",
                "深圳供电局": "5.53%"
            }

        # 保存字典到文件
        file_path = os.path.join(
            UPLOAD_DIRECTORY, username, project_name, "unit_ratio.json")
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(unit_dict, f, ensure_ascii=False, indent=4)
        logger.info(f"建设单位比例字典已保存！")

        return unit_dict
    except Exception as e:
        logger.error(f"保存建设单位比例字典时发生错误: {str(e)}")
        return {}


def read_config(username, project_name):
    """
    读取用户项目的config文件
    Args:
        username (str): 用户名
        project_name (str): 项目名称
    Returns:
        dict: 配置字典
    """
    try:
        file_path = os.path.join(
            UPLOAD_DIRECTORY, username, project_name, "config.json")
        with open(file_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except Exception as e:
        logger.error(f"读取config文件出错：{e}")
        return None


if __name__ == "__main__":
    pass
