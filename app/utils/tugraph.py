# 本文件用于存储查询图谱的相关函数
# - 结点去重 deduplicate_node
# - 结点转名称列表 node2name
# - 查询功能项实体 search_function
# - 查询项目分类 search_project
# - 验证实体是否存在 validate_node

# 导入相关依赖
from app.core.config import settings
from app.core.logging_config import get_logger
from neo4j import GraphDatabase

logger = get_logger(__name__)

# 设置初始图谱信息
tugraph_url = settings.GDB_HOST
tugraph_user = settings.GDB_USER
tugraph_password = settings.GDB_PASSWORD
if tugraph_url is None or tugraph_user is None or tugraph_password is None:
    logger.error("TuGraph环境变量未设置")
    raise ValueError("Tugraph environment variable is not set")
tugraph_bolt_url = "bolt://" + tugraph_url + ":7687"


def deduplicate_node(node_list):
    '''
    对结点列表进行去重
    Args:
        node_list (List): 结点列表
    Returns:
        distinct_list (List): 去重后的结点列表
    '''
    if node_list == []:
        return node_list
    else:
        seen_ids = set()
        distinct_list = []
        for node in node_list:
            if node.element_id not in seen_ids:
                seen_ids.add(node.element_id)
                distinct_list.append(node)
        return distinct_list


def node2name(node_list):
    '''
    将结点列表转化为结点名称列表
    Args:
        node_list (List): 结点列表
    Returns:
        name_list (List): 结点名称列表
    '''
    name_list = []
    if node_list:
        for node in node_list:
            prop_dict = node._properties
            name_list.append(prop_dict['name'])
    return name_list


def search_function(name_list):
    '''
    查询包含功能项名称的功能项结点
    Args:
        name_list: 功能项名称列表
    Returns:
        node_results (List): 相关功能项结点列表
    '''
    try:
        # 打开图谱驱动
        driver = GraphDatabase.driver(tugraph_bolt_url, auth=(tugraph_user, tugraph_password))
        # 初始化最终结果集合
        node_results = []
        # 构建Cypher查询语句模板
        cypher_query_template = """
        MATCH (n:功能项)
        WHERE n.name CONTAINS $node_name
        RETURN n LIMIT 100
        """
        with driver.session(database="能源数字知识图谱_0912") as session:
            for node_name in name_list:
                result = session.run(cypher_query_template, node_name=node_name)
                node_results.extend(record['n'] for record in result)
        return node2name(deduplicate_node(node_results))
    except Exception as e:
        raise ValueError("Tugraph search failed!" + e)


# 查询逻辑实体
def search_logic(name_list):
    '''
    查询包含逻辑实体名称的逻辑实体结点
    Args:
        name_list: 逻辑实体名称列表
    Returns:
        node_results (List): 相关逻辑实体结点列表
    '''
    try:
        # 打开图谱驱动
        driver = GraphDatabase.driver(tugraph_bolt_url, auth=(tugraph_user, tugraph_password))
        # 初始化最终结果集合
        node_results = []
        # 构建Cypher查询语句模板
        cypher_query_template = """
        MATCH (n:逻辑实体)
        WHERE n.name CONTAINS $node_name
        RETURN n LIMIT 100
        """
        with driver.session(database="能源数字知识图谱_0912") as session:
            for node_name in name_list:
                result = session.run(cypher_query_template, node_name=node_name)
                node_results.extend(record['n'] for record in result)
        return node2name(deduplicate_node(node_results))
    except Exception as e:
        raise ValueError("Tugraph search failed!" + e)


def search_project(project_type):
    '''
    查询包含项目类名称的三级项目分类
    Args:
        project_name (str): 项目类名称
    Returns:
        third_class (str): 项目库三级分类名称
    '''
    try:
        # 打开图谱驱动
        driver = GraphDatabase.driver(tugraph_bolt_url, auth=(tugraph_user, tugraph_password))
        # 初始结果集合
        node_results = []
        # 构建Cypher查询语句模板
        cypher_query_template = """
        MATCH (m:项目类)<--(n:项目库分类)
        WHERE m.name = $node_name
        RETURN n LIMIT 1
        """
        with driver.session(database="能源数字知识图谱_0912") as session:
            result = session.run(cypher_query_template, node_name=project_type)
            node_results.extend(record['n'] for record in result)
        third_class = node_results[0]._properties['name']
        return third_class
    except Exception as e:
        raise ValueError("Tugraph search failed!" + e)
    

def validate_node(node_name, node_label):
    '''
    指定标签，查询节点是否存在
    Args:
        node_name (Str): 节点名称
        node_label (Str): 节点标签
    Returns:
        validate_result (Bool): 目标节点是否存在的布尔值
    '''
    try:
        # 打开图谱驱动
        driver = GraphDatabase.driver(tugraph_bolt_url, auth=(tugraph_user, tugraph_password))
        # 初始结果集合
        node_results = []
        # 构建Cypher查询语句模板
        cypher_query_template = f"""
        MATCH (n:{node_label})
        WHERE n.name CONTAINS '{node_name}'
        RETURN n LIMIT 1
        """
        with driver.session(database="能源数字知识图谱_0912") as session:
            result = session.run(cypher_query_template)
            node_results.extend(record['n'] for record in result)
        if node_results:
            return True
        else:
            return False
    except Exception as e:
        logger.error("Tugraph search failed!" + e)
        return False


def search_func_logic(func_name_list):
    '''
    查询功能项/功能子项关联的逻辑实体名称
    Args:
        func_name_list (List): 功能项/功能子项名称列表
    Returns:
        logic_name_list (List): 功能项/功能子项关联的逻辑实体名称
    Raises:
        ValueError: 图谱查询失败
    '''
    try:
        # 打开图谱驱动
        driver = GraphDatabase.driver(tugraph_bolt_url, auth=(tugraph_user, tugraph_password))
        # 初始化最终结果集合
        logic_name_list = []
        # 构建Cypher查询语句模板
        cypher_query_template1 = """
        MATCH (n:功能项)-[r*2]->(m:逻辑实体)
        WHERE n.name = $node_name
        RETURN m
        """
        cypher_query_template2 = """
        MATCH (n:功能子项)-[r*2]->(m:逻辑实体)
        WHERE n.name = $node_name
        RETURN m
        """
        with driver.session(database="能源数字知识图谱_0912") as session:
            for node_name in func_name_list:
                if node_name == "/":
                    continue
                result = session.run(cypher_query_template1, node_name=node_name)
                logic_name_list.extend(record['m'] for record in result)
                result = session.run(cypher_query_template2, node_name=node_name)
                logic_name_list.extend(record['m'] for record in result)
        return node2name(deduplicate_node(logic_name_list))
    except Exception as e:
        logger.error("Tugraph search failed!" + e)
        return []