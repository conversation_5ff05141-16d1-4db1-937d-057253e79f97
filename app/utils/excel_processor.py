# 本文件用于存放Excel处理相关的函数
# - 插入起始行列 /insert_start_pos
# - 填充空字符串 /replace_standalone
# - 获取起止行号 /get_start_end

from app.core.config import settings
from app.core.logging_config import get_logger
import glob
import json
from openpyxl import load_workbook
import os
import pandas as pd
import re
import xlrd

# 从配置中获取上传目录
UPLOAD_DIRECTORY = settings.UPLOAD_DIRECTORY
logger = get_logger(__name__)


def insert_start_pos(df: pd.DataFrame, start_pos: tuple) -> pd.DataFrame:
    # 通过start_pos为DataFrame插入空行和空列
    df = df.copy(deep=True)
    # 插入空列
    for i in range(start_pos[1]-1):  # 插入start_pos[1]数量的空列
        df.insert(i, f'empty_col_{i}', '')  # 在最前面添加空字符串列
    # 创建含有start_pos[0]个空行的DataFrame
    empty_rows = pd.DataFrame(
        [[''] * len(df.columns)] * (start_pos[0]-1), columns=df.columns)
    # 拼接空行与原始DataFrame
    df = pd.concat([empty_rows, df], ignore_index=True)
    # 替换DataFrame中的所有空值（包括NaN和None）为空字符串''
    df.fillna('', inplace=True)
    return df


def replace_standalone(s):
    # 定义一个函数用于判断-/0并替换空字符串
    if s in ('-', '/', '0'):
        return ''
    else:
        return s


def get_start_end(df: pd.DataFrame, column_name: str):
    '''
    从DataFrame中获取指定列名的起止行号
    Args:
        df (pd.DataFrame): 输入的DataFrame
        column_name (str): 指定的列名
    Returns:
        group_index (dict): 包含起止行号的字典，格式为 {列名: (起始行号, 结束行号)}
    Raises:
        ValueError: 如果指定的列名不存在于DataFrame中
    '''
    # 按照列名进行获取各个记录对应的起止行数
    group_list = []
    group_dict = {}
    group_index = {}
    try:
        for index, row in df.iterrows():
            group_name = row[column_name]
            if group_name not in group_list:
                group_list.append(group_name)
                index_list = []
            index_list.append(index)
            group_dict[group_name] = index_list
        for group_name, index_list in group_dict.items():
            start_row = index_list[0]
            end_row = index_list[-1]
            group_index[group_name] = (start_row, end_row)
        return group_index
    except Exception as e:
        logger.error(f"获取起止行号失败：{e}")
        return None


def get_cell_value(file_path: str, sheet_name: str, target_values: list[str]) -> dict[str, any]:
    """
    从Excel文件中读取指定单元格右侧的值。支持.xls和.xlsx格式。

    Args:
        file_path (str): Excel文件的路径
        sheet_name (str): 工作表名称
        target_values (list[str]): 要查找的目标值列表

    Returns:
        dict[str, any]: 包含所有目标值及其右侧单元格值的字典，格式为 {目标值: 右侧值}

    Raises:
        FileNotFoundError: 文件不存在
        ValueError: 找不到指定的工作表或目标值
    """
    results = {value: None for value in target_values}  # 初始化结果字典

    try:
        if file_path.endswith('.xls'):
            # 处理.xls文件
            wb = xlrd.open_workbook(file_path)

            if sheet_name not in wb.sheet_names():
                raise ValueError(f"找不到'{sheet_name}'工作表")

            ws = wb.sheet_by_name(sheet_name)

            # 遍历工作表查找所有目标值
            for row in range(ws.nrows):
                for col in range(ws.ncols):
                    cell_value = ws.cell_value(row, col)
                    if cell_value in target_values:
                        if col + 1 < ws.ncols:
                            next_value = ws.cell_value(row, col + 1)
                            results[cell_value] = next_value if next_value != '' else None
        else:
            # 处理.xlsx文件
            wb = load_workbook(file_path, data_only=True)

            if sheet_name not in wb.sheetnames:
                return None

            ws = wb[sheet_name]

            # 遍历工作表查找所有目标值
            for row in ws.rows:
                for cell in row:
                    if cell.value in target_values:
                        next_cell = ws.cell(
                            row=cell.row, column=cell.column + 1)
                        results[cell.value] = next_cell.value if next_cell.value is not None else None

        return results

    except FileNotFoundError:
        raise FileNotFoundError(f"找不到文件: {file_path}")
    except Exception as e:
        raise ValueError(f"处理Excel文件时出错: {str(e)}")
    finally:
        if 'wb' in locals() and not file_path.endswith('.xls'):
            wb.close()


def get_calculation_parameters(file_path: str, sheet_name: str = "计算参数表") -> dict:
    """
    从Excel文件中批量读取计算参数表中的指定参数值。

    Args:
        file_path (str): Excel文件的路径
        sheet_name (str): 工作表名称，默认为"计算参数表"

    Returns:
        dict: 包含所有参数值的字典，格式为 {参数名: 参数值}

    Raises:
        FileNotFoundError: 文件不存在
        ValueError: 找不到指定的工作表或出现其他错误
    """
    # 定义需要获取的参数列表
    parameters = [
        "项目类型", "是否单独计列【详细设计费】", "项目分类", "系统类型",
        "变更阶段", "建设性质", "行政区域", "系统等保级别",
        "系统用户数量", "系统部署方式", "建设周期T（月）", "地域范围",
        "灾备情况", "多系统测评", "咨询人员", "需求分析工作量系数",
        "需求分析调节系数", "初步设计工作量系数", "详细设计工作量系数",
        "设计调节系数", "系统设计比例系数", "开发工作量系数",
        "集成工作量系数", "集成工作量调整系数", "咨询服务费调节系数",
        "可研深度"
    ]

    # 直接获取所有参数的值
    results = get_cell_value(file_path, sheet_name, parameters)
    return results


def read_xlsx_file(file_path: str, sheet_name: str = "应用功能", start_pos: tuple = (3, 1)):
    """
    从xlsx文件中读取指定工作表，返回pandas的dataframe类型。
    默认将第三行作为表头，从第三行开始读取数据。

    Args:
        file_path (str): Excel文件的路径
        sheet_name (str): 工作表名称
        start_pos (tuple): 起始位置，默认为(3, 1)

    Returns:
        pd.DataFrame: 包含工作表数据的DataFrame，合并单元格会被展开

    Raises:
        FileNotFoundError: 文件不存在
        ValueError: 找不到指定的工作表
    """
    try:
        # 读取Excel文件
        wb = load_workbook(filename=file_path, data_only=True)
        if sheet_name not in wb.sheetnames:
            logger.info(f"未找到工作表{sheet_name}，请检查上传文件！")
            return None

        # 读取目标工作簿
        ws = wb[sheet_name]
        # 获取合并单元格的信息
        merged_ranges = ws.merged_cells.ranges

        # 创建一个空的数据列表
        data = []
        # 遍历每一行
        for row in ws.rows:
            row_data = []
            for cell in row:
                value = cell.value
                # 检查该单元格是否在合并区域内
                for merged_range in merged_ranges:
                    if cell.coordinate in merged_range:
                        # 获取合并区域左上角单元格的值
                        value = ws.cell(merged_range.min_row,
                                        merged_range.min_col).value
                        break
                row_data.append(str(value))
            data.append(row_data)

        # 转换为DataFrame
        df = pd.DataFrame(data)
        start_row = start_pos[0]-1
        # 将第三行（索引为2）设置为列名
        if len(df) > start_row:  # 确保至少有3行数据
            df.columns = df.iloc[start_row]
            df = df.iloc[start_row+1:]  # 从第四行开始获取数据
        # 重置索引
        df = df.reset_index(drop=True)
        wb.close()
        return df
    except Exception as e:
        logger.error(f"读取xlsx文件{file_path}时出错：{e}")
        return None
    

def read_xls_file(file_path: str, sheet_name: str = "应用功能", start_pos: tuple = (3, 1)):
    """
    从xls文件中读取指定工作表，返回pandas的dataframe类型。
    默认将第三行作为表头，从第三行开始读取数据。

    Args:
        file_path (str): Excel文件的路径
        sheet_name (str): 工作表名称
        start_pos (tuple): 起始位置，默认为(3, 1)

    Returns:
        pd.DataFrame: 包含工作表数据的DataFrame，合并单元格会被展开

    Raises:
        FileNotFoundError: 文件不存在
        ValueError: 找不到指定的工作表
    """
    try:
        # 打开工作簿并选择工作表
        workbook = xlrd.open_workbook(file_path, formatting_info=True)
        sheet = workbook.sheet_by_name(sheet_name)
        
        # 获取所有合并单元格的信息
        merged_cells = sheet.merged_cells
        
        def get_cell_value(row, col):
            """获取单元格值，若单元格是合并单元，则返回其左上角单元格的值"""
            for (rlow, rhigh, clow, chigh) in merged_cells:
                if rlow <= row < rhigh and clow <= col < chigh:
                    return sheet.cell_value(rlow, clow)
            return sheet.cell_value(row, col)

        # 读取所有单元格数据到二维列表中
        data = []
        for row in range(sheet.nrows):
            row_data = []
            for col in range(sheet.ncols):
                row_data.append(str(get_cell_value(row, col)))
            data.append(row_data)
        
        # 将二维列表转换为DataFrame
        df = pd.DataFrame(data)
        
        # 按照起始位置截取DataFrame，并将起始位置所在的行作为列名
        start_row, _ = start_pos
        df.columns = df.iloc[start_row-1]
        df = df.iloc[start_row:, :]
        # 重置索引
        df.reset_index(drop=True, inplace=True)
        
        return df
    except Exception as e:
        logger.error(f"读取xls文件{file_path}时出错：{e}")
        return None


def read_app_func(username: str, project_name: str, sheet_name: str = "应用功能", start_pos: tuple = (3, 1)):
    """
    从厂家资料的《功能清单与需求对应表》中读取"应用功能"工作表，返回pandas的dataframe类型。
    默认将第三行作为表头，从第三行开始读取数据。

    Args:
        user_name (str): 用户名
        project_name (str): 格式为"项目类（具体项目名称）"
        sheet_name (str): 工作表名称
        start_pos (tuple): 起始位置，默认为(3, 1)

    Returns:
        pd.DataFrame: 包含工作表数据的DataFrame，合并单元格会被展开

    Raises:
        FileNotFoundError: 文件不存在
        ValueError: 找不到指定的工作表
    """
    try:
        # 查找user_project_directory目录下的所有xlsx文件
        user_project_directory = os.path.join(
            UPLOAD_DIRECTORY, username, project_name)
        file_list = glob.glob(os.path.join(user_project_directory, '*.xlsx'))
        if not file_list:
            file_list = glob.glob(os.path.join(user_project_directory, '*.xls'))
            if not file_list:
                return None
            else:
                file_path = file_list[0]
                df = read_xls_file(file_path, sheet_name, start_pos)
                if df is None:
                    logger.info(f"读取文件{file_path}内容为空！")
                    return None
        else:
            file_path = file_list[0]
            df = read_xlsx_file(file_path, sheet_name, start_pos)
        # 将df中的column名称去除空格
        df.columns = df.columns.str.strip()
        return df
    except Exception as e:
        logger.error(f"搜索Excel文档时发生错误: {str(e)}")
        return None


def get_merged_cells_info(username: str, project_name: str, sheet_name: str = '应用功能', start_pos: tuple = (3, 1)):
    """
    获取Excel文件中指定工作表的合并单元格信息。
    返回一个列表，列表中的每个元素是一个元组，表示一个合并单元格的坐标范围,
    元组中的四个元素分别表示合并单元格的开始行、开始列、结束行、结束列。

    Args:
        user_name (str): 用户名
        project_name (str): 格式为"项目类（具体项目名称）"
        sheet_name (str): 工作表名称
        start_pos (tuple): 起始位置，默认为(3, 1)

    Returns:
        result_list (List): 合并坐标列表

    Raises:
        FileNotFoundError: 文件不存在
        ValueError: 找不到指定的工作表
    """
    try:
        user_project_directory = os.path.join(
            UPLOAD_DIRECTORY, username, project_name)
        # 查找user_project_directory目录下的所有xlsx文件
        xlsx_list = glob.glob(os.path.join(user_project_directory, '*.xlsx'))
        if not xlsx_list:
            raise ValueError("未找到xlsx文件")
        file_path = xlsx_list[0]
        wb = load_workbook(filename=file_path, data_only=True)
        ws = wb[sheet_name]
        merged_cells_list = [(range.min_row, range.min_col, range.max_row, range.max_col)
                             for range in ws.merged_cells.ranges]
        result_list = []
        for i in range(len(merged_cells_list)):
            if merged_cells_list[i][0] >= start_pos[0] and merged_cells_list[i][1] >= start_pos[1]:
                result_list.append(merged_cells_list[i])
        return result_list
    except Exception as e:
        logger.error(f"获取合并列表时发生错误: {str(e)}")
        return None


if __name__ == "__main__":
    pass
