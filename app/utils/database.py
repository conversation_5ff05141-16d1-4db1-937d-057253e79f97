from contextlib import contextmanager
from typing import List, Dict, Any, Optional, Generator

import mysql.connector
from mysql.connector import Error
from mysql.connector.pooling import MySQLConnectionPool
from mysql.connector.connection import MySQLConnection
from app.core.logging_config import get_logger, log_database_operation

logger = get_logger(__name__)


class DatabaseManager:
    def __init__(self, host: str, port: int, user: str, password: str, database: str, pool_size: int = 5):
        """
        初始化数据库管理器。

        :param host: 数据库主机地址
        :param port: 数据库端口
        :param user: 数据库用户名
        :param password: 数据库密码
        :param database: 数据库名称
        :param pool_size: 连接池大小
        """
        self.pool: Optional[MySQLConnectionPool] = None
        self.config = {
            "host": host,
            "port": port,
            "user": user,
            "password": password,
            "database": database,
        }
        self.pool_size = pool_size

    def connect(self) -> None:
        """创建数据库连接池"""
        try:
            self.pool = MySQLConnectionPool(pool_name="mypool", pool_size=self.pool_size, **self.config)
        except Error as e:
            logger.error(f"创建MySQL连接池时出错: {e}", exc_info=True)

    def disconnect(self) -> None:
        """关闭数据库连接池中的所有连接"""
        if self.pool:
            try:
                # 尝试获取并关闭池中的所有连接
                connections = []
                try:
                    # 尝试获取所有可用连接
                    while True:
                        conn = self.pool.get_connection()
                        connections.append(conn)
                except Exception:
                    # 当无法获取更多连接时会抛出异常，这是预期行为
                    pass

                # 关闭所有获取到的连接
                for conn in connections:
                    try:
                        conn.close()
                    except Exception as e:
                        logger.warning(f"关闭连接时出错: {e}")

                # 将连接池设置为 None，让 Python 的垃圾回收机制处理
                self.pool = None
            except Exception as e:
                logger.error(f"关闭数据库连接池时出错: {e}", exc_info=True)

    @contextmanager
    def get_connection(self) -> Generator[MySQLConnection, None, None]:
        """获取数据库连接的上下文管理器"""
        if not self.pool:
            raise RuntimeError("数据库连接池未初始化")

        conn = self.pool.get_connection()
        try:
            yield conn
        finally:
            conn.close()

    @contextmanager
    def get_cursor(self, dictionary: bool = True) -> Generator[mysql.connector.cursor.MySQLCursor, None, None]:
        """获取数据库游标的上下文管理器"""
        with self.get_connection() as conn:
            # Set transaction isolation level to ensure read consistency
            try:
                conn.start_transaction(isolation_level='READ COMMITTED')
            except Exception as e:
                logger.warning(f"Setting transaction isolation level failed: {e}")

            cursor = conn.cursor(dictionary=dictionary)
            try:
                yield cursor
                conn.commit()
                logger.debug("Transaction committed successfully")
            except Error as e:
                conn.rollback()
                logger.error(f"数据库操作出错: {e}", exc_info=True)
                raise
            except Exception as e:
                conn.rollback()
                logger.error(f"非数据库错误导致回滚: {e}", exc_info=True)
                raise
            finally:
                cursor.close()

    def execute_query(self, query: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """
        执行查询并返回结果。

        :param query: SQL查询语句
        :param params: 查询参数
        :return: 查询结果列表
        """
        with self.get_cursor() as cursor:
            cursor.execute(query, params)
            return cursor.fetchall()

    def execute_update(self, query: str, params: Optional[tuple] = None) -> int:
        """
        执行更新操作并返回受影响的行数。

        :param query: SQL更新语句
        :param params: 更新参数
        :return: 受影响的行数
        """
        with self.get_cursor(dictionary=False) as cursor:
            cursor.execute(query, params)
            return cursor.rowcount

# 使用示例:
# db = DatabaseManager("localhost", "username", "password", "database_name")
# db.connect()
# results = db.execute_query("SELECT * FROM users WHERE age > %s", (25,))
# affected_rows = db.execute_update("UPDATE users SET name = %s WHERE id = %s", ("新名字", 1))
# db.disconnect()
