from typing import Any, Dict, Optional, Union, List
from fastapi.responses import JSONResponse
from fastapi import HTTPException
from pydantic import BaseModel, Field
from functools import wraps
import traceback
from app.core.logging_config import get_logger

logger = get_logger(__name__)


class APIResponse:
    """API响应类，用于统一响应格式"""
    def __init__(self, status_code: int, message: str, data: Optional[Any] = None):
        self.status_code = status_code
        self.message = message
        self.data = data

    def to_dict(self) -> Dict[str, Any]:
        return {
            "status_code": self.status_code,
            "message": self.message,
            "data": self.data
        }


class StandardResponse(BaseModel):
    """标准API响应模型"""
    status_code: int = Field(..., description="HTTP状态码", example=200)
    message: str = Field(..., description="响应消息", example="操作成功")
    data: Optional[Any] = Field(None, description="响应数据")

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "status_code": 200,
                    "message": "操作成功",
                    "data": {"result": "success"}
                },
                {
                    "status_code": 400,
                    "message": "请求参数错误",
                    "data": None
                }
            ]
        }


class ErrorResponse(BaseModel):
    """错误响应模型"""
    status_code: int = Field(..., description="HTTP错误状态码")
    message: str = Field(..., description="错误消息")
    detail: Optional[str] = Field(None, description="详细错误信息")
    error_code: Optional[str] = Field(None, description="业务错误码")

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "status_code": 400,
                    "message": "请求参数错误",
                    "detail": "缺少必需的参数: project_name",
                    "error_code": "MISSING_PARAMETER"
                },
                {
                    "status_code": 404,
                    "message": "资源不存在",
                    "detail": "指定的项目未找到",
                    "error_code": "PROJECT_NOT_FOUND"
                },
                {
                    "status_code": 500,
                    "message": "服务器内部错误",
                    "detail": "数据库连接失败",
                    "error_code": "DATABASE_ERROR"
                }
            ]
        }


class ValidationErrorResponse(BaseModel):
    """参数验证错误响应模型"""
    status_code: int = Field(422, description="参数验证错误状态码")
    message: str = Field("参数验证失败", description="错误消息")
    errors: List[Dict[str, Any]] = Field(..., description="详细验证错误信息")

    class Config:
        json_schema_extra = {
            "example": {
                "status_code": 422,
                "message": "参数验证失败",
                "errors": [
                    {
                        "loc": ["body", "username"],
                        "msg": "field required",
                        "type": "value_error.missing"
                    }
                ]
            }
        }


# 标准HTTP状态码响应模型映射
RESPONSE_MODELS = {
    200: {"model": StandardResponse, "description": "请求成功"},
    201: {"model": StandardResponse, "description": "创建成功"},
    400: {"model": ErrorResponse, "description": "请求参数错误"},
    401: {"model": ErrorResponse, "description": "未授权访问"},
    403: {"model": ErrorResponse, "description": "权限不足"},
    404: {"model": ErrorResponse, "description": "资源不存在"},
    409: {"model": ErrorResponse, "description": "资源冲突"},
    422: {"model": ValidationErrorResponse, "description": "参数验证失败"},
    500: {"model": ErrorResponse, "description": "服务器内部错误"}
}


def create_error_response(status_code: int, message: str, detail: str = None, error_code: str = None) -> ErrorResponse:
    """创建标准错误响应"""
    return ErrorResponse(
        status_code=status_code,
        message=message,
        detail=detail,
        error_code=error_code
    )


def api_response(f):
    """API响应装饰器，统一处理响应格式和异常"""
    @wraps(f)
    async def decorated(*args, **kwargs):
        try:
            result = await f(*args, **kwargs)
            if isinstance(result, APIResponse):
                return JSONResponse(content=result.to_dict(), status_code=result.status_code)
            return result
        except HTTPException as e:
            # 处理FastAPI的HTTPException
            logger.warning(f"HTTP异常: {e.status_code} - {e.detail}")
            error_response = create_error_response(
                status_code=e.status_code,
                message=str(e.detail),
                detail=getattr(e, 'detail', None)
            )
            return JSONResponse(
                content=error_response.dict(),
                status_code=e.status_code
            )
        except Exception as e:
            # 处理未预期的异常
            logger.error(f"未处理的异常: {str(e)}", exc_info=True)
            error_response = create_error_response(
                status_code=500,
                message="服务器内部错误",
                detail=str(e),
                error_code="INTERNAL_ERROR"
            )
            return JSONResponse(
                content=error_response.dict(),
                status_code=500
            )
    return decorated
