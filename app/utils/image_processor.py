import os
from docx import Document
from docx.oxml.ns import qn
import re


def find_image_locations(docx_path, images_dir):
    """
    查找文档中所有图片的位置并将图片保存到指定目录

    Args:
        docx_path: Word文档路径
        images_dir: 图片保存路径
    Returns:
        list: 包含所有图片位置信息的列表，每个元素为 [图片路径, 段落索引]
    """
    if os.path.exists(images_dir):
        # 如果文件夹存在，删除其中的所有文件
        for file in os.listdir(images_dir):
            file_path = os.path.join(images_dir, file)
            if os.path.isfile(file_path):
                os.remove(file_path)
    else:
        # 如果文件夹不存在，则递归创建
        os.makedirs(images_dir)

    doc = Document(docx_path)

    # 创建图片ID到文件名的映射
    image_types = ('.emf', '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.wmf')
    images = {}
    for rel_id, rel in doc.part.rels.items():
        if "image" in rel.target_ref and rel.target_ref.lower().endswith(image_types):
            # 保存图片
            image_data = rel.target_part.blob
            image_filename = os.path.basename(rel.target_ref)
            image_path = os.path.join(images_dir, image_filename)

            with open(image_path, 'wb') as f:
                f.write(image_data)

            # 更新图片路径为保存后的本地路径
            images[rel_id] = image_filename

    # 遍历所有段落查找图片位置
    found_locations = []  # 存储找到的图片位置

    # 获取文档的body元素
    body = doc._element.body

    # 遍历所有段落
    for para_index, paragraph in enumerate(body.findall('.//w:p', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})):
        try:
            # 检查所有可能包含图片的元素
            # 1. 检查内联图片和绘图对象
            for blip in paragraph.findall('.//a:blip', {'a': 'http://schemas.openxmlformats.org/drawingml/2006/main'}):
                image_rid = blip.get(qn('r:embed'))
                if image_rid in images:
                    found_locations.append([images[image_rid], para_index])

            # 2. 检查VML图片
            for imagedata in paragraph.findall('.//v:imagedata', {'v': 'urn:schemas-microsoft-com:vml'}):
                image_rid = imagedata.get(qn('r:href')) or imagedata.get(qn('r:id'))
                if image_rid in images:
                    found_locations.append([images[image_rid], para_index])

            # 3. 检查OLE对象
            for ole in paragraph.findall('.//o:OLEObject', {'o': 'urn:schemas-microsoft-com:office:office'}):
                image_rid = ole.get(qn('r:id'))
                if image_rid in images:
                    found_locations.append([images[image_rid], para_index])

            # 4. 检查shape对象
            for shape in paragraph.findall('.//v:shape', {'v': 'urn:schemas-microsoft-com:vml'}):
                for imagedata in shape.findall('.//v:imagedata', {'v': 'urn:schemas-microsoft-com:vml'}):
                    image_rid = imagedata.get(qn('r:href')) or imagedata.get(qn('r:id'))
                    if image_rid in images:
                        found_locations.append([images[image_rid], para_index])

            # 5. 检查图片引用
            for pic in paragraph.findall('.//pic:pic', {'pic': 'http://schemas.openxmlformats.org/drawingml/2006/picture'}):
                for blip in pic.findall('.//a:blip', {'a': 'http://schemas.openxmlformats.org/drawingml/2006/main'}):
                    image_rid = blip.get(qn('r:embed'))
                    if image_rid in images:
                        found_locations.append([images[image_rid], para_index])

            # 6. 检查所有可能的关系引用
            for element in paragraph.iter():
                for key, value in element.attrib.items():
                    if 'id' in key.lower() and value in images:
                        found_locations.append([images[value], para_index])

        except Exception:
            continue

    # 去重并返回结果
    return list(map(list, set(map(tuple, found_locations))))


def get_image_type(image_path):
    """
    获取图片类型

    Args:
        image_path: 图片路径

    Returns:
        str: 图片类型 ('emf', 'png', 'jpg' 等)
    """
    return os.path.splitext(image_path)[1][1:].lower()


def extract_image_paths(text: str) -> list:
    """
    从文本中提取所有图片路径

    Args:
        text (str): 包含图片标签的文本

    Returns:
        list: 图片路径列表
    """
    return re.findall(r'<image>(.*?)</image>', text)


if __name__ == "__main__":
    from app.core.logging_config import get_logger
    logger = get_logger(__name__)

    docx_path = "/home/<USER>/gedi/office_plugins/uploads/zhang/测试项目/客户服务平台（现货结算、违约金、电力负控设备等优化）建设项目可行性研究报告.docx"
    locations = find_image_locations(docx_path)

    # 按图片类型分组显示结果
    image_types = {}
    for loc in locations:
        img_type = get_image_type(loc[0])
        if img_type not in image_types:
            image_types[img_type] = []
        image_types[img_type].append(loc)

    for img_type, locs in image_types.items():
        logger.debug(f"{img_type.upper()} 格式图片:")
        for loc in locs:
            logger.debug(f"图片路径: {loc[0]}, 段落索引: {loc[1]}")
