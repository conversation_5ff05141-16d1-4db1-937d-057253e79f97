"""
统一日志配置模块

提供项目统一的日志配置和记录器实例
支持不同环境的日志级别和输出格式
"""

import logging
import logging.handlers
import sys
from datetime import datetime
import json
from pathlib import Path
from urllib.parse import unquote


class JSONFormatter(logging.Formatter):
    """简化的JSON格式日志格式化器"""

    def format(self, record):
        # 基础日志信息
        log_entry = {
            "time": datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S'),
            "level": record.levelname,
            "message": record.getMessage(),
        }

        # 只在非INFO级别时添加位置信息
        if record.levelname != 'INFO':
            log_entry["location"] = f"{record.module}:{record.funcName}:{record.lineno}"

        # 添加异常信息
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)

        # 添加重要的额外字段
        if hasattr(record, 'user_id') and record.user_id:
            log_entry["user_id"] = record.user_id
        if hasattr(record, 'request_id') and record.request_id:
            log_entry["request_id"] = record.request_id
        if hasattr(record, 'duration') and record.duration:
            log_entry["duration"] = record.duration
        if hasattr(record, 'status_code') and record.status_code:
            log_entry["status_code"] = record.status_code
        if hasattr(record, 'method') and record.method:
            log_entry["method"] = record.method
        if hasattr(record, 'url') and record.url:
            log_entry["url"] = record.url
        if hasattr(record, 'client_ip') and record.client_ip:
            log_entry["client_ip"] = record.client_ip

        return json.dumps(log_entry, ensure_ascii=False, separators=(',', ':'))


class ColoredFormatter(logging.Formatter):
    """简化的带颜色控制台日志格式化器"""

    # ANSI颜色代码
    COLORS = {
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }

    def __init__(self):
        super().__init__()
        # 检测是否支持颜色输出
        self.use_colors = self._supports_color()

    def _supports_color(self):
        """检测终端是否支持颜色输出"""
        import os

        # 如果强制禁用颜色
        if os.environ.get('NO_COLOR'):
            return False

        # 如果强制启用颜色
        if os.environ.get('FORCE_COLOR'):
            return True

        # 检查是否是交互式终端
        if hasattr(sys.stdout, 'isatty') and sys.stdout.isatty():
            return True

        # 检查常见的支持颜色的终端环境变量
        term = os.environ.get('TERM', '')
        if 'color' in term or term in ['xterm', 'xterm-256color', 'screen', 'linux']:
            return True

        return False

    def format(self, record):
        # 格式化时间（只显示时分秒）
        time_str = datetime.fromtimestamp(record.created).strftime('%H:%M:%S')

        if self.use_colors:
            # 添加颜色
            color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
            reset = self.COLORS['RESET']

            # 简化的格式：只在非INFO级别时显示位置信息
            if record.levelname == 'INFO':
                formatted = f"{color}[{time_str}] {record.levelname}{reset} {record.getMessage()}"
            else:
                formatted = f"{color}[{time_str}] {record.levelname}{reset} {record.module}:{record.funcName} - {record.getMessage()}"
        else:
            # 无颜色格式
            if record.levelname == 'INFO':
                formatted = f"[{time_str}] {record.levelname} {record.getMessage()}"
            else:
                formatted = f"[{time_str}] {record.levelname} {record.module}:{record.funcName} - {record.getMessage()}"

        # 添加异常信息
        if record.exc_info:
            formatted += f"\n{self.formatException(record.exc_info)}"

        return formatted


class AccessLogFormatter(logging.Formatter):
    """简化的API访问日志格式化器"""

    def __init__(self):
        super().__init__()
        # 检测是否支持颜色输出
        self.use_colors = self._supports_color()

    def _supports_color(self):
        """检测终端是否支持颜色输出"""
        import os

        # 如果强制禁用颜色
        if os.environ.get('NO_COLOR'):
            return False

        # 如果强制启用颜色
        if os.environ.get('FORCE_COLOR'):
            return True

        # 检查是否是交互式终端
        if hasattr(sys.stdout, 'isatty') and sys.stdout.isatty():
            return True

        # 检查常见的支持颜色的终端环境变量
        term = os.environ.get('TERM', '')
        if 'color' in term or term in ['xterm', 'xterm-256color', 'screen', 'linux']:
            return True

        return False

    def format(self, record):
        # 格式化时间（只显示时分秒）
        time_str = datetime.fromtimestamp(record.created).strftime('%H:%M:%S')

        # 简化uvicorn访问日志格式
        message = record.getMessage()

        # 解析uvicorn访问日志格式: "IP:PORT - "METHOD PATH HTTP/VERSION" STATUS"
        # 例如: ***********:49723 - "POST /docs_api/status HTTP/1.1" 200 OK
        if ' - "' in message and '" ' in message:
            try:
                parts = message.split(' - "', 1)
                rest = parts[1]

                request_parts = rest.split('" ', 1)
                request_info = request_parts[0]  # METHOD PATH HTTP/VERSION
                status_info = request_parts[1] if len(request_parts) > 1 else ""

                # 提取方法和路径
                request_elements = request_info.split(' ')
                if len(request_elements) >= 2:
                    method = request_elements[0]
                    path = request_elements[1]
                    status = status_info.split()[0] if status_info else "?"

                    # URL解码路径中的中文字符
                    try:
                        decoded_path = unquote(path, encoding='utf-8')
                    except Exception:
                        # 如果解码失败，使用原始路径
                        decoded_path = path

                    if self.use_colors:
                        # 根据状态码选择颜色
                        if status.startswith('2'):  # 2xx 成功
                            status_color = '\033[32m'  # 绿色
                        elif status.startswith('3'):  # 3xx 重定向
                            status_color = '\033[34m'  # 蓝色
                        elif status.startswith('4'):  # 4xx 客户端错误
                            status_color = '\033[33m'  # 黄色
                        elif status.startswith('5'):  # 5xx 服务器错误
                            status_color = '\033[31m'  # 红色
                        else:
                            status_color = '\033[0m'   # 默认

                        # 简化格式：[时间] API 方法 路径 状态码
                        formatted = f"\033[36m[{time_str}] API\033[0m {method} {decoded_path} {status_color}{status}\033[0m"
                    else:
                        # 无颜色格式
                        formatted = f"[{time_str}] API {method} {decoded_path} {status}"

                    return formatted
            except:
                pass

        # 如果解析失败，使用默认格式
        if self.use_colors:
            return f"\033[36m[{time_str}] API\033[0m {message}"
        else:
            return f"[{time_str}] API {message}"


class LoggingConfig:
    """日志配置类"""
    
    def __init__(self, app_mode: str = "dev", log_dir: str = "logs"):
        self.app_mode = app_mode.lower()
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 根据环境设置日志级别
        import os
        log_format = os.environ.get('LOG_FORMAT', '').lower()

        if self.app_mode == "prod":
            self.log_level = logging.INFO
            # 如果强制使用人类可读格式，则在控制台也显示INFO级别
            if log_format in ['colored', 'human']:
                self.console_level = logging.INFO
            else:
                self.console_level = logging.WARNING
        else:  # dev 和 test 环境都使用 INFO 级别
            self.log_level = logging.INFO
            self.console_level = logging.INFO
    
    def setup_logging(self):
        """设置日志配置"""
        # 获取根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)

        # 清除现有的处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 设置控制台处理器
        self._setup_console_handler(root_logger)

        # 设置文件处理器
        self._setup_file_handlers(root_logger)
        
        # 设置第三方库的日志级别
        self._setup_third_party_loggers()

        # 设置特殊的过滤器
        self._setup_log_filters()

        return root_logger
    
    def _setup_console_handler(self, logger):
        """设置控制台处理器"""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.console_level)

        # 检查是否有专门的日志格式控制环境变量
        import os
        log_format = os.environ.get('LOG_FORMAT', '').lower()

        if log_format == 'json':
            # 强制使用JSON格式
            console_handler.setFormatter(JSONFormatter())
        elif log_format == 'colored' or log_format == 'human':
            # 强制使用彩色格式
            console_handler.setFormatter(ColoredFormatter())
        else:
            # 使用默认逻辑：生产环境JSON，其他环境彩色
            if self.app_mode == "prod":
                console_handler.setFormatter(JSONFormatter())
            else:
                console_handler.setFormatter(ColoredFormatter())

        logger.addHandler(console_handler)
    
    def _setup_file_handlers(self, logger):
        """设置文件处理器"""
        # 应用日志文件（所有级别）
        app_log_file = self.log_dir / "app.log"
        app_handler = logging.handlers.RotatingFileHandler(
            app_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        app_handler.setLevel(self.log_level)
        app_handler.setFormatter(JSONFormatter())
        logger.addHandler(app_handler)

        # 错误日志文件（只记录ERROR和CRITICAL）
        error_log_file = self.log_dir / "error.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=10,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(JSONFormatter())
        logger.addHandler(error_handler)
        
        # 访问日志文件（专门记录HTTP请求）
        access_log_file = self.log_dir / "access.log"
        self.access_handler = logging.handlers.RotatingFileHandler(
            access_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        self.access_handler.setLevel(logging.INFO)
        self.access_handler.setFormatter(JSONFormatter())
    
    def _setup_third_party_loggers(self):
        """设置第三方库的日志级别"""
        # 设置第三方库的日志级别，避免过多的调试信息
        third_party_loggers = {
            # Web框架相关
            'uvicorn': logging.WARNING if self.app_mode == "prod" else logging.INFO,
            'uvicorn.access': logging.INFO,  # 显示API请求，但使用简洁格式
            'uvicorn.error': logging.WARNING if self.app_mode == "prod" else logging.INFO,
            'fastapi': logging.WARNING if self.app_mode == "prod" else logging.INFO,

            # 数据库相关
            'mysql.connector': logging.WARNING,

            # HTTP客户端
            'urllib3': logging.WARNING,
            'requests': logging.WARNING,
            'aiohttp': logging.WARNING,

            # 图像处理库 - 完全禁用DEBUG和INFO日志
            'PIL': logging.ERROR,
            'PIL.Image': logging.ERROR,
            'PIL.PngImagePlugin': logging.ERROR,
            'PIL.JpegImagePlugin': logging.ERROR,
            'PIL.TiffImagePlugin': logging.ERROR,
            'PIL.BmpImagePlugin': logging.ERROR,
            'PIL.GifImagePlugin': logging.ERROR,
            'PIL.WebPImagePlugin': logging.ERROR,
            'PIL.IcoImagePlugin': logging.ERROR,
            'PIL.TgaImagePlugin': logging.ERROR,
            'PIL.PdfImagePlugin': logging.ERROR,

            # 密码处理库 - 完全禁用DEBUG和INFO日志
            'passlib': logging.ERROR,
            'passlib.registry': logging.ERROR,
            'passlib.handlers': logging.ERROR,
            'passlib.handlers.bcrypt': logging.ERROR,
            'passlib.handlers.sha2_crypt': logging.ERROR,
            'passlib.utils': logging.ERROR,
            'passlib.utils.compat': logging.ERROR,

            # 其他第三方库
            'python_multipart': logging.WARNING if self.app_mode == "prod" else logging.INFO,
            'python_multipart.multipart': logging.WARNING,
            'neo4j': logging.WARNING,
            'neo4j.pool': logging.WARNING,
            'neo4j.io': logging.WARNING,
            'asyncio': logging.WARNING,
        }

        for logger_name, level in third_party_loggers.items():
            third_party_logger = logging.getLogger(logger_name)
            third_party_logger.setLevel(level)

            # 对于PIL和passlib库，完全禁用传播到根记录器
            if logger_name.startswith(('PIL', 'passlib')):
                third_party_logger.propagate = False

            # 为uvicorn相关的日志器设置统一格式
            if logger_name.startswith('uvicorn'):
                # 清除现有的处理器
                for handler in third_party_logger.handlers[:]:
                    third_party_logger.removeHandler(handler)

                # 添加我们的控制台处理器
                console_handler = logging.StreamHandler()
                console_handler.setLevel(level)

                # 检查是否有专门的日志格式控制环境变量
                import os
                log_format = os.environ.get('LOG_FORMAT', '').lower()

                if log_format == 'json':
                    # 强制使用JSON格式
                    console_handler.setFormatter(JSONFormatter())
                elif log_format == 'colored' or log_format == 'human':
                    # 强制使用彩色格式
                    if logger_name == 'uvicorn.access':
                        console_handler.setFormatter(AccessLogFormatter())
                    else:
                        console_handler.setFormatter(ColoredFormatter())
                else:
                    # 使用默认逻辑：生产环境JSON，其他环境彩色
                    if self.app_mode == "prod":
                        console_handler.setFormatter(JSONFormatter())
                    else:
                        # 为访问日志使用专门的格式化器
                        if logger_name == 'uvicorn.access':
                            console_handler.setFormatter(AccessLogFormatter())
                        else:
                            console_handler.setFormatter(ColoredFormatter())

                third_party_logger.addHandler(console_handler)

                # 禁用传播，避免重复日志
                third_party_logger.propagate = False

    def _setup_log_filters(self):
        """设置日志过滤器，专门过滤PIL和passlib的嘈杂日志"""

        class PILFilter(logging.Filter):
            """专门过滤PIL库的嘈杂日志"""

            def __init__(self):
                super().__init__()
                # PIL相关的嘈杂消息模式
                self.pil_noise_patterns = [
                    'STREAM b\'',
                    'Importing',
                    'Image: failed to import',
                    'unknown)',
                    'call',
                    '_open',
                ]

            def filter(self, record):
                """过滤PIL相关的嘈杂日志"""
                # 只允许ERROR级别的PIL日志通过
                if record.name.startswith('PIL'):
                    return record.levelno >= logging.ERROR
                return True

        class PasslibFilter(logging.Filter):
            """专门过滤passlib库的嘈杂日志"""

            def __init__(self):
                super().__init__()
                # passlib相关的嘈杂消息模式
                self.passlib_noise_patterns = [
                    'loaded lazy attr',
                    'registered',
                    'trapped unexpected response',
                    'backend lacks',
                    'enabling workaround',
                ]

            def filter(self, record):
                """过滤passlib相关的嘈杂日志"""
                # 只允许ERROR级别的passlib日志通过
                if record.name.startswith('passlib'):
                    return record.levelno >= logging.ERROR
                return True

        # 为根记录器添加过滤器
        root_logger = logging.getLogger()
        root_logger.addFilter(PILFilter())
        root_logger.addFilter(PasslibFilter())

        # 为PIL相关的所有记录器设置严格过滤
        pil_loggers = [
            'PIL', 'PIL.Image', 'PIL.PngImagePlugin', 'PIL.JpegImagePlugin',
            'PIL.TiffImagePlugin', 'PIL.BmpImagePlugin', 'PIL.GifImagePlugin',
            'PIL.WebPImagePlugin', 'PIL.IcoImagePlugin', 'PIL.TgaImagePlugin',
            'PIL.PdfImagePlugin'
        ]

        for logger_name in pil_loggers:
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.ERROR)
            logger.propagate = False

        # 为passlib相关的所有记录器设置严格过滤
        passlib_loggers = [
            'passlib', 'passlib.registry', 'passlib.handlers',
            'passlib.handlers.bcrypt', 'passlib.handlers.sha2_crypt',
            'passlib.utils', 'passlib.utils.compat'
        ]

        for logger_name in passlib_loggers:
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.ERROR)
            logger.propagate = False
    
    def get_access_logger(self):
        """获取访问日志记录器"""
        access_logger = logging.getLogger('access')
        access_logger.setLevel(logging.INFO)
        access_logger.addHandler(self.access_handler)
        # 防止日志传播到根记录器
        access_logger.propagate = False
        return access_logger


def get_logger(name: str = None) -> logging.Logger:
    """
    获取日志记录器实例
    
    Args:
        name: 日志记录器名称，通常使用 __name__
        
    Returns:
        logging.Logger: 日志记录器实例
    """
    return logging.getLogger(name)


def setup_logging(app_mode: str = "dev", log_dir: str = "logs") -> logging.Logger:
    """
    设置项目日志配置
    
    Args:
        app_mode: 应用模式 (dev/test/prod)
        log_dir: 日志文件目录
        
    Returns:
        logging.Logger: 根日志记录器
    """
    config = LoggingConfig(app_mode, log_dir)
    return config.setup_logging()


def get_access_logger() -> logging.Logger:
    """获取访问日志记录器"""
    return logging.getLogger('access')


# 日志记录的便捷函数
def log_request(method: str, url: str, status_code: int, duration: float, 
                client_ip: str = None, user_id: str = None, request_id: str = None):
    """记录HTTP请求日志"""
    access_logger = get_access_logger()
    access_logger.info(
        f"{method} {url} - {status_code} - {duration:.2f}ms",
        extra={
            'method': method,
            'url': url,
            'status_code': status_code,
            'duration': duration,
            'client_ip': client_ip,
            'user_id': user_id,
            'request_id': request_id
        }
    )


def log_database_operation(operation: str, table: str, duration: float = None, 
                          user_id: str = None, success: bool = True):
    """记录数据库操作日志"""
    logger = get_logger('database')
    level = logging.INFO if success else logging.ERROR
    message = f"Database {operation} on {table}"
    if duration:
        message += f" - {duration:.2f}ms"
    
    logger.log(level, message, extra={
        'operation': operation,
        'table': table,
        'duration': duration,
        'user_id': user_id,
        'success': success
    })


def log_llm_call(model: str, prompt_length: int, response_length: int = None, 
                 duration: float = None, success: bool = True, error: str = None):
    """记录LLM调用日志"""
    logger = get_logger('llm')
    level = logging.INFO if success else logging.ERROR
    
    if success:
        message = f"LLM call to {model} - prompt: {prompt_length} chars, response: {response_length} chars"
        if duration:
            message += f" - {duration:.2f}ms"
    else:
        message = f"LLM call to {model} failed: {error}"
    
    logger.log(level, message, extra={
        'model': model,
        'prompt_length': prompt_length,
        'response_length': response_length,
        'duration': duration,
        'success': success,
        'error': error
    })
