# 本文件主要存放prompt模板


# 扩写文段的prompt
expand_paragraph_prompt = """
/no_think
给你一段文字，请你对其进行扩写并直接返回扩写后的结果，不要添加任何的markdown标记，也不要生成任何其他不相关的内容。\n
下面是需扩写的正文：
"""

# 缩写文段的prompt
shorten_paragraph_prompt = """
/no_think
给你一段文字，请你对其进行缩写并直接返回缩写后的结果，不要添加任何的markdown标记，也不要生成任何其他不相关的内容。\n
下面是需缩写的正文：
"""

# 润色文段的prompt
polish_paragraph_prompt = """
/no_think
给你一段文字，请你对其进行润色并直接返回润色后的结果，不要添加任何的markdown标记，也不要生成任何其他不相关的内容。\n
下面是需润色的正文：
"""

# 生成背景的prompt
background_generate_prompt = """
/no_think
<role>你是一名资深的电力领域项目可行性研究报告编写专家。</role>
<task>针对给定的项目资料，整理生成项目背景。</task>
<requirement>1. 请按照可行性研究报告的口吻丰富扩写项目背景的正文内容，不包含标题；2. 不增加、删改其他与项目背景无关的内容；3. 不使用markdown形式进行标记。</requirement>\n
下面是项目相关资料：
"""

# 生成目标的prompt
objective_generate_prompt = """
/no_think
<role>你是一名资深的电力领域项目可行性研究报告编写专家。</role>
<task>针对给定的项目资料，整理生成项目目标。</task>
<requirement>1. 请按照可行性研究报告的口吻生成项目目标正文内容，不包含标题；2. 不增加、删改其他与项目目标无关的内容；3. 不使用markdown形式进行标记；4. 要求使用总分结构进行叙述，首先描述项目响应的相关要求，后续分点描述项目建设的目标，不要分点太多。</requirement>\n
下面是项目相关资料：
"""

# 生成现状分析的prompt
polish_current_prompt = """
/no_think
给你项目名称以及需要润色的现状分析原始段落，请你对原始段落进行润色并直接返回润色后的结果，不要添加任何的markdown标记，也不要生成任何其他不相关的内容。
下面是项目名称以及原始段落：
"""

# 生成业务范围的prompt
business_scope_prompt = """
/no_think
你是一名资深的电力领域项目可行性研究报告编写专家，现在给你项目相关资料，请按照可行性研究报告的口吻凝练出项目业务范围，并对业务范围进行分点表述，不增加删改其他与业务范围无关的内容，不使用markdown形式进行标记，。\n
下面是项目相关资料：
"""

# 抽取功能项和逻辑文件的prompt
extract_function_logical = """
<role>你是一名资深的电力领域项目可行性研究报告编写专家。</role>
<task>针对给定的资料，提取出功能项和逻辑文件。</task>
<requirement>1、提取的功能项和逻辑文件名称尽量简短凝练；2、严格按照给定JSON格式示例进行回答；3、答案中仅包括json格式的数据，不增加删除任何文字；4、若未涉及相关功能项和逻辑文件则返回对应空数组；</requirement>
<example_input>项目资料：本项目与电网管理平台集成操作票和工作票、工器具、人身风险等数据；与数字身份认证平台集成人员当量等数据；通过人工智能平台，调用未佩戴安全帽识别组件、未穿着工作服识别组件、压板状态识别组件等；<example_input>
<example_output>{"功能项":["未佩戴安全帽识别","未穿着工作服识别","压板状态识别"],"逻辑文件":["操作票","工作票","工器具","人身风险","人员当量"]}</example_output>
"""

# 抽取最大用户数和最大并发数的prompt
extract_user_scope = """
<role>你是一名资深的电力领域项目可行性研究报告编写专家。</role>
<task>针对给定的资料，提取出最大用户数和最大并发数。</task>
<requirement>1、严格按照给定JSON格式示例进行回答；2、答案中仅包括json格式的数据，不增加删除任何文字；3、若未涉及相关描述则返回对应空值；</requirement>
<example_input>项目资料：1）支持最大用户数要求大于等于100，并行用户数要求大于等于40。
2)当系统进行用户操作时，响应时间应满足如下要求：系统登录及首页访问平均响应时间在3秒之内显示完整内容；业务分析统计页面响应时间小于8秒；复杂的业务处理（主要包括数据量较大、逻辑复杂的批量数据更新和复杂的统计查询处理业务等）后台响应时间不超过20秒，后台应用处理高峰时<25秒，并且应有明确的界面提示信息。<example_input>
<example_output>{"最大用户数":"100","最大并发数":"40"}</example_output>
"""

identify_type_prompt = """
/no_think
<role>你是一名资深的电力领域项目可行性研究报告编写专家。</role>
<task>针对给定的资料，识别出该项目是否为新建项目。</task>
<requirement>1、严格按照“是”和“否”进行回答；2、答案中仅包括json格式的数据，不增加删除任何文字；3、若未涉及相关描述则返回“否”；</requirement>
<example_input>为落实国家战略部署，承接公司数字化转型要求，落实公司市场营销域高质量发展的重点任务，服务公司“十四五”期间改革发展，加快市场营销域数字化转型，公司在秉承数字化转型的方针下，深度应用基于云平台的互联网、人工智能、大数据、物联网等新技术，整合资源，建立生态圈，开展电网管理平台（项目管理应用V2.0-营销项目管理优化）建设，立足公司电网管理平台，公司市场营销部《南方电网公司营销项目数字化管理提升工作方案》明确指出“充分利用电网管理平台、施工管控平台、施工管控APP等数字化手段，推进营销项目全过程规范、透明、闭环管控。通过推动系统应用，不断发现问题，解决问题，持续优化系统功能，提升系统实用化水平，更好服务营销项目管理”。</example_input>
<example_output>否</example_output>
下面是项目相关信息，请识别是否为新建项目：
"""

# 提取业务能力的prompt
extract_business_capability_prompt = """
请你从给定的文本中提取出所有的**业务能力**，并以列表形式返回。\n
下面是一个示例，供你参考：\n
<example_input>
本项目涉及业务能力为两票管理、作业计划管理、需求预测、招标方案确定、招标、仓库建设、仓库出入库、逆向物资回收，业务能力架构如图1所示。
</example_input>
<example_output>
['两票管理','作业计划管理','需求预测','招标方案确定','招标','仓库建设','仓库出入库','逆向物资回收']
</example_output>
\n\n
下面是你需要从中提取业务能力的文本：
"""

# 对必要性结论进行润色的prompt
polish_necessity_conclusion_prompt = """
/no_think
<role>你是一名资深的电力领域项目可行性研究报告编写专家。</role>
<task>现在提供给你《某电力领域项目可行性报告》的**必要性结论**部分，请你按照书面化、专业化的风格对其进行润色，并直接返回你润色后的内容，不要添加多余的提示。</task>
<requirement>1、仅润色"style": "0"内的内容；2、不要添加其他额外信息，如多余的标点、括号等；3、优化后的内容要更加专业和简洁，确保符合项目可研报告的标准。</requirement>
<content>{content}</content>
"""

# 生成必要性结论的prompt
generate_necessity_conclusion_prompt = """
/no_think
<role>你是一名资深的电力领域项目可行性研究报告编写专家。</role>\n
<task>现在提供给你《某电力领域项目可行性研究报告》的部分内容，请你根据这些内容撰写报告的**必要性结论**，要求如下：</task>\n
<requirement>1、直接返回必要性结论，不要添加任何多余的文字或符号，不要包含任何markdown形式的标记；2、字数控制在1000字左右，可以分2到3个段落；3、最后一个段落需要给出总体性的结论，例如“本项目符合国家和企业的战略要求，充分调研了项目现状和问题，围绕项目建设目标，深度分析了业务需求。通过项目建设将充分发挥南网智瞰数字化技术优势，加快推进电网设备数字化、生产业务数字化，促进公司数字电网高质量发展，因此，本项目的建设是非常必要的。”（该例子仅供参考）</requirement>\n
下面是《某电力领域项目可行性研究报告》的部分内容，请你根据这些内容撰写报告的**必要性结论**。\n
"""

# 对开发范围进行润色的prompt
polish_development_scope_prompt = """
/no_think
<role>你是一名资深的电力领域项目可行性研究报告编写专家。</role>
<task>现在提供给你《某电力领域项目可行性报告》的**开发范围**部分，请你按照书面化、专业化的风格对其进行润色，并直接返回你润色后的内容，不要添加多余的提示。</task>
<requirement>1、仅润色"style": "0"内的内容；2、不要添加其他额外信息，如多余的标点、括号等；3、优化后的内容要更加专业和简洁，确保符合项目可研报告的标准。</requirement>
<content>{content}</content>
"""

# 生成开发范围的prompt
generate_development_scope_prompt = """
/no_think
<role>你是一名资深的电力领域项目可行性研究报告编写专家。</role>\n
<task>现在提供给你《某电力领域项目可行性研究报告》的部分内容，请你根据这些内容撰写报告的**开发范围**，要求如下：</task>\n
<requirement>1、直接返回开发范围，不要添加任何多余的文字或符号，不要包含任何markdown形式的标记；2、字数控制在500字左右，可以分1到2个段落；3、开发范围需要包含项目的主要目标、所涉及的功能模块等。</requirement>\n
下面是《某电力领域项目可行性研究报告》的部分内容，请你根据这些内容撰写报告的**开发范围**,并以“本项目开发范围为开头”。\n
"""

# 生成管理效益分析的prompt
generate_management_benefit_prompt = """
/no_think
<role>你是一名资深的电力领域项目可行性研究报告编写专家。</role>\n
<task>现在提供给你《某电力领域项目可行性研究报告》的部分内容，请你根据这些内容撰写报告的**管理效益分析**，要求如下：</task>\n
<requirement>1、直接返回管理效益分析，不要添加任何多余的文字或符号，不要包含任何markdown形式的标记，以“本项目”开头；2、字数控制在400字左右，可以分1到2个段落；3、分析内容需涵盖项目的管理效益等方面。</requirement>\n
下面是《某电力领域项目可行性研究报告》的部分内容，请你根据这些内容撰写报告的**管理效益分析**。\n
"""

# 生成经济效益分析的prompt
generate_economic_benefit_prompt = """
/no_think
<role>你是一名资深的电力领域项目可行性研究报告编写专家。</role>\n
<task>现在提供给你《某电力领域项目可行性研究报告》的部分内容，请你根据这些内容撰写报告的**经济效益分析**，要求如下：</task>\n
<requirement>1、直接返回经济效益分析，不要添加任何多余的文字或符号，不要包含任何markdown形式的标记，以“本项目”开头；2、字数控制在400字左右，可以分1到2个段落；3、分析内容需涵盖项目的经济效益、成本效益、投资回报等方面。</requirement>\n
下面是《某电力领域项目可行性研究报告》的部分内容，请你根据这些内容撰写报告的**经济效益分析**。\n
"""

# 生成社会效益分析的prompt
generate_social_benefit_prompt = """
/no_think
<role>你是一名资深的电力领域项目可行性研究报告编写专家。</role>\n
<task>现在提供给你《某电力领域项目可行性研究报告》的部分内容，请你根据这些内容撰写报告的**社会效益分析**，要求如下：</task>\n
<requirement>1、直接返回社会效益分析，不要添加任何多余的文字或符号，不要包含任何markdown形式的标记，以“本项目”开头；2、字数控制在400字左右，可以分1到2个段落；3、分析内容需涵盖项目的社会效益、环境影响、可持续性等方面，特别是项目对社会发展的推动作用，如何提升公众福利、环保方面的影响、项目的社会责任感以及对当地经济和社会的长期影响。</requirement>\n
下面是《某电力领域项目可行性研究报告》的部分内容，请你根据这些内容撰写报告的**社会效益分析**。\n
"""

# 优化可研结论的prompt
feasibility_summary_prompt = """
/no_think
<role>假定你是一名资深的电力领域项目可行性研究报告编写专家。</role>
<task>针对以下文本内容，帮助优化润色，并且避免添加任何无关的部分，内容仅需优化提升，不要返回其他多余的文字或符号。</task>
<requirement>1. 优化文本内容；2. 不要添加其他额外信息，如多余的标点、括号等；3. 优化后的内容要更加专业和简洁，确保符合项目可研报告的标准。</requirement>
下面是需要优化的文本内容：
"""

# 生成可研结论的prompt
generate_feasibility_conclusion_prompt = """
<role>你是一名资深的电力领域项目可行性研究报告编写专家。</role>\n
<task>请根据提供的内容撰写某项目的可行性结论，要求如下：</task>\n
<requirement>
1. 结论要总结项目的可行性，并清晰表述项目的实施价值，直接返回项目可研结论，不要添加任何多余的文字或符号，不要包含任何markdown形式的标记；
2. 字数控制在1000字左右，可以从技术可行性、业务可行性、经济可行性、管理可行性等角度分为几个段落；
3. 最后应给出明确的结论，例如：“综上所述，本项目的建设符合国家和企业的战略要求，针对现状进行了深入分析，符合市场需求，具有高度可行性……”（此为参考内容）。
</requirement>\n
下面是《某电力领域项目可行性研究报告》的部分内容，请你根据这些内容撰写项目的可行性结论。\n
"""

# 识别项目类型的prompt
identify_project_type_prompt = """
<role>你是一名资深的电力领域数字化项目可行性研究报告编写专家。</role>\n
<task>现在提供给你《某电力领域项目可行性研究报告》的部分内容，请你根据这些内容识别出项目的类型。</task>\n
<requirement>1、直接返回项目类型（**一个项目有且只有一个项目类型**），不要添加任何多余的文字或符号，不要包含任何markdown形式的标记；\n
2、项目类型包括：\n
- 信息系统建设与升级改造
- 信息专题研究
</requirement>\n\n
下面是《某电力领域项目可行性研究报告》的部分内容，请你根据这些内容识别出项目的类型。\n
"""

# 识别项目参数的prompt
identify_project_config_prompt = """
<role>你是一名资深的电力领域数字化项目可行性研究报告编写专家。</role>\n 
<task>现在提供给你《某电力领域项目可行性研究报告》的部分内容，请你根据这些内容识别出项目的必要参数。</task>\n 
项目必要参数包括：\n
- **项目分类**：分为**信息系统建设与升级改造**、**信息基础设施建设与升级改造**、**信息安全防护体系建设与升级改造**、**运行维护**、**信息专题研究**共五个选项，单选。若无法识别，则默认为**信息系统建设与升级改造**。\n
- **建设周期**：分为**一年内**、**一年以上两年内**、**两年内**共三个选项，单选。若无法识别，则默认为**一年内**。\n
- **项目类型**：分为**应用系统类**、**技术支持平台**、**数据分析应用**、**移动应用类**、**其他**共五个选项，单选。若无法识别，则默认为**应用系统类**。 \n
- **建设性质**：分为**新建**、**升级改造**共两个选项，单选。若无法识别，则默认为**升级改造**。\n
- **系统等保级别**：分为**一级（不含互联网用户）**、**一级（含互联网用户）**、**二级（不含互联网用户）**、**二级（含互联网用户）**、**三级**、**四级**共六个选项，单选。若无法识别，则默认为**三级**。\n
- **系统用户数量**：单位为人，通常根据内部用户和外部用户数量综合判断。若无法判断，则默认为0。 \n
- **系统部署方式**：分为**网一级部署模式**、**网省两级部署模式**、**省一级部署模式**、**省地两级部署模式**、**省地县三级部署模式**、**网一级管理节点和省一级分节点部署模式**共六个选项，单选。若无法识别，则默认为**网一级部署模式**。\n

<requirement>以**JSON格式**返回识别结果，不要添加任何多余的文字或符号，不要包含任何markdown形式的标记。
例如：{"项目分类":"信息系统建设与升级改造","建设周期":"一年内","项目类型":"应用系统类","建设性质":"升级改造","系统等保级别":"二级（含互联网用户）","系统用户数量":5000,"系统部署方式":"网一级部署模式"}。</requirement>\n 
<task>下面是《某电力领域项目可行性研究报告》的部分内容，请你根据这些内容识别出项目的必要参数。</task>\n\n

"""


identify_three_points_prompt_0311 = """
假如你是一名专业的电力领域报告编写人员，请对下述一段功能描述进行三点判断，请你首先对功能描述的内容进行评价，要求给出取值为[1-10]的评分以及对应建议，然后对外部输入、外部输出、外部查询三点进行更改新增判断，以JSON格式返回，不要使用任何其它格式如markdown等：
功能点估算法核心：EI（外部输入）：对数据进行维护或改变系统状态/行为的事务；EO（外部输出）：对数据加工后呈现或输出的事务；EQ（外部查询）：对已有数据直接呈现或输出的事务。

1.功能描述的修改意见，如描述模糊等，但不要对缺失的信息进行提示。
2.外部输入的操作项清单、对应的更改数量、对应的新增数量
3.外部输出的操作项清单、对应的更改数量、对应的新增数量
4.外部查询的操作项清单、对应的更改数量、对应的新增数量

JSON格式如下：
{
    "description": {
        "score": 8,
        "advices": "content"
    },
    "external_inputs": {
        "name": ["input_1", "input_2", "input_3"],
        "new": 1,
        "changed": 2
    },
    "external_outputs": {
        "name": ["output_1", "output_2", "output_3"],
        "new": 3,
        "changed": 0
    },
    "external_inquiries": {
        "name": ["inquiry_1", "inquiry_2", "inquiry_3"],
        "new": 2,
        "changed": 1
    }
}

<requirement>
1.你只需要直接返回JSON数据，不要添加任何文字描述和其他形式如markdown的回答。
2.如果功能描述中内容写得歧义太多、不是该功能的内容，如给了言情小说的文字内容，请将评分设置为0分并给出建议。
3.内容中识别有内部逻辑文件，但没有识别到任何外部输入、输出以及查询操作项，请将评分设置为0分并给出建议。
4.内容中没有涉及对应的内容，请返回对应的空列表以及0，不要返回样例。
5.所有名称请用中文，不要用英文。
6.请确认内部逻辑文件与外部逻辑文件是名词而不是动词。
</requirement>

下面是具体的功能描述：
"""


identify_five_points_prompt_0311 = """
请对下述一段功能描述进行五点法功能点估算判断，请你首先对功能描述的内容进行评价，要求给出取值为[1-10]的评分以及对应建议，然后对内部逻辑文件、外部接口文件、外部输入、外部输出、外部查询五点进行更改新增判断，以JSON格式返回，不要使用任何其它格式如markdown等：
功能点估算法核心：​​ILF（内部逻辑文件）​​本功能维护的业务数据，比如电商平台的订单库、学生管理系统的学生信息，如果涉及调用人工智能大模型（知识图谱、通用算法、小模型不算大模型），要固定增加“模型调用配置信息”和“模型输出结果信息”两个ILF；​​EIF（外部接口文件）​​是引用别人系统的数据，比如天气预报软件调用的气象局数据、调用某个大模型、调用某个知识库，EIF名称后缀不要叫“接口”，可以是XX数据、XX信息、XX模型、XX知识库；​​EI（外部输入）​​是用户操作改数据或系统状态，比如提交注册表单，像检索、查询条件输入不改变内部逻辑文件数据的不能算外部输入，如果涉及调用人工智能大模型（知识图谱、通用算法、小模型不算大模型）不用，要固定增加“模型配置信息增、删、改”3个EI；​​EO（外部输出）​​是系统加工后的复杂结果，比如生成带图表的销售统计报表，如果涉及调用人工智能大模型（知识图谱、通用算法、小模型不算大模型），要固定增加“输出大模型生成结果”1个EO；​​EQ（外部查询）​​是直接查数据展示，比如输入书名查图书馆库存、导出表格。

1.功能描述的修改意见，如描述模糊等，但不要对缺失的信息进行提示。
2.内部逻辑文件清单、对应的更改数量、对应的新增数量
3.外部接口文件清单、对应的更改数量、对应的新增数量
4.外部输入的操作项清单、对应的更改数量、对应的新增数量
5.外部输出的操作项清单、对应的更改数量、对应的新增数量
6.外部查询的操作项清单、对应的更改数量、对应的新增数量

JSON格式如下：
{
    "description": {
        "score": 8,
        "advices": "content"
    },
    "internal_logical_files": {
        "name": ["file_1", "file_2", "file_3"],
        "new": 2,
        "changed": 1
    },
    "external_interface_files": {
        "name": ["file_1", "file_2", "file_3"],
        "new": 2,
        "changed": 1
    },
    "external_inputs": {
        "name": ["input_1", "input_2", "input_3"],
        "new": 1,
        "changed": 2
    },
    "external_outputs": {
        "name": ["output_1", "output_2", "output_3"],
        "new": 3,
        "changed": 0
    },
    "external_inquiries": {
        "name": ["inquiry_1", "inquiry_2", "inquiry_3"],
        "new": 2,
        "changed": 1
    }
}

<requirement>
1.你只需要直接返回JSON数据，不要添加任何文字描述和其他形式如markdown的回答。
2.如果功能描述中内容写得歧义太多、不是该功能的内容，如给了言情小说的文字内容，请将评分设置为0分。
3.内容中识别有内部逻辑文件，但没有识别到任何外部输入、输出以及查询操作项，请将评分设置为0分。
4.内容过于冗长重复内容过多，请将评分设置为0分，并在建议部分指出对应的重复内容。
5.内容中没有涉及对应的内容，请返回对应的空列表以及0，不要返回样例。
6.所有文件名称请用中文，不要用英文。
7.请确认外部接口文件是名词而不是动词。
</requirement>

下面是具体的功能描述：
"""

identify_EIF = """
请对下述一段集成功能描述进行功能点估算判断，请你首先对功能描述的内容进行评价，要求给出取值为[1-10]的评分以及对应建议，然后对描述中可能的外部接口文件进行更改新增的判断，以JSON格式返回，不要使用任何其它格式如markdown等：
​​EIF（外部接口文件）​​是引用别人系统的数据，比如天气预报软件调用的气象局数据、调用某个大模型、调用某个知识库，EIF名称后缀不要叫“接口”，可以是XX数据、XX信息、XX模型、XX知识库；

1.功能描述的修改意见，如描述模糊等，但不要对缺失的信息进行提示。
2.内部逻辑文件清单、对应的更改数量、对应的新增数量
3.外部接口文件清单、对应的更改数量、对应的新增数量
4.外部输入的操作项清单、对应的更改数量、对应的新增数量
5.外部输出的操作项清单、对应的更改数量、对应的新增数量
6.外部查询的操作项清单、对应的更改数量、对应的新增数量

JSON格式如下：
{
    "description": {
        "score": 8,
        "advices": "content"
    },
    "external_interface_files": {
        "name": ["file_1", "file_2", "file_3"],
        "new": 2,
        "changed": 1
    },
}

<requirement>
1.你只需要直接返回JSON数据，不要添加任何文字描述和其他形式如markdown的回答。
2.如果功能描述中内容写得歧义太多、不是该功能的内容，如给了言情小说的文字内容，请将评分设置为0分。
3.内容中识别有内部逻辑文件，但没有识别到任何外部输入、输出以及查询操作项，请将评分设置为0分。
4.内容过于冗长重复内容过多，请将评分设置为0分，并在建议部分指出对应的重复内容。
5.内容中没有涉及对应的内容，请返回对应的空列表以及0，不要返回样例。
6.所有文件名称请用中文，不要用英文。
7.请确认外部接口文件是名词而不是动词。
</requirement>

下面是具体的功能描述：
"""


deduplicate_prompt = """
<role>你是一名资深的电力领域数字化项目文件或者接口识别专家。</role>
<task>现在提供给你一个文件列表，请你根据列表中的文件或者接口名称（按照逗号分割）进行去重，将所有表意、语义以及逻辑上疑似重复的文件或者接口名称进行提炼统一为一个文件，然后标识出可能重复的其他文件。</task>
<requirement>
1、提炼出文件或者接口名称；
2、标记出可能重复的文件名称；
3、对不可能重复的文件不做处理，对仅出现一次的文件且没有可能重复的文件不做处理；
4、请忽略空字符串以及未知文件；
5、汇总成一个json格式，严格按照json格式输出而不能添加任何额外的文字描述以及其他如markdown格式等
</requirement>
<example_input>
["历史问答记录","历史问答内容", "用户历史问答", "用户历史对话，不相关的文件1", "不相关的文件2"]
</example_input>
<example_output>
{
    "历史问答记录": "历史问答信息", 
    "历史问答内容": "历史问答信息",
    "用户历史问答": "历史问答信息",
    "用户历史对话": "历史问答信息"
}
</example_output>

下面是一个文件列表，请你根据列表中的内部逻辑文件名称进行去重：
"""


mark_desc_prompt = """
请对下述一段功能描述进行评价，要求给出取值为[1-10]的评分以及对应建议，以JSON格式返回，不要使用任何其它格式如markdown等：
1.功能描述的修改意见，如描述模糊等，但不要对缺失的信息进行提示。

JSON格式如下：
{
    "description": {
        "score": 8,
        "advices": "content"
    }
}

<requirement>
1.你只需要直接返回JSON数据，不要添加任何文字描述和其他形式如markdown的回答。
2.如果功能描述中内容写得歧义太多、不是该功能的内容，如给了言情小说的文字内容，请将评分设置为0分。
3.内容中识别有内部逻辑文件，但没有识别到任何外部输入、输出以及查询操作项，请将评分设置为0分。
4.内容过于冗长，重复内容过多，请将评分设置为0分，并在建议部分指出对应的重复内容。
5.内容中没有涉及对应的功能描述，请返回对应的空建议以及0，不要返回样例。
</requirement>

下面是具体的功能描述：
"""
