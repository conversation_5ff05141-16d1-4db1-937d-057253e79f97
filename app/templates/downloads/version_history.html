<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>有解助手 - 版本历史</title>
    <!-- 从本地加载CSS资源，同时添加回退方案 -->
    <link rel="stylesheet" href="/static/downloads/css/bootstrap.min.css" onerror="useInlineStyles()">
    <link rel="stylesheet" href="/static/downloads/css/bootstrap-icons.css" onerror="useInlineIconStyles()">
    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-light: #eff6ff;
            --accent-color: #2563eb;
            --border-color: #e5e7eb;
            --highlight-color: #f0f9ff;
            --text-color: #1f2937;
            --text-secondary: #6b7280;
        }
        
        body {
            background-color: #f9fafb;
            color: var(--text-color);
            font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: white;
            padding: 2.5rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 15px 15px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .container {
            max-width: 980px;
            margin: 0 auto;
            padding: 0 15px;
        }
        
        .text-center {
            text-align: center;
        }
        
        .mb-2 {
            margin-bottom: 0.5rem;
        }
        
        .mb-4 {
            margin-bottom: 1.5rem;
        }
        
        .pb-5 {
            padding-bottom: 3rem;
        }
        
        h1 {
            margin: 0;
            font-weight: 600;
            font-size: 2rem;
        }
        
        .version-table {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            background: white;
        }
        
        .table-responsive {
            display: block;
            width: 100%;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        
        .table {
            width: 100%;
            margin-bottom: 0;
            color: #212529;
            border-collapse: collapse;
        }
        
        .table-bordered {
            border: 1px solid var(--border-color);
        }
        
        .align-middle {
            vertical-align: middle;
        }
        
        .mb-0 {
            margin-bottom: 0 !important;
        }
        
        .table thead th {
            background: #f3f4f6;
            font-weight: 600;
            text-align: center;
            padding: 1rem 0.75rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }
        
        /* Override Bootstrap's thick top border on tbody following a thead */
        .table > thead + tbody {
            border-top-width: 1px !important;
            border-top-color: var(--border-color) !important;
        }
        
        .table tbody td {
            vertical-align: middle;
            text-align: center;
            padding: 1rem 0.75rem;
            border-color: var(--border-color);
            border: 1px solid var(--border-color);
        }
        
        .table tbody tr {
            transition: all 0.2s ease;
        }
        
        .table tbody tr:hover {
            background-color: var(--highlight-color);
        }
        
        .version-badge {
            display: inline-block;
            padding: 0.35rem 0.75rem;
            border-radius: 999px;
            font-weight: 600;
            background-color: var(--primary-light);
            color: var(--primary-color);
            font-size: 0.9rem;
            line-height: 1;
        }
        
        .current-version-row {
            background-color: var(--highlight-color);
        }
        
        .text-success {
            color: #10b981;
        }
        
        .mt-1 {
            margin-top: 0.25rem;
        }
        
        .download-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s ease;
            padding: 0.25rem 0.6rem;
            border-radius: 6px;
            background-color: rgba(59, 130, 246, 0.1);
            width: auto;
            margin: 0 auto;
            font-size: 0.85rem;
        }
        
        .download-link:hover {
            background-color: rgba(59, 130, 246, 0.2);
            color: var(--accent-color);
            transform: translateY(-1px);
        }
        
        /* 基础图标样式，以防图标库无法加载 */
        .bi {
            display: inline-block;
            vertical-align: -0.125em;
            width: 1em;
            height: 1em;
            font-size: 1rem;
        }
        
        .bi-download::before {
            content: "";
            margin-right: 0;
            display: none;
        }
        
        .bi-download {
            display: none;
        }
        
        .bi-check-circle-fill::before {
            content: "✓";
            margin-right: 0.35rem;
            color: #10b981;
        }
        
        .bi-info-circle-fill::before {
            content: "ⓘ";
            margin-right: 0.5rem;
            color: #3b82f6;
            display: inline-block;
            vertical-align: middle;
        }
        
        .changelog-col {
            text-align: left;
            white-space: pre-line;
            max-width: 450px;
            font-size: 0.95rem;
            color: #4b5563;
        }
        
        .footer-note {
            font-size: 0.85rem;
            color: var(--text-secondary);
            margin-top: 1rem;
            text-align: center;
        }
        
        .current-version-indicator {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1.25rem;
            background-color: #fff3cd;
            border-radius: 8px;
            border: 1px solid #ffeeba;
            color: #856404;
            font-size: 0.95rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .current-version-indicator i {
            color: #ff9800;
            margin-right: 0.75rem;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
        }
        
        .bi-info-circle-fill::before {
            content: "ⓘ";
            margin-right: 0.5rem;
            color: #ff9800;
            display: inline-block;
            vertical-align: middle;
        }
        
        /* 响应式布局 */
        @media (max-width: 768px) {
            .container {
                padding: 0 10px;
            }
            
            .table thead th, 
            .table tbody td {
                padding: 0.75rem 0.5rem;
            }
            
            .changelog-col {
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="page-header">
        <div class="container">
            <h1 class="text-center mb-2">有解助手版本历史</h1>
        </div>
    </div>
    
    <div class="container pb-5">
        <div class="version-table mb-4">
            <div class="table-responsive">
                <table class="table table-bordered align-middle mb-0">
                    <thead>
                        <tr>
                            <th style="width: 120px;">版本号</th>
                            <th style="width: 150px;">发布日期</th>
                            <th>主要特性/变化</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for version in versions %}
                        <tr class="{% if version.is_latest %}current-version-row{% endif %}">
                            <td>
                                <span class="version-badge">{{ version.version }}</span>
                                {% if version.is_latest %}
                                <div style="margin-top: 0.5rem;">
                                    <a href="/frontend/downloads/download/{{ version.version }}" class="download-link">
                                        <i class="bi bi-download"></i> 下载
                                    </a>
                                </div>
                                {% endif %}
                            </td>
                            <td>{{ version.release_date }}</td>
                            <td class="changelog-col">{{ version.changelog }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="footer-note">
            <div class="current-version-indicator">
                <i class="bi bi-info-circle-fill"></i> 安装前请关闭正在运行的Office应用
            </div>
        </div>
    </div>

    <!-- 从本地加载JS资源，同时添加回退方案 -->
    <script src="/static/downloads/js/bootstrap.bundle.min.js" onerror="console.log('Bootstrap JS could not be loaded')"></script>
    
    <!-- 内联脚本，确保页面在无网络环境下也能正常工作 -->
    <script>
        function useInlineStyles() {
            console.log("Bootstrap CSS failed to load, using inline styles");
            // Add fallback styling if bootstrap.min.css fails to load
            // ... existing code if any ...
        }
        
        function useInlineIconStyles() {
            console.log("Bootstrap Icons CSS failed to load, using inline icon styles");
            // Make sure icons are visible even if bootstrap-icons.css fails to load
            document.querySelectorAll('.bi-download').forEach(icon => {
                icon.style.display = 'none';
            });
            document.querySelectorAll('.download-link').forEach(link => {
                link.style.justifyContent = 'center';
                link.style.width = '80px';
                link.style.margin = '0 auto';
            });
            document.querySelectorAll('.bi-info-circle-fill').forEach(icon => {
                icon.textContent = 'ⓘ';
                icon.style.color = '#ff9800';
                icon.style.marginRight = '0.75rem';
                icon.style.display = 'inline-flex';
                icon.style.alignItems = 'center';
                icon.style.fontSize = '1.1rem';
                icon.style.verticalAlign = 'middle';
            });
            document.querySelectorAll('.bi-check-circle-fill').forEach(icon => {
                icon.textContent = '✓';
                icon.style.color = '#10b981';
                icon.style.marginRight = '0.35rem';
            });
        }
    </script>
</body>
</html> 