from fastapi import APIRouter
from app.excel_api.endpoints import table0, table1, table2, table3, table3_1, table3_2, table4, table5, table678, parameter_table, other_table, check_table
router = APIRouter()

router.include_router(table0.router, prefix="/table0", tags=["封面、编制说明"])
router.include_router(table1.router, prefix="/table1", tags=["表一（项目估算汇总表）"])
router.include_router(table2.router, prefix="/table2", tags=["表二（项目分项汇总投资表）"])
router.include_router(table3.router, prefix="/table3", tags=["表三（项目分项估算表）"])
router.include_router(table4.router, prefix="/table4", tags=["表四（其他费用估算表）"])
router.include_router(table5.router, prefix="/table5", tags=["表五（分阶段投资估算表）"])
router.include_router(table678.router, prefix="/table678", tags=["表六（软件设备汇总表）、表七（设备服务租赁汇总表）、表八（需求变更费用对比表）"])
router.include_router(table3_1.router, prefix="/table3_1", tags=["表三附1（开发、集成工作量测算）"])
router.include_router(table3_2.router, prefix="/table3_2", tags=["表三附2（实施工作量）"])
router.include_router(parameter_table.router, prefix="/parameter_table", tags=["计算参数表"])
router.include_router(other_table.router, prefix="/other_table", tags=["生成分摊单位表格"])
router.include_router(check_table.router, prefix="/check", tags=["检查Excel厂家资料"])
