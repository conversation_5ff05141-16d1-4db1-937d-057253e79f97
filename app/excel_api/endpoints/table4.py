# 本文件用于存放以下接口：
# - 生成表四（其他费用估算表）
from fastapi import APIRouter, HTTPException
from typing import List
from app.utils.response import APIResponse, api_response
from app.docs_api.schemas import ExcelRequest
from app.core.config import settings
import os
import glob
import pandas as pd
import xlrd
import json
import openpyxl
from app.core.logging_config import get_logger
from openpyxl import load_workbook

UPLOAD_DIRECTORY = settings.UPLOAD_DIRECTORY

router = APIRouter()
logger = get_logger(__name__)


def get_len(user_path, file_name):
    """
    获取json文件的长度。
    Args:
        file_path (str): json文件的路径。
    Returns:
        int: json文件的长度。
    """
    try:
        table_path = os.path.join(user_path, file_name)
        table_df = pd.read_json(table_path, orient='records')
        return len(table_df)
    except Exception as e:
        logger.error(f"获取表格{file_name}长度失败: {e}")
        return 0


def convert_unit(unit_list):
    """
    将单位列表转换为对应的简称。
    公司总部	南网超高压公司	广东电网公司	广西电网公司	云南电网公司	贵州电网公司	海南电网公司	深圳供电局
    总部	超高压	广东	广西	云南	贵州	海南	深圳
    Args:
        unit_list (list): 单位列表。
    Returns:
        unit_list (list): 转换后的单位列表。
    """
    unit_dict = {
        "公司总部": "总部",
        "南网超高压公司": "超高压",
        "广东电网公司": "广东",
        "广西电网公司": "广西",
        "云南电网公司": "云南",
        "贵州电网公司": "贵州",
        "海南电网公司": "海南",
        "深圳供电局": "深圳"
    }
    for i in range(len(unit_list)):
        if unit_list[i] in unit_dict:
            unit_list[i] = unit_dict[unit_list[i]]
    return unit_list


def col_letter_to_index(col_letter: str) -> int:
    """
    将列字母转换为列索引。
    """
    col_letter = col_letter.upper()
    result = 0
    for i, char in enumerate(col_letter[::-1]):
        result += (ord(char) - ord('A') + 1) * (26 ** i)
    return result - 1  # 因为列索引是从0开始的


def get_cell_value(sheet, row: int, col: str):
    """
    获取指定行列的单元格值，支持不同的库访问方式。
    """
    if isinstance(sheet, openpyxl.worksheet.worksheet.Worksheet):
        # openpyxl 使用字母表示的列（例如 'A', 'B', 'C'）
        cell_value = sheet[f"{col}{row}"].value
    elif isinstance(sheet, xlrd.sheet.Sheet):
        # xlrd 使用整数索引的列（例如 0, 1, 2 对应 'A', 'B', 'C'）
        col_idx = col_letter_to_index(col)
        cell_value = sheet.cell_value(row - 1, col_idx)  # xlrd 的行是 0 索引
    else:
        raise ValueError("不支持的 sheet 类型")

    # 如果返回的是字符串，则尝试转换为数字，如果无法转换，则返回 0
    if isinstance(cell_value, str):
        try:
            cell_value = float(cell_value)  # 尝试将字符串转换为 float
        except ValueError:
            return 0  # 如果转换失败，返回 0
    # 如果是数字类型，直接返回
    if isinstance(cell_value, (int, float)):
        return cell_value
    else:
        return 0  # 如果是其他类型，返回 0


def get_cell_string(sheet, row: int, col: str):
    """
    获取指定行列的单元格字符串值，支持不同的库访问方式。
    """
    if isinstance(sheet, openpyxl.worksheet.worksheet.Worksheet):
        # openpyxl 使用字母表示的列（例如 'A', 'B', 'C'）
        cell_value = sheet[f"{col}{row}"].value
    elif isinstance(sheet, xlrd.sheet.Sheet):
        # xlrd 使用整数索引的列（例如 0, 1, 2 对应 'A', 'B', 'C'）
        col_idx = col_letter_to_index(col)
        cell_value = sheet.cell_value(row - 1, col_idx)  # xlrd 的行是 0 索引
    else:
        raise ValueError("不支持的 sheet 类型")

    # 如果返回的值是字符串，则直接返回字符串
    if isinstance(cell_value, str):
        return cell_value
    # 如果是数字类型，则转换为字符串后返回
    elif isinstance(cell_value, (int, float)):
        return str(cell_value)
    else:
        return ""  # 如果是其他类型，返回空字符串


def calculate_sum(sheet, row: int, cols: List[str]) -> float:
    """
    计算指定行和列的和。
    """
    values = []

    for col in cols:
        if isinstance(sheet, openpyxl.worksheet.worksheet.Worksheet):
            # openpyxl 中的列使用字母索引
            cell_value = sheet[f"{col}{row}"].value
        elif isinstance(sheet, xlrd.sheet.Sheet):
            # xlrd 中的列使用整数索引，列字母需要转换为索引
            col_idx = col_letter_to_index(col)
            cell_value = sheet.cell_value(row - 1, col_idx)  # xlrd 下需要减去1

        if isinstance(cell_value, (int, float)):
            values.append(cell_value)
        elif cell_value is not None:
            logger.warning(f"警告: {col}{row} 的值不是数字，已跳过。")

    return sum(values)


def load_excel_sheet(username: str, project_name: str, sheet_name: str):
    user_project_directory = os.path.join(
        UPLOAD_DIRECTORY, username, project_name)
    xlsx_list = glob.glob(os.path.join(user_project_directory, '*.xlsx')) + \
        glob.glob(os.path.join(user_project_directory, '*.xls'))

    if not xlsx_list:
        raise HTTPException(status_code=404, detail="未找到上传的Excel文件")

    file_path = xlsx_list[0]

    if file_path.endswith(".xlsx"):
        wb = load_workbook(file_path, read_only=True)
        sheet_names = wb.sheetnames  # openpyxl 获取工作表名称
    elif file_path.endswith(".xls"):
        wb = xlrd.open_workbook(file_path)
        sheet_names = wb.sheet_names()  # xlrd 获取工作表名称
    else:
        raise HTTPException(status_code=400, detail="不支持的文件格式")

    if sheet_name not in sheet_names:
        raise HTTPException(
            status_code=404, detail=f"Excel中未找到指定的工作表：{sheet_name}")

    # 根据使用的库返回对应的工作表
    if file_path.endswith(".xlsx"):
        return wb[sheet_name]  # openpyxl 使用 wb[sheet_name]
    elif file_path.endswith(".xls"):
        return wb.sheet_by_name(sheet_name)  # xlrd 使用 sheet_by_name


def hlookup(sheet, lookup_value, row_index, search_range):
    """
    模拟 HLOOKUP 函数，根据 lookup_value 查找表格中的数据并返回对应行的值。
    """
    # 去除 lookup_value 的多余空格
    lookup_value = lookup_value.strip()

    for col in search_range:
        if isinstance(sheet, openpyxl.worksheet.worksheet.Worksheet):
            # openpyxl 使用字母索引
            cell_value = sheet[f"{col}{row_index}"].value
            if cell_value and cell_value.strip() == lookup_value:
                return sheet[f"{col}{row_index + 3}"].value  # 返回第4行的对应值
        elif isinstance(sheet, xlrd.sheet.Sheet):
            # xlrd 使用整数索引，列字母需要转换为索引
            col_idx = col_letter_to_index(col)
            cell_value = sheet.cell_value(row_index - 1, col_idx)
            if cell_value.strip() == lookup_value:
                # 返回第4行的对应值（需要减1）
                return sheet.cell_value(row_index + 3 - 1, col_idx)

    return None


@router.post("/table4", summary="生成表四（其他费用估算表）")
@api_response
async def generate_table4(request: ExcelRequest):
    """
    生成表四（其他费用估算表）。根据建设单位选择不同模板进行生成。
    Args:
        request (ExcelRequest): 用户请求，包含用户名、项目名称
    Returns:
        APIResponse: 包含表格内容的响应字典
    """
    try:
        # 初始化
        content_list = []
        unit_data = []

        # 读取建设单位
        user_path = os.path.join(
            UPLOAD_DIRECTORY, request.username, request.project_name)
        config_js = os.path.join(user_path,  "config.json")
        with open(config_js, "r", encoding="utf-8") as f:
            data = json.load(f)
        unit_list = data.get("construction_unit", "").split("、")
        num = len(unit_list)

        # 读取表三附1以及表三的内容，获取其总长度
        len3_1 = get_len(user_path, 'table3_1.json')
        len3 = get_len(user_path, 'table3.json')

        # 定义路径
        template_path = os.path.join(
            settings.PROJECT_ROOT, "documents", "其他费用模板")
        file_path = os.path.join(template_path, "单公司_0409.xlsx")

        if num != 1:

            # 生成E列数据
            replace_dict = {
                5: f"=ROUND('表三（项目分项估算表）'!J{len3+16}*计算参数表!D49,0)",
                8: f"=ROUND(IF(基数表!B11/10000<1000,0,'表三附1（开发、集成工作量测算）'!AE{len3_1+1}*9/10*计算参数表!C18*计算参数表!C19*计算参数表!$D$89),0)",
                11: f"=ROUND(IF(基数表!B11/10000<1000,0,'表三附1（开发、集成工作量测算）'!AE{len3_1+1}*9/10*计算参数表!C20*计算参数表!C22*计算参数表!$D$91),0)",
                12: f"=ROUND('表三附1（开发、集成工作量测算）'!AE{len3_1+1}*9/10*计算参数表!C21*计算参数表!C22*计算参数表!$D$91,0)"
            }

            # 读取模板内容
            file_path = os.path.join(template_path, "多公司_0423.xlsx")
            wb = load_workbook(file_path)
            sheet = wb["表四(其他费用估算表)"]
            for row_idx in range(4, 28):
                cell_value = sheet.cell(row_idx, 5).value
                content_list.append([cell_value])

            # 根据字典的key替换二维列表第五列的值
            for key, value in replace_dict.items():
                content_list[key][0] = value
                
        else:

            # 读取表三附1以及表三的内容，获取其总长度
            len3_1 = get_len(user_path, 'table3_1.json')
            len3 = get_len(user_path, 'table3.json')

            # 生成E列数据
            replace_dict = {
                5: f"=ROUND('表三（项目分项估算表）'!J{len3+3}*计算参数表!D49,0)",
                8: f"=ROUND(IF('计算参数表（其他费用）'!B4<1000,0,'表三附1（开发、集成工作量测算）'!AE{len3_1+1}*9/10*计算参数表!C18*计算参数表!C19*计算参数表!$D$89),0)",
                11: f"=ROUND(IF('计算参数表（其他费用）'!B4<1000,0,'表三附1（开发、集成工作量测算）'!AE{len3_1+1}*9/10*计算参数表!C20*计算参数表!C22*计算参数表!$D$91),0)",
                12: f"=ROUND('表三附1（开发、集成工作量测算）'!AE{len3_1+1}*9/10*计算参数表!C21*计算参数表!C22*计算参数表!$D$91,0)"
            }

            # 读取模板内容
            if unit_list[0] == "海南电网公司":
                file_path = os.path.join(template_path, "海南电网公司_0409.xlsx")
            wb = load_workbook(file_path)
            sheet = wb["表四(其他费用估算表)"]
            for row_idx in range(4, 29):
                row_data = []
                for col_idx in range(1, 6):
                    cell_value = sheet.cell(row_idx, col_idx).value
                    row_data.append(cell_value)
                content_list.append(row_data)

            # 根据字典的key替换二维列表第五列的值
            for key, value in replace_dict.items():
                content_list[key][4] = value

        return APIResponse(200, "生成表四成功", {"content_list": content_list})

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成表四失败！{str(e)}")
