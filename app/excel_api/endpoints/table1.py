# 本文件用于存放以下接口：
# - 生成表一（项目估算汇总表）
from app.core.config import settings
from app.docs_api.schemas import ExcelRequest
from app.utils.response import APIResponse, api_response, RESPONSE_MODELS
from app.schemas import SuccessResponse, ErrorResponse, ExcelResponse
from fastapi import APIRouter, HTTPException
import glob
import json
from openpyxl import load_workbook
from app.core.logging_config import get_logger
import os
import openpyxl
import string
from typing import List, Dict
import xlrd

UPLOAD_DIRECTORY = settings.UPLOAD_DIRECTORY

router = APIRouter()
logger = get_logger(__name__)


def col_letter_to_index(col_letter: str) -> int:
    """
    将列字母（如 'A'、'B'）转换为索引（0, 1, 2, ...）
    """
    return ord(col_letter.upper()) - ord('A')


def get_cell_value(sheet, row: int, col: str):
    """
    获取指定行列的单元格值，支持不同的库访问方式。
    """
    if isinstance(sheet, openpyxl.worksheet.worksheet.Worksheet):
        # openpyxl 使用字母表示的列（例如 'A', 'B', 'C'）
        cell_value = sheet[f"{col}{row}"].value
    elif isinstance(sheet, xlrd.sheet.Sheet):
        # xlrd 使用整数索引的列（例如 0, 1, 2 对应 'A', 'B', 'C'）
        col_idx = col_letter_to_index(col)
        cell_value = sheet.cell_value(row - 1, col_idx)  # xlrd 的行是 0 索引
    else:
        raise ValueError("不支持的 sheet 类型")

    # 如果返回的是字符串，则尝试转换为数字，如果无法转换，则返回 0
    if isinstance(cell_value, str):
        try:
            cell_value = float(cell_value)  # 尝试将字符串转换为 float
        except ValueError:
            return 0  # 如果转换失败，返回 0
    # 如果是数字类型，直接返回
    if isinstance(cell_value, (int, float)):
        return cell_value
    else:
        return 0  # 如果是其他类型，返回 0


def calculate_sum(sheet, row: int, cols: List[str]) -> float:
    """
    计算指定行和列的和，结果转换为万元（除以10000）。
    """
    values = []

    for col in cols:
        if isinstance(sheet, openpyxl.worksheet.worksheet.Worksheet):
            # openpyxl 中的列使用字母索引
            cell_value = sheet[f"{col}{row}"].value
        elif isinstance(sheet, xlrd.sheet.Sheet):
            # xlrd 中的列使用整数索引，列字母需要转换为索引
            col_idx = col_letter_to_index(col)
            cell_value = sheet.cell_value(row - 1, col_idx)  # xlrd 下需要减去1

        if isinstance(cell_value, (int, float)):
            values.append(cell_value)
        elif cell_value is not None:
            logger.warning(f"警告: {col}{row} 的值不是数字，已跳过。")

    return sum(values) / 10000


def load_excel_sheet(username: str, project_name: str, sheet_name: str):
    user_project_directory = os.path.join(
        UPLOAD_DIRECTORY, username, project_name)
    xlsx_list = glob.glob(os.path.join(user_project_directory, '*.xlsx')) + \
        glob.glob(os.path.join(user_project_directory, '*.xls'))

    if not xlsx_list:
        raise HTTPException(status_code=404, detail="未找到上传的Excel文件")

    file_path = xlsx_list[0]

    if file_path.endswith(".xlsx"):
        wb = load_workbook(file_path, read_only=True)
        sheet_names = wb.sheetnames  # openpyxl 获取工作表名称
    elif file_path.endswith(".xls"):
        wb = xlrd.open_workbook(file_path)
        sheet_names = wb.sheet_names()  # xlrd 获取工作表名称
    else:
        raise HTTPException(status_code=400, detail="不支持的文件格式")

    if sheet_name not in sheet_names:
        raise HTTPException(
            status_code=404, detail=f"Excel中未找到指定的工作表：{sheet_name}")

    # 根据使用的库返回对应的工作表
    if file_path.endswith(".xlsx"):
        return wb[sheet_name]  # openpyxl 使用 wb[sheet_name]
    elif file_path.endswith(".xls"):
        return wb.sheet_by_name(sheet_name)  # xlrd 使用 sheet_by_name


def hlookup(sheet, lookup_value, row_index, search_range):
    """
    模拟 HLOOKUP 函数，根据 lookup_value 查找表格中的数据并返回对应行的值。
    """
    # 去除 lookup_value 的多余空格
    lookup_value = lookup_value.strip()

    for col in search_range:
        if isinstance(sheet, openpyxl.worksheet.worksheet.Worksheet):
            # openpyxl 使用字母索引
            cell_value = sheet[f"{col}{row_index}"].value
            if cell_value and cell_value.strip() == lookup_value:
                return sheet[f"{col}{row_index + 3}"].value  # 返回第4行的对应值
        elif isinstance(sheet, xlrd.sheet.Sheet):
            # xlrd 使用整数索引，列字母需要转换为索引
            col_idx = col_letter_to_index(col)
            cell_value = sheet.cell_value(row_index - 1, col_idx)
            if cell_value.strip() == lookup_value:
                # 返回第4行的对应值（需要减1）
                return sheet.cell_value(row_index + 3 - 1, col_idx)

    return None


def generate_column_range(start_col, end_col_num):
    # 生成从 start_col 到 end_col_num 的列字母范围
    columns = list(string.ascii_uppercase)  # 'A' 到 'Z' 的字母列表
    start_index = columns.index(start_col)  # 获取起始列字母的索引
    return columns[start_index:start_index + end_col_num]


@router.post(
    "/table1",
    summary="生成表一（项目估算汇总表）",
    description="根据建设单位信息生成项目投资估算汇总表，包含各单位投资分配和汇总统计",
    responses={
        200: {"model": ExcelResponse, "description": "表格生成成功"},
        404: {"model": ErrorResponse, "description": "建设单位信息缺失"},
        500: {"model": ErrorResponse, "description": "表格生成失败"}
    },
    tags=["表一（项目估算汇总表）"]
)
@api_response
async def generate_table_one(request: ExcelRequest):
    """
    ## 生成表一（项目估算汇总表）

    根据项目的建设单位信息，自动生成项目投资估算汇总表。该表格是项目可研报告中的核心财务表格，
    用于展示各建设单位的投资分配情况和项目总体投资汇总。

    ### 表格结构

    #### 主要列字段
    - **序号**: 自动编号（1, 2, 3...）
    - **投资单位名称**: 各建设单位的完整名称
    - **项目名称**: 统一的项目名称
    - **投资估算**: 引用计算公式，关联详细估算数据
    - **其他费用**: 预留字段，默认为0
    - **合计**: 自动计算总投资
    - **占比**: 各单位投资占总投资的百分比

    #### 汇总行
    - **小计行**: 各列数据的小计
    - **合计行**: 项目总投资合计
    - **占比行**: 总占比（100%）

    ### 计算逻辑

    #### 数据来源
    1. 从`unit_ratio.json`文件读取建设单位列表
    2. 建设单位信息来源于文件上传时的自动解析
    3. 投资金额通过Excel公式关联到详细估算表

    #### 公式设计
    - **投资估算**: `=L{行号}` - 引用第L列的对应行数据
    - **合计**: `=D{行号}+E{行号}` - 投资估算+其他费用
    - **占比**: `=F{行号}/F{合计行}*100` - 计算百分比

    ### 功能特性
    - 动态行数生成（根据建设单位数量）
    - 自动公式计算
    - 标准格式输出
    - 支持自定义起始位置

    ### 参数说明
    - **username**: 用户名，用于定位项目文件
    - **project_name**: 项目名称，必须与上传文件时一致
    - **start_row**: 表格起始行号，默认为5
    - **start_col**: 表格起始列号，默认为1

    ### 返回数据
    - **content_list**: 二维数组，包含完整的表格数据
    - **mark_list**: 备注信息列表（如有）
    - **merged_list**: 合并单元格信息列表（如有）

    ### 应用场景
    - 项目可研报告投资估算章节
    - 项目申报材料财务汇总
    - 投资分配方案展示
    - 财务审核和评估

    ### 错误处理
    - **404**: 建设单位信息文件不存在或为空
    - **500**: 文件读取失败或表格生成异常

    ### 依赖关系
    - 需要先上传项目文件并完成建设单位信息解析
    - 依赖其他详细估算表的数据（通过Excel公式关联）
    """
    try:
        # 获取建设单位及其比例
        unit_ratio = os.path.join(
            UPLOAD_DIRECTORY, request.username, request.project_name, "unit_ratio.json")
        with open(unit_ratio, "r", encoding="utf-8") as f:
            data = json.load(f)
        construction_units = list(data.keys())
        if not construction_units:
            logger.error("建设单位为空！")
            raise HTTPException(status_code=404, detail="建设单位为空！")
        unit_num = len(construction_units)
        table_data = []

        # 生成前 x 行数据
        for i, unit in enumerate(construction_units, start=1):
            row = []

            row.append(i)  # 第一列
            j = i+4
            row.append(unit)  # 第二列: 投资单位名称
            row.append(request.project_name)  # 第三列: project_name
            row.append(f"=L{j}")  # 第四列
            row.append(0)  # 第五列
            row.append(f"=D{j}+E{j}")
            row.append(f"=SUM('表二(项目分项汇总估算表)'!D{j}:F{j})/10000")
            row.append(f"=SUM('表二(项目分项汇总估算表)'!G{j}:H{j})/10000")
            row.append(f"=SUM('表二(项目分项汇总估算表)'!I{j}:J{j})/10000")
            row.append(f"=SUM('表二(项目分项汇总估算表)'!K{j}:O{j})/10000")
            row.append(f"='表四(其他费用估算表)'!E4*计算参数表!E{30 + i}/10000")
            row.append(f"=SUM(G{j}:K{j})")
            table_data.append(row)

        # 4. 生成倒数第三行（用空字符串代替）
        table_data.append([""] * 12)

        # 5. 生成倒数第二行（合计）
        total_row = [""] * 2
        total_row.append("合计")
        total_row.append(f"=SUM(D5:D{4+unit_num})")
        total_row.append(f"=SUM(D5:D{4+unit_num})")
        total_row.append(f"=SUM(F5:F{4+unit_num})")
        total_row.append(f"=SUM(G5:G{4+unit_num})")
        total_row.append(f"=SUM(H5:H{4+unit_num})")
        total_row.append(f"=SUM(I5:I{4+unit_num})")
        total_row.append(f"=SUM(J5:J{4+unit_num})")
        total_row.append(f"=SUM(K5:K{4+unit_num})")
        total_row.append(f"=SUM(L5:L{4+unit_num})")
        table_data.append(total_row)

        # 6. 生成倒数第一行（占比）
        ratio_row = [""] * 2
        ratio_row.append("各项费用占总投资比例%")
        ratio_row.append(f"=D{6+unit_num}/F{6+unit_num}")
        ratio_row.append(f"=E{6+unit_num}/F{6+unit_num}")
        ratio_row.append("100%")
        ratio_row.append(f"=G{6+unit_num}/F{6+unit_num}")
        ratio_row.append(f"=H{6+unit_num}/F{6+unit_num}")
        ratio_row.append(f"=I{6+unit_num}/F{6+unit_num}")
        ratio_row.append(f"=J{6+unit_num}/F{6+unit_num}")
        ratio_row.append(f"=K{6+unit_num}/F{6+unit_num}")
        ratio_row.append("100%")
        table_data.append(ratio_row)

        return APIResponse(200, "生成表一成功", {"content_list": table_data})

    except Exception as e:
        logger.error(f"生成表一失败！{str(e)}")
        raise HTTPException(status_code=500, detail=f"生成表一失败！{str(e)}")
