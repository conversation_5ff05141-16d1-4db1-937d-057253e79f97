# 本文件用于存放以下接口：
# - 生成表五 /table5
import os
import pandas as pd
from fastapi import APIRouter, HTTPException
from app.docs_api.schemas import ExcelRequest
from app.utils.response import APIResponse, api_response
from app.core.config import settings
from app.core.logging_config import get_logger
import json

logger = get_logger(__name__)
UPLOAD_DIRECTORY = settings.UPLOAD_DIRECTORY

router = APIRouter()

# 生成表五接口
@router.post("/table5", summary="生成表五")
@api_response
async def table5(request: ExcelRequest):
    """
    生成表五。
    Args:
        request (ProjectInfoRequest): 包含用户输入的项目名称
            - user_name (str): 用户名
            - project_name (str): 格式为"项目类（具体项目名称）"
            - start_row (int): 起始行号
            - start_col (int): 起始列号
    Returns:
        APIResponse: 包含处理结果的响应对象
            - status_code: 200 校验成功
            - message: 处理结果说明
            - data:
                - content_list (List): excel内容的二维列表
                - mark_list (List): 包含坐标信息的备注列表
                - merged_list (List): 包含坐标信息的合并单元格列表
    Raises:
        HTTPException(500): 服务器内部错误
    """
    try:
        # 初始化
        content_list, mark_list, merged_list = [], [], []
        start_pos = (request.start_row, request.start_col)
        col_list = ['序号', '建设单位', '项目名称', '总投资', 
                    '第一年资本性', '第一年费用性', '第一年合计', 
                    '第二年资本性', '第二年费用性', '第二年合计']
        table5_df = pd.DataFrame(columns=col_list)
        
        # 获取建设单位及其比例
        unit_ratio = os.path.join(
            UPLOAD_DIRECTORY, request.username, request.project_name, "unit_ratio.json")
        with open(unit_ratio, "r", encoding="utf-8") as f:
            data = json.load(f)
        construction_units = list(data.keys())
        if not construction_units:
            logger.error("建设单位为空！")
            raise HTTPException(status_code=404, detail="建设单位为空！")
        unit_num = len(construction_units)

        # 生成表五内容
        for i in range(unit_num):
            id = str(i+5)
            table5_df.loc[i, '序号'] = i + 1
            table5_df.loc[i, '建设单位'] = "='表一(项目估算汇总表)'!B" + id
            table5_df.loc[i, '项目名称'] = "='表一(项目估算汇总表)'!C" + id
            table5_df.loc[i, '总投资'] = "='表一(项目估算汇总表)'!F" + id
            table5_df.loc[i, '第一年资本性'] = "='表一(项目估算汇总表)'!D" + id + "*'表五（分阶段投资估算表）'!$L$3"
            table5_df.loc[i, '第一年费用性'] = "='表一(项目估算汇总表)'!E" + id + "*'表五（分阶段投资估算表）'!$L$3"
            table5_df.loc[i, '第一年合计'] = "=E" + id + "+F" + id
            table5_df.loc[i, '第二年资本性'] = "='表一(项目估算汇总表)'!D" + id + "-'表五（分阶段投资估算表）'!E" + id
            table5_df.loc[i, '第二年费用性'] = "='表一(项目估算汇总表)'!I" + id + "-'表五（分阶段投资估算表）'!F" + id
            table5_df.loc[i, '第二年合计'] = "=H" + id + "+I" + id

        # 插入两行空值
        empty_row = pd.DataFrame([[''] * len(col_list)], columns=col_list)
        table5_df = pd.concat([table5_df, empty_row, empty_row], ignore_index=True)

        # 生成合计行
        row_i = unit_num + 2
        sum_i = unit_num + 6
        table5_df.loc[row_i, '序号'] = ""
        table5_df.loc[row_i, '建设单位'] = ""
        table5_df.loc[row_i, '项目名称'] = "合计"
        table5_df.loc[row_i, '总投资'] = f"=SUM(D5:D{sum_i})"
        table5_df.loc[row_i, '第一年资本性'] = f"=SUM(E5:E{sum_i})"
        table5_df.loc[row_i, '第一年费用性'] = f"=SUM(F5:F{sum_i})"
        table5_df.loc[row_i, '第一年合计'] = f"=SUM(G5:G{sum_i})"
        table5_df.loc[row_i, '第二年资本性'] = f"=SUM(H5:H{sum_i})"
        table5_df.loc[row_i, '第二年费用性'] = f"=SUM(I5:I{sum_i})"
        table5_df.loc[row_i, '第二年合计'] = f"=SUM(J5:J{sum_i})"

        content_list = table5_df.values.tolist()

        return APIResponse(200, "生成表五内容成功", {"content_list": content_list, "mark_list": mark_list, "merged_list": merged_list})

    except Exception as e:
        logger.error(f"生成表五内容失败！{str(e)}")
        raise HTTPException(status_code=500, detail=f"生成表五内容失败！{str(e)}")
        