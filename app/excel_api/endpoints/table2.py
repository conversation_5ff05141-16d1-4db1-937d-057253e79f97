# 本文件用于存放以下接口：
# - 生成表二（项目分项估算汇总表）

from app.docs_api.schemas import ExcelRequest
from fastapi import APIRouter, HTTPException
from typing import Dict
from app.utils.response import APIResponse, api_response
from app.core.config import settings
import os
import json
import pandas as pd

UPLOAD_DIRECTORY = settings.UPLOAD_DIRECTORY

router = APIRouter()


def column_number_to_letter(col_num):
    """将列号转换为 Excel 列字母 (1 -> A, 2 -> B, ..., 27 -> AA)"""
    result = ""
    while col_num > 0:
        col_num -= 1
        result = chr(col_num % 26 + 65) + result
        col_num //= 26
    return result


def percent_to_decimal(percent_str):
    """将百分数（字符串格式，如 '30%'）转换为小数"""
    return float(percent_str.strip('%')) / 100


@router.post("/table2", summary="生成表二")
@api_response
async def generate_table_two(request: ExcelRequest) -> Dict:
    """
    根据建设单位生成表二。
    """
    try:
        # 获取建设单位以及表三
        unit_ratio  = os.path.join(UPLOAD_DIRECTORY, request.username, request.project_name, "unit_ratio.json")
        with open(unit_ratio, "r", encoding="utf-8") as f:
            data = json.load(f)
        construction_units = list(data.keys())
        unit_num = len(construction_units)

        table3_path = os.path.join(UPLOAD_DIRECTORY, request.username, request.project_name, "table3.json")
        if not os.path.exists(table3_path):
            raise HTTPException(status_code=404, detail="未找到表三附1内容，请先生成表三附1！")
        table3_df = pd.read_json(table3_path, orient='records')
        len3 = len(table3_df)
        
        table_data = []

        # 单公司时填充一行数据
        if unit_num == 1:
            # 第一列序号, 第二列投资单位名称, 第三列项目名称
            row = [1, "='表一(项目估算汇总表)'!B5", "='表一(项目估算汇总表)'!C5", "='表三(项目分项估算表)'!I7"]
            # 获取表三项目名称为实施费的行号
            index_1 = table3_df[table3_df["项目名称"] == "实施费"].index[0] + 1
            row.append(f"='表三（项目分项估算表）'!I{index_1}")  # 实施费
            row.append(f"='表三（项目分项估算表）'!I{len3+1}") # 咨询服务费
            row.append(f"='表三（项目分项估算表）'!I{len3+5}") # 硬件购置费
            row.append(f"='表三（项目分项估算表）'!I{len3+15}") # 软件购置费
            row.append(f"='表三（项目分项估算表）'!I{len3+20}") # 设备租赁费
            row.append(f"='表三（项目分项估算表）'!I{len3+24}") # 云服务租赁费
            row.append(f"='表三（项目分项估算表）'!I{len3+29}") # 硬件运维费
            row.append(f"='表三（项目分项估算表）'!I{len3+30}") # 软件运维费
            row.append(f"='表三（项目分项估算表）'!I{len3+31}") # 基础环境运维费
            row.append(f"='表三（项目分项估算表）'!I{len3+32}") # IT服务运维费
            row.append(f"='表三（项目分项估算表）'!I{len3+33}") # 网络安全保障费
            row.append(f"=SUM(D5:O5)")  # 合计
            table_data.append(row)

        # 多公司时填充多行数据
        else:
            for i, unit in enumerate(construction_units, start=1):
                row = [i, unit, "='表一(项目估算汇总表)'!C5"]
                row.append(f"=HLOOKUP(B{4+i},'表三(项目分项估算表)'!$V$1:$AC${len3+2},'表三(项目分项估算表)'!U$7,FALSE())")   #开发费
                row.append(f"=HLOOKUP(B{4+i},'表三(项目分项估算表)'!$V$1:$AC${len3+2},'表三(项目分项估算表)'!U${len3+2},FALSE())")   # 实施费
                row.append(f"=0")   # 咨询服务费
                row.append(f"=0")   # 硬件购置费
                row.append(f"=0")   # 软件购置费
                row.append(f"=0")   # 设备租赁费
                row.append(f"=0")   # 云服务租赁费
                row.append(f"=0")   # 硬件运维费
                row.append(f"=0")   # 软件运维费
                row.append(f"=0")   # 基础环境运维费
                row.append(f"=0")   # IT服务运维费
                row.append(f"=0")   # 网络安全保障费
                row.append(f"=SUM(D{i+4}:O{i+4})")  # 合计
                table_data.append(row)

        #生成倒数第二行（用空字符串代替）
        table_data.append([""] * 12)

        #生成倒数第一行
        total_row = [""] * 2
        total_row.append("合计")
        columns = [chr(c) for c in range(ord('D'), ord('P') + 1)]  # 生成从 'D' 到 'P' 的列
        total_row.extend([f"=SUM({col}5:{col}{5+unit_num})" for col in columns])
        table_data.append(total_row)

        return APIResponse(200, "生成表二成功", {"content_list": table_data})
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成表二失败！{str(e)}")
  
