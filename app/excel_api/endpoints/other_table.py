# 本文件用于存放以下接口：
# - 生成表三 /table3
import os
import pandas as pd
from fastapi import APIRouter, HTTPException
from app.core.config import settings
from app.core.logging_config import get_logger
from app.docs_api.schemas import ExcelRequest
from app.utils.response import APIResponse, api_response

logger = get_logger(__name__)

# 上传文件的目录
UPLOAD_DIRECTORY = settings.UPLOAD_DIRECTORY

router = APIRouter()


def add_row(df, content_list):
    """
    在DataFrame中添加一行数据
    Args:
        df (pd.DataFrame): 要添加数据的DataFrame
        content_list (list): 要添加的数据列表
    Returns:
        pd.DataFrame: 添加数据后的DataFrame
    """
    # 创建一个新的DataFrame
    new_row = pd.DataFrame([content_list], columns=df.columns)
    # 将新行添加到原DataFrame的末尾
    df = pd.concat([df, new_row], ignore_index=True)
    return df


@router.post("/other_table", summary="生成分摊单位表格")
@api_response
async def other_table(request: ExcelRequest):
    """
    生成分摊单位表格
    Args:
        request (ProjectInfoRequest): 包含用户输入的项目名称
            - user_name (str): 用户名
            - project_name (str): 格式为"项目类（具体项目名称）"
            - start_row (int): 起始行号
            - start_col (int): 起始列号
    Returns:
        APIResponse: 包含处理结果的响应对象
            - status_code: 200 校验成功
            - message: 处理结果说明
            - data: 
                - content_list (List): excel内容的二维列表
                - mark_list (List): 包含坐标信息的备注列表
                - merged_list (List): 包含坐标信息的合并单元格列表
    Raises:
        HTTPException(500): 服务器内部错误
    """
    try:
        # 初始化
        content_list, mark_list, merged_list = [], [], []
        table_name = request.project_name + "建设项目可行性研究投资估算书-总表（含开发、实施工作量）.xls"

        # 读取表三附1的内容
        table3_1_path = os.path.join(
            UPLOAD_DIRECTORY, request.username, request.project_name, 'table3.json')
        if not os.path.exists(table3_1_path):
            raise HTTPException(status_code=404, detail="未找到表三内容，请先生成！")
        table_df = pd.read_json(table3_1_path, orient='records')
        len3 = len(table_df)

        # 多公司时需要填充各个分公司内容
        col_list = ['序号', '项目名称', '单位', '数量',
                    '建设费单价', '购置费单价', '租赁费单价', '运行维护费单价',
                    '建设费合计', '购置费合计', '租赁费合计', '运行维护费合计']
        nan_list = [''] * len(col_list)
        table3_df = pd.DataFrame(columns=col_list)

        # 第一行
        row = ["='表一(项目估算汇总表)'!B5", '', '', '',
               '', '', '', '',
               f'=I6', f'=J{len3 + 16}', f'=K{len3 + 31}', f'=L{len3 + 40}']
        table3_df = add_row(table3_df, row)

        # 第二行
        row = ['一', '建设费', '', '',
               '', '', '', '',
               f'=I7+I{len3 + 2}+I{len3 + 13}', '', '', '']
        table3_df = add_row(table3_df, row)

        # 第三行
        row = ['（一）', '开发费', '', '',
               '', '', '', '',
               f'=ROUND(SUM(I8:I{len3}),0)', '', '', '']
        table3_df = add_row(table3_df, row)

        # 第四行开始依次填入各个应用组的名称、数量、单价、合计
        for i in range(8, len3+1):
            row = [i-7, f"='[{table_name}]表三（项目分项估算表）'!$B{i}", '人天', f"=HLOOKUP(A$5,'[{table_name}]表三（项目分项估算表）'!$M$1:$T${len3 + 11},'[{table_name}]表三（项目分项估算表）'!U{i},FALSE())", 
                   f"='[{table_name}]计算参数表'!$D$89", '', '', '',
                   f'=ROUND(E{i}*D{i},0)', '', '', '']
            table3_df = add_row(table3_df, row)
        table3_df = add_row(table3_df, nan_list)  # 添加空行

        # 实施费
        row = ['（二）', '实施费', '', '',
               '', '', '', '',
               f'=ROUND(SUM(I{len3+3}:I{len3 + 12}),0)', '', '', '']
        table3_df = add_row(table3_df, row)

        # 逐行填写各个实施费
        for i in range(len3+3, len3+12):
            row = [i-len3-2, f"='[{table_name}]表三（项目分项估算表）'!$B{i}", '人天', f"=HLOOKUP(A$5,'[{table_name}]表三（项目分项估算表）'!$M$1:$T${len3 + 11},'[{table_name}]表三（项目分项估算表）'!U{i},FALSE())",
                   f"='[{table_name}]计算参数表'!$D$90", '', '', '',
                   f'=ROUND(E{i}*D{i},0)', '', '', '']
            table3_df = add_row(table3_df, row)

        content_list = table3_df.values.tolist()
        return APIResponse(200, "生成成功", {"content_list": content_list, "mark_list": mark_list, "merged_list": merged_list})

    except Exception as e:
        logger.error(f"生成表三失败！{str(e)}")
        raise HTTPException(status_code=500, detail=f"生成失败！{str(e)}")
