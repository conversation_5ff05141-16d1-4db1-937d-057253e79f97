# 本文件用于存放以下接口：
# 生成表六、表七、表八
from fastapi import APIRouter, HTTPException
from app.docs_api.schemas import ExcelRequest
import pandas as pd
from app.core.config import settings
from app.utils.response import APIResponse, api_response
from openpyxl import load_workbook
import xlrd
import os
from app.core.logging_config import get_logger
import glob

router = APIRouter()
logger = get_logger(__name__)

# 从配置中获取上传目录
UPLOAD_DIRECTORY = settings.UPLOAD_DIRECTORY

def read_excel_sheet(file_path: str, sheet_name: str, start_row: int = 1, start_col: int = 1) -> pd.DataFrame:
    """
    读取 Excel 文件中的指定工作表内容并返回为 DataFrame 格式，可以指定从哪一行和哪一列开始读取数据。
    
    Args:
        file_path (str): Excel 文件路径
        sheet_name (str): 目标工作表名称
        start_row (int): 起始行（默认从第一行开始，1 表示第一行）
        start_col (int): 起始列（默认从第一列开始，1 表示第一列）

    Returns:
        pd.DataFrame: 读取的数据，如果 sheet 不存在，则返回空 DataFrame。
    """
    try:
        if file_path.endswith('.xlsx'):
            # 处理 .xlsx 文件
            wb = load_workbook(file_path, data_only=True)
            if sheet_name not in wb.sheetnames:
                wb.close()
                return pd.DataFrame()  # 找不到工作表，返回空 DataFrame
            
            ws = wb[sheet_name]

            # 提取从指定行和列开始的数据 (openpyxl 默认行列从 1 开始)
            data = []
            for row in ws.iter_rows(min_row=start_row, min_col=start_col, values_only=True):
                data.append(row)

            df = pd.DataFrame(data)
            wb.close()

        elif file_path.endswith('.xls'):
            # 处理 .xls 文件
            book = xlrd.open_workbook(file_path)
            if sheet_name not in book.sheet_names():
                return pd.DataFrame()  # 找不到工作表，返回空 DataFrame
            
            sheet = book.sheet_by_name(sheet_name)

            # 提取从指定行和列开始的数据 (xlrd 默认行列从 0 开始)
            data = []
            for row_idx in range(start_row - 1, sheet.nrows):  # start_row 需要减 1
                row = sheet.row_values(row_idx)[start_col - 1:]  # start_col 也要减 1
                data.append(row)

            df = pd.DataFrame(data)

        else:
            return pd.DataFrame()  # 不支持的格式，返回空 DataFrame

        return df

    except Exception as e:
        logger.error(f"读取 Excel 失败: {e}", exc_info=True)
        return pd.DataFrame()  # 遇到异常时返回空 DataFrame


# 生成表六
@router.post("/table6", summary="生成表六（软件设备汇总表）")
@api_response
async def table6(request: ExcelRequest):
    """
    读取上传的Excel文件中的表六（软件设备汇总表），并返回其内容。

    Args:
        request (ExcelRequest): 用户请求，包含用户名、项目名称、起始行、起始列

    Returns:
        dict: 包含表格内容的响应字典
    """
    try:
        # 查找用户上传的Excel文件
        user_project_directory = os.path.join(UPLOAD_DIRECTORY, request.username, request.project_name)
        xlsx_list = glob.glob(os.path.join(user_project_directory, '*.xlsx')) + glob.glob(os.path.join(user_project_directory, '*.xls'))

        if not xlsx_list:
            # raise HTTPException(status_code=404, detail="未找到上传的Excel文件")
            content_list = []
            content_list.append([""] * 11)
            return APIResponse(200, "生成表六成功", {"content_list": content_list})

        else:
            file_path = xlsx_list[0]

            # 读取表六（软件设备汇总表）
            df = read_excel_sheet(file_path, sheet_name="表六（软件设备汇总表）", start_row=4, start_col=1)

            if df.empty:
                content_list = []
                content_list.append([""] * 11)

            else:
                # 转换为二维列表返回
                content_list = df.values.tolist()
            
            return APIResponse(200, "生成表六成功", {"content_list": content_list})

    except Exception as e:
        logger.error(f"生成表六失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成表六失败: {str(e)}")
    

# 生成表七
@router.post("/table7", summary="生成表七（设备服务租赁汇总表）")
@api_response
async def table7(request: ExcelRequest):
    """
    读取上传的Excel文件中的表七（设备服务租赁汇总表），并返回其内容。

    Args:
        request (ExcelRequest): 用户请求，包含用户名、项目名称、起始行、起始列

    Returns:
        dict: 包含表格内容的响应字典
    """
    try:
        # 查找用户上传的Excel文件
        user_project_directory = os.path.join(UPLOAD_DIRECTORY, request.username, request.project_name)
        xlsx_list = glob.glob(os.path.join(user_project_directory, '*.xlsx')) + glob.glob(os.path.join(user_project_directory, '*.xls'))

        if not xlsx_list:
            # raise HTTPException(status_code=404, detail="未找到上传的Excel文件")
            content_list = []
            content_list.append([""] * 9)
            return APIResponse(200, "生成表七成功", {"content_list": content_list})
        
        else:
            file_path = xlsx_list[0]

            # 读取表七（设备服务租赁汇总表）
            df = read_excel_sheet(file_path, sheet_name="表七（设备服务租赁汇总表）", start_row=4, start_col=1)

            if df.empty:
                content_list = []
                content_list.append([""] * 9)
            
            else:
                # 转换为二维列表返回
                content_list = df.values.tolist()
            
            return APIResponse(200, "生成表七成功", {"content_list": content_list})

    except Exception as e:
        logger.error(f"生成表七失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成表七失败: {str(e)}")


# 生成表八
@router.post("/table8", summary="生成表八（需求变更费用对比表）")
@api_response
async def table8(request: ExcelRequest):
    """
    读取上传的Excel文件中的表八（需求变更费用对比表），并返回其内容。

    Args:
        request (ExcelRequest): 用户请求，包含用户名、项目名称、起始行、起始列

    Returns:
        dict: 包含表格内容的响应字典
    """
    try:
        # 查找用户上传的Excel文件
        user_project_directory = os.path.join(UPLOAD_DIRECTORY, request.username, request.project_name)
        xlsx_list = glob.glob(os.path.join(user_project_directory, '*.xlsx')) + glob.glob(os.path.join(user_project_directory, '*.xls'))

        if not xlsx_list:
            # raise HTTPException(status_code=404, detail="未找到上传的Excel文件")
            content_list = []
            content_list.append([""] * 12)

            return APIResponse(200, "生成表八成功", {"content_list": content_list})
        else:

            file_path = xlsx_list[0]

            # 读取表八（需求变更费用对比表）
            df = read_excel_sheet(file_path, sheet_name="表八（需求变更费用对比表）", start_row=5, start_col=1)

            if df.empty:
                content_list = []
                content_list.append([""] * 12)

            else:
                # 转换为二维列表返回
                content_list = df.values.tolist()

            return APIResponse(200, "生成表八成功", {"content_list": content_list})

    except Exception as e:
        logger.error(f"生成表八失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成表八失败: {str(e)}")

