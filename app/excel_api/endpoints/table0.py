# --coding:utf-8--
# 本文件用于保存封面、编制说明等接口：
# - 生成封面估算造价表格内容 /estimate_cost
# - 生成编制说明项目投资内容 /project_invest

import os
import json
from fastapi import APIRouter, HTTPException
from app.core.config import settings
from app.core.logging_config import get_logger
from app.utils.response import APIResponse, api_response
from app.docs_api.schemas import ProjectInfoRequest

logger = get_logger(__name__)

# 从配置中获取上传目录
UPLOAD_DIRECTORY = settings.UPLOAD_DIRECTORY

router = APIRouter()


def get_unit_ratio(username: str, project_name: str) -> dict:
    """
    获取建设单位及其比例。
    Args:
        username (str): 用户名
        project_name (str): 项目名称
    Returns:
        dict: 建设单位及其比例
    Raises:
        HTTPException: 404 未找到建设单位比例文件
    """
    try:
        # 构建文件路径
        unit_ratio_path = os.path.join(
            UPLOAD_DIRECTORY, username, project_name, "unit_ratio.json")
        # 检查文件是否存在
        if not os.path.exists(unit_ratio_path):
            raise HTTPException(
                status_code=404, detail="未找到建设单位比例文件！")
        # 读取文件内容
        with open(unit_ratio_path, "r", encoding="utf-8") as f:
            data = json.load(f)
        return data

    except Exception as e:
        logger.error(f"获取建设单位及其比例失败！{str(e)}")
        return None


@router.post("/estimate_cost", summary="封面估算造价表格内容")
@api_response
async def estimate_cost(request: ProjectInfoRequest):
    """
    生成封面估算造价表格内容。
    Args:
        request (ProjectInfoRequest): 包含用户输入的项目名称
            - user_name (str): 用户名
            - project_name (str): 格式为"项目类（具体项目名称）"
    Returns:
        APIResponse: 包含处理结果的响应对象
            - status_code: 200 校验成功
            - message: 成功信息
            - data: 处理后的表格内容
    Raises:
        HTTPException: 500 服务器内部错误
            - detail: 错误信息
    """
    try:
        # 获取建设单位及其比例
        construction_units = get_unit_ratio(
            request.username, request.project_name)
        if not construction_units:
            raise HTTPException(status_code=404, detail="建设单位比例文件未找到！")
        row_index = len(construction_units) + 6
        # 返回对应公式
        return APIResponse(200, "生成成功", {"content_list": f"""=CONCATENATE(ROUND('表一（项目估算汇总表）'!L{row_index},2),"万元")"""})

    except Exception as e:
        logger.error(f"获取封面估算造价表格内容失败！{str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/project_invest", summary="编制说明项目投资内容")
@api_response
async def project_invest(request: ProjectInfoRequest):
    """
    生成编制说明项目投资内容。
    Args:
        request (ProjectInfoRequest): 包含用户输入的项目名称
            - user_name (str): 用户名
            - project_name (str): 格式为"项目类（具体项目名称）"
    Returns:
        APIResponse: 包含处理结果的响应对象
            - status_code: 200 校验成功
            - message: 成功信息
            - data: 处理后的表格内容
    Raises:
        HTTPException: 500 服务器内部错误
            - detail: 错误信息
    """
    try:
        # 获取建设单位及其比例
        construction_units = get_unit_ratio(
            request.username, request.project_name)
        if not construction_units:
            raise HTTPException(status_code=404, detail="建设单位比例文件未找到！")
        row_index = len(construction_units) + 6
        # 返回对应公式
        return APIResponse(200, "生成成功", {"content_list": f"""=CONCATENATE("    本项目估算总投资：",ROUND('表一（项目估算汇总表）'!L{row_index},2),"万元")"""})

    except Exception as e:
        logger.error(f"获取编制说明项目投资内容失败！{str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
