# 本文件用于存放以下接口：
# 生成计算参数表 
from app.docs_api.schemas import ExcelRequest
from fastapi import APIRouter, HTTPException
from pydantic import Field
from typing import Dict
from app.utils.response import APIResponse, api_response
from app.utils.doc_processor import save_unit_ratio
import json
import os
from app.core.config import settings

router = APIRouter()

UPLOAD_DIRECTORY = settings.UPLOAD_DIRECTORY
# 定义一个平台名列表
platform_keywords = [
    "电网管理平台", "客户服务平台", "调度运行平台", "云景平台", "大数据平台", 
    "GIS平台", "企业级中台", "人工智能平台", "云平台", "物联网平台"
]

# 定义省份列表
valid_provinces = ["广西", "云南", "贵州", "海南"]

@router.post("/parameter_table", summary="生成计算参数表")
@api_response
async def parameter_table(request: ExcelRequest) -> Dict:
    """
    根据提供的项目配置生成计算参数表，返回结果为二维列表形式
    """
    try:
        config_js = os.path.join(UPLOAD_DIRECTORY, request.username, request.project_name, "config.json")
        with open(config_js, "r", encoding="utf-8") as f:
            data = json.load(f)
        
        project_category = data["project_category"]
        construction_cycle = data["construction_cycle"]
        project_type = data["project_type"]
        construction_nature = data["construction_nature"]
        system_security_level = data["system_security_level"]
        system_user_number = data["system_user_number"]
        system_deployment_mode = data["system_deployment_mode"]
        construction_unit = data["construction_unit"]
        unit_list = construction_unit.split("、")
        # 初始化二维列表，每个行的数据初始化为空
        table_data = []
        # 设置指定行的参数
        if len(unit_list) == 1:
            table_data.append([project_type, ""])
            table_data.append(["否", ""])
            table_data.append([project_category, ""])
            if any(keyword in request.project_name for keyword in platform_keywords):
                table_data.append(["核心系统", ""])
            else:
                table_data.append(["其他业务系统", ""])
            table_data.append(["决策阶段", ""])
            table_data.append([construction_nature, ""])
            province_found = [province for province in valid_provinces if province in construction_unit]
            if len(province_found) == 1:
                table_data.append([province_found[0]])
            else:
                table_data.append(["广东", ""])
            table_data.append([system_security_level, ""])
            table_data.append([system_user_number, ""])
            if system_deployment_mode == "网一级部署模式":
                table_data.append(["全网统一部署", ""])
            elif system_deployment_mode in ["网省两级部署模式", "网一级管理节点和省一级分节点部署模式"]:
                table_data.append(["分省分级部署", ""])
            else:
                table_data.append(["省市自建系统", ""])
            table_data.append([construction_cycle, ""])
            # 获取建设单位列表并保存对应比例
            unit_dict = save_unit_ratio(request.username, request.project_name, unit_list)
            unit_ratio = [[key, value] for key, value in unit_dict.items()]
            for i in range(len(unit_ratio)):
                table_data.append(unit_ratio[i])

        else:
            table_data.append([project_type])
            table_data.append(["否"])
            table_data.append([project_category])
            if any(keyword in request.project_name for keyword in platform_keywords):
                table_data.append(["核心系统"])
            else:
                table_data.append(["其他业务系统"])
            table_data.append(["决策阶段"])
            table_data.append([construction_nature])
            province_found = [province for province in valid_provinces if province in construction_unit]
            if len(province_found) == 1:
                table_data.append([province_found[0]])
            else:
                table_data.append(["广东"])
            table_data.append([system_security_level])
            table_data.append([system_user_number])
            if system_deployment_mode == "网一级部署模式":
                table_data.append(["全网统一部署"])
            elif system_deployment_mode in ["网省两级部署模式", "网一级管理节点和省一级分节点部署模式"]:
                table_data.append(["分省分级部署"])
            else:
                table_data.append(["省市自建系统"])
            table_data.append([construction_cycle])
            if len(unit_list) in [6,7,8]:
                table_data.append([len(unit_list)])
            else:
                table_data.append([8])

        # 返回结果
        return APIResponse(200, "计算参数表生成成功", {"content_list": table_data})
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成计算参数表时发生错误：{str(e)}")