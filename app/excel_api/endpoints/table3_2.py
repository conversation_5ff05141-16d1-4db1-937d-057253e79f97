# 本文件用于存放以下接口：
# 生成表三附2（实施工作量）
from fastapi import APIRouter, HTTPException
from app.docs_api.schemas import ExcelRequest
from app.core.config import settings
from app.core.config import settings
from app.utils.response import APIResponse, api_response
from app.utils.excel_processor import insert_start_pos
from openpyxl import load_workbook
import json
import os
import pandas as pd
import glob
import xlrd
from app.core.logging_config import get_logger

router = APIRouter()
logger = get_logger(__name__)

# 从配置中获取上传目录
UPLOAD_DIRECTORY = settings.UPLOAD_DIRECTORY


def read_excel_cell(file_path: str, sheet_name: str, row: int, col: int) -> str:
    """
    读取Excel文件中指定工作表、指定单元格的数据。

    Args:
        file_path (str): Excel文件路径
        sheet_name (str): 目标工作表名称
        row (int): 行号（从1开始）
        col (int): 列号（从1开始）

    Returns:
        str: 单元格数据
    """
    try:
        # 读取 .xlsx 文件
        if file_path.endswith('.xlsx'):
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
        # 读取 .xls 文件
        elif file_path.endswith('.xls'):
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
        else:
            raise ValueError("不支持的文件格式，只支持 .xlsx 或 .xls 文件")

        # 返回指定单元格的数据
        cell_value = df.iloc[row - 1, col - 1]  # 行列索引从0开始，用户输入从1开始，需减1
        return cell_value

    except Exception as e:
        logger.error(f"读取Excel单元格时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"读取Excel单元格时发生错误: {str(e)}")


def read_excel_sheet(file_path: str, sheet_name: str, start_row: int = 1, start_col: int = 1) -> pd.DataFrame:
    """
    读取Excel文件中的指定工作表内容并返回为DataFrame格式，可以指定从哪一行和哪一列开始读取数据。

    Args:
        file_path (str): Excel文件路径
        sheet_name (str): 目标工作表名称
        start_row (int): 起始行（默认从第一行开始，1表示第一行）
        start_col (int): 起始列（默认从第一列开始，1表示第一列）

    Returns:
        pd.DataFrame: 工作表数据
    """
    try:
        if file_path.endswith('.xlsx'):
            # 处理.xlsx文件
            wb = load_workbook(file_path, data_only=True)
            if sheet_name not in wb.sheetnames:
                raise ValueError(f"找不到工作表 '{sheet_name}'")
            ws = wb[sheet_name]

            # 提取从指定行和列开始的数据 (openpyxl默认行列从1开始)
            data = []
            for row in ws.iter_rows(min_row=start_row, min_col=start_col, values_only=True):
                data.append(row)

            # 将数据转换为DataFrame
            df = pd.DataFrame(data)
            wb.close()

        elif file_path.endswith('.xls'):
            # 处理.xls文件
            book = xlrd.open_workbook(file_path)
            if sheet_name not in book.sheet_names():
                raise ValueError(f"找不到工作表 '{sheet_name}'")
            sheet = book.sheet_by_name(sheet_name)

            # 提取从指定行和列开始的数据 (xlrd默认行列从0开始)
            data = []
            for row_idx in range(start_row - 1, sheet.nrows):  # 索引从0开始，start_row需要减1
                row = sheet.row_values(
                    row_idx)[start_col - 1:]  # start_col同样减1
                data.append(row)

            # 将数据转换为DataFrame
            df = pd.DataFrame(data)

        else:
            raise ValueError("不支持的文件格式，只支持 .xlsx 或 .xls 文件")

        return df
    except Exception as e:
        logger.error(f"读取Excel工作表时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"读取Excel工作表时发生错误: {str(e)}")


def generate_table_by_unit(unit_list: list):
    """
    根据建设单位判断选择相应的csv文件读取数据并返回其内容。
    Args:
        unit_list (list): 建设单位列表
    Returns:
        df (pd.DataFrame): 表格内容
    """
    # 初始化一个空的DataFrame
    df = pd.DataFrame()
    try:
        csv_file = os.path.join(
                settings.PROJECT_ROOT, "documents", "实施任务分配", "表三附2（实施工作量）-单公司_0409.csv")
        # 读取CSV文件
        df = pd.read_csv(csv_file, header=None, skiprows=1)  # 跳过第一行，第二行开始读
        df.fillna('', inplace=True)  # 用空字符串替换 NaN
        return df
    except Exception as e:
        logger.error(f"生成表三附2（实施工作量）时发生错误: {e}")
        return df


def generate_table_by_fee(dev_fee: float, unit_list: list):
    """
    根据开发费的数值判断选择相应的CSV文件读取数据并返回其内容。
    Args:
        dev_fee (float): 开发费的数值
        unit_list (list): 建设单位列表
    Returns:
        df (pd.DataFrame): 表格内容
    """
    file_path = os.path.join(
        settings.PROJECT_ROOT, "documents", "实施任务分配", "表三附2（实施工作量）-多公司_0421.xlsx")
    sheet = "表三附2(实施工作量)-开发费500万至1000万"
    df = pd.DataFrame()
    try:
        if 0 < dev_fee <= 200:
            sheet = "表三附2(实施工作量)-开发费小于等于200万"
        elif 200 < dev_fee <= 500:
            sheet = "表三附2(实施工作量)-开发费200万至500万"
        elif 500 < dev_fee <= 1000:
            sheet = "表三附2(实施工作量)-开发费500万至1000万"
        elif 1000 < dev_fee <= 2000:
            sheet = "表三附2(实施工作量)-开发费1000万至2000万"
        elif 2000 < dev_fee <= 5000:
            sheet = "表三附2(实施工作量)-开发费2000万至5000万"
        elif 5000 < dev_fee:
            sheet = "表三附2(实施工作量)-开发费5000万至10000万"

        # 读取模板内容
        content_list = []
        wb = load_workbook(file_path)
        sheet = wb[sheet]
        for row_idx in range(1,64):
            row_data = []
            for col_idx in range(7, 15):
                cell_value = sheet.cell(row_idx, col_idx).value
                row_data.append(cell_value)
            content_list.append(row_data)

        # 以字典的形式读取对应json文件，替换公司简称为极简称
        json_file = os.path.join(settings.PROJECT_ROOT, "documents", "公司简称-极简称对应表.json")
        if not os.path.exists(json_file):
            raise HTTPException(status_code=404, detail="文件 '公司简称-极简称对应表.json' 不存在")
        with open(json_file, 'r', encoding='utf-8') as file:
            unit_dict = json.load(file)
        new_list = []
        for unit in unit_list:
            new_list.append(unit_dict.get(unit, unit))

        # 将数据转换为DataFrame，第一行为表头
        df = pd.DataFrame(content_list, columns=content_list[0])
        # 对表头中不存在于new_list的列填充0
        for col in df.columns:
            if col not in new_list:
                df[col] = 0
        # 为空值填充0
        df.fillna(0, inplace=True)
        # 删除第一行
        df.drop(index=0, inplace=True)

        return df
    except Exception as e:
        logger.error(f"生成表三附2（实施工作量）时发生错误: {e}")
        return df


def generate_table_content(dev_fee: float, unit_list: list):
    """
    根据开发费的数值、建设单位判断选择相应的CSV文件读取数据并返回其内容。
    Args:
        dev_fee (float): 开发费的数值
        unit_list (list): 建设单位列表
    Returns:
        pd.DataFrame: 表格内容
    """
    # 初始化一个空的DataFrame
    df = pd.DataFrame()
    if len(unit_list) == 1:
        df = generate_table_by_unit(unit_list)
    else:
        df = generate_table_by_fee(dev_fee, unit_list)
    return df



@router.post("/table3_2", summary="生成表三附2(实施工作量)")
@api_response
async def table3_2(request: ExcelRequest):
    """
    如果Excel中已存在“表三附2(实施工作量)”工作表，则直接读取该工作表内容
    否则读取上传的Excel文件中的表一(项目估算汇总表)，根据开发费的数值判断选择相应的CSV文件读取数据，并返回其内容。
    Args:
        request (ExcelRequest): 用户请求，包含用户名、项目名称
    Returns:
        dict: 包含表格内容的响应字典
    """
    try:
        # 查找用户上传的Excel文件
        user_project_directory = os.path.join(
            UPLOAD_DIRECTORY, request.username, request.project_name)
        xlsx_list = glob.glob(os.path.join(user_project_directory, '*.xlsx')) + \
            glob.glob(os.path.join(user_project_directory, '*.xls'))
        data = {}
        start_pos = (request.start_row, request.start_col)
        if not xlsx_list:
            config_js = os.path.join(
                UPLOAD_DIRECTORY, request.username, request.project_name, "config.json")
            with open(config_js, "r", encoding="utf-8") as f:
                data = json.load(f)
            dev_fee = float(data.get("dev_fee", 0))
            unit_list = data.get("construction_unit", "").split("、")
            df = generate_table_content(dev_fee, unit_list)

        else:
            file_path = xlsx_list[0]
            # 根据文件扩展名加载工作簿
            if file_path.endswith(".xlsx"):
                wb = load_workbook(file_path, read_only=True)
            else:
                wb = xlrd.open_workbook(file_path)

            # 检查Excel文件中是否存在表三附2(实施工作量)
            sheet_names = wb.sheetnames if file_path.endswith(
                ".xlsx") else wb.sheet_names()
            if "表三附2(实施工作量)" in sheet_names:
                # 存在该工作表，读取其内容
                df = read_excel_sheet(
                    file_path, sheet_name="表三附2(实施工作量)", start_row=2)
            elif "表三附2（实施工作量）" in sheet_names:
                # 存在该工作表，读取其内容
                df = read_excel_sheet(
                    file_path, sheet_name="表三附2（实施工作量）", start_row=2)
            else:
                config_js = os.path.join(
                    UPLOAD_DIRECTORY, request.username, request.project_name, "config.json")
                with open(config_js, "r", encoding="utf-8") as f:
                    data = json.load(f)
                dev_fee = float(data.get("dev_fee", 0))
                unit_list = data.get("construction_unit", "").split("、")
                df = generate_table_content(dev_fee, unit_list)
                
        # 插入空白行后保存临时json文件
        try:
            table3_2_df = insert_start_pos(df, start_pos)
            user_project_directory = os.path.join(
                UPLOAD_DIRECTORY, request.username, request.project_name, 'table3_2.json')
            table3_2_df.to_json(user_project_directory,
                       orient='records', force_ascii=False)
        except Exception as e:
            logger.error(f"保存表三附2临时json文件失败: {str(e)}")
        
        # 转换为二维列表返回
        content_list = df.values.tolist()
        return APIResponse(200, "生成表三附2成功", {"content_list": content_list})

    except Exception as e:
        logger.error(f"生成表三附2失败: {e}")
        raise HTTPException(status_code=500, detail=f"生成表三附2失败: {e}")
