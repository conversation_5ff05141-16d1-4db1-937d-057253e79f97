# 本文件用于存放以下接口：
# - 生成表三 /table3
import os
import pandas as pd
from fastapi import APIRouter, HTTPException
from app.core.config import settings
from app.core.logging_config import get_logger
from app.docs_api.schemas import ExcelRequest
from app.utils.response import APIResponse, api_response
from app.utils.excel_processor import insert_start_pos, get_start_end
import json

logger = get_logger(__name__)

# 上传文件的目录
UPLOAD_DIRECTORY = settings.UPLOAD_DIRECTORY

router = APIRouter()


def add_row(df, content_list):
    """
    在DataFrame中添加一行数据
    Args:
        df (pd.DataFrame): 要添加数据的DataFrame
        content_list (list): 要添加的数据列表
    Returns:
        pd.DataFrame: 添加数据后的DataFrame
    """
    # 创建一个新的DataFrame
    new_row = pd.DataFrame([content_list], columns=df.columns)
    # 将新行添加到原DataFrame的末尾
    df = pd.concat([df, new_row], ignore_index=True)
    return df


@router.post("/table3", summary="生成表三")
@api_response
async def table3(request: ExcelRequest):
    """
    生成表三
    Args:
        request (ProjectInfoRequest): 包含用户输入的项目名称
            - user_name (str): 用户名
            - project_name (str): 格式为"项目类（具体项目名称）"
            - start_row (int): 起始行号
            - start_col (int): 起始列号
    Returns:
        APIResponse: 包含处理结果的响应对象
            - status_code: 200 校验成功
            - message: 处理结果说明
            - data: 
                - content_list (List): excel内容的二维列表
                - mark_list (List): 包含坐标信息的备注列表
                - merged_list (List): 包含坐标信息的合并单元格列表
    Raises:
        HTTPException(500): 服务器内部错误
    """
    try:
        # 初始化
        content_list, mark_list, merged_list = [], [], []
        start_pos = (request.start_row, request.start_col)

        # 读取config.json文件
        config_path = os.path.join(
            UPLOAD_DIRECTORY, request.username, request.project_name, 'config.json')
        unit_list = ["南方电网", "南网超高压公司", "广东电网公司", "广西电网公司",
                     "云南电网公司", "贵州电网公司", "海南电网公司", "深圳供电局"]
        if not os.path.exists(config_path):
            logger.info("未找到建设单位内容，本次生成实施费将采用多公司模板！")
        else:
            with open(config_path, "r", encoding="utf-8") as f:
                config_dict = json.load(f)
            unit_list = config_dict.get("construction_unit", "").split("、")

        # 读取表三附1的内容
        table3_1_path = os.path.join(
            UPLOAD_DIRECTORY, request.username, request.project_name, 'table3_1.json')
        if not os.path.exists(table3_1_path):
            raise HTTPException(status_code=404, detail="未找到表三附1内容，请先生成表三附1！")
        table3_1_df = pd.read_json(table3_1_path, orient='records')
        # 按照应用组进行获取各个应用组对应的起止行数
        group_index = get_start_end(table3_1_df, '应用组')
        # 去除key为空字符串的元素
        group_index = {k: v for k, v in group_index.items() if k}
        len3_1 = len(group_index)

        if len(unit_list) == 1:

            # 单公司时需要填充实施费
            col_list = ['序号', '项目名称', '单位', '数量',
                        '建设费单价', '购置费单价', '租赁费单价', '运行维护费单价',
                        '建设费合计', '购置费合计', '租赁费合计', '运行维护费合计']
            nan_list = [''] * len(col_list)
            table3_df = pd.DataFrame(columns=col_list)

            # 表三附二套用单公司模板
            task_dict = {
                '项目管理': 2,
                '技术管控': 18,
                '项目启动': 25,
                '系统环境准备': 28,
                '系统初始化': 30,
                '系统集成调试': 46,
                '用户培训': 49,
                '系统上线切换': 53,
                '运行持续支持': 62
            }

            # 第一行填入'','公司总部','','','','','','','=I6','=J26','=K41','=L50'
            first_row = ['', '公司总部', '', '', '', '', '', '', f'=I{start_pos[0] + 1}', f'=J{start_pos[0] + len3_1 + 18}',
                         f'=K{start_pos[0] + len3_1 + 33}', f'=L{start_pos[0] + len3_1 + 42}']
            table3_df = add_row(table3_df, first_row)

            # 第二行填入'一','建设费','','','','','','','=I6','','',''
            second_row = ['一', '建设费', '', '', '', '', '', '',
                          f'=I{start_pos[0] + 2}+I{start_pos[0] + 4 + len3_1}+I{start_pos[0] + len3_1 + 15}', '', '', '']
            table3_df = add_row(table3_df, second_row)

            # 第三行填入'（一）','开发费','','','','','','','=ROUND(SUM(I8:I10),0)','','',''
            third_row = ['（一）', '开发费', '', '', '', '', '', '',
                         f'=ROUND(SUM(I{start_pos[0] + 3}:I{start_pos[0] + 2 + len3_1}),0)', '', '', '']
            table3_df = add_row(table3_df, third_row)

            # 第四行开始依次填入各个应用组的名称、数量、单价、合计
            count = len(table3_df)  # 当前行数
            start = count  # 记录起点以填充序号
            for name, index in group_index.items():
                row = start_pos[0] + count  # 当前行的行号
                count += 1
                row_list = [count - start, name, '人天',
                            f"=SUM('表三附1（开发、集成工作量测算）'!AF{index[0] + 1}:AF{index[1] + 1})", '=计算参数表!$D$89', '', '', '', f'=ROUND(E{row}*D{row},0)', '', '', '']
                table3_df = add_row(table3_df, row_list)

            # 空一行
            table3_df = add_row(table3_df, nan_list)

            # 当前行填入'（二）','实施费','','','','','','','=ROUND(SUM(I13:I22),0)','','',''
            row_list = ['（二）', '实施费', '', '', '', '', '', '',
                        f'=ROUND(SUM(I{start_pos[0] + len3_1 + 5}:I{start_pos[0] + len3_1 + 20}),0)', '', '', '']
            table3_df = add_row(table3_df, row_list)

            # 当前行开始依次填入各个任务的名称、数量、单价、合计
            count = len(table3_df)
            start = count  # 记录起点以填充序号
            for name, index in task_dict.items():
                row = start_pos[0] + count  # 当前行的行号
                count += 1
                row_list = [count - start, name, '人天',
                            f"='表三附2（实施工作量）'!F{index}", '=计算参数表!$D$90', '', '', '', f'=ROUND(E{row}*D{row},0)', '', '', '']
                table3_df = add_row(table3_df, row_list)

        else:

            # 多公司时需要填充各个分公司内容
            col_list = ['序号', '项目名称', '单位', '数量',
                        '建设费单价', '购置费单价', '租赁费单价', '运行维护费单价',
                        '建设费合计', '购置费合计', '租赁费合计', '运行维护费合计',
                        '公司总部', '南网超高压公司', '广东电网公司', '广西电网公司',
                        '云南电网公司', '贵州电网公司', '海南电网公司', '深圳供电局',
                        '行序数',
                        '公司总部_1', '南网超高压公司_1', '广东电网公司_1', '广西电网公司_1',
                        '云南电网公司_1', '贵州电网公司_1', '海南电网公司_1', '深圳供电局_1']
            nan_list = [''] * len(col_list)
            table3_df = pd.DataFrame(columns=col_list)

            # 第一行
            first_row = ['', '公司总部', '', '', '', '', '', '', f'=I{start_pos[0] + 1}', f'=J{start_pos[0] + len3_1 + 18}', f'=K{start_pos[0] + len3_1 + 33}', f'=L{start_pos[0] + len3_1 + 42}', '', '', '', '', '', '', '', '',
                         f'=row(B{start_pos[0]})', f'=V{start_pos[0]+1}', f'=W{start_pos[0]+1}', f'=X{start_pos[0]+1}', f'=Y{start_pos[0]+1}', f'=Z{start_pos[0]+1}', f'=AA{start_pos[0]+1}', f'=AB{start_pos[0]+1}', f'=AC{start_pos[0]+1}']
            table3_df = add_row(table3_df, first_row)

            # 第二行
            formula = f'=I{start_pos[0] + 2}+I{start_pos[0] + 4 + len3_1}+I{start_pos[0] + len3_1 + 15}'
            second_row = ['一', '建设费', '', '', '', '', '', '', formula, '', '', '', '', '', '', '', '', '', '', '', f'=row(B{start_pos[0]+1})', formula.replace("I", "V"), formula.replace(
                "I", "W"), formula.replace("I", "X"), formula.replace("I", "Y"), formula.replace("I", "Z"), formula.replace("I", "AA"), formula.replace("I", "AB"), formula.replace("I", "AC")]
            table3_df = add_row(table3_df, second_row)

            # 第三行
            formula = f'=ROUND(SUM(I{start_pos[0] + 3}:I{start_pos[0] + 2 + len3_1}),0)'
            third_row = ['（一）', '开发费', '', '', '', '', '', '', formula, '', '', '', '', '', '', '', '', '', '', '', f'=row(B{start_pos[0]+2})', formula.replace("I", "V"), formula.replace(
                "I", "W"), formula.replace("I", "X"), formula.replace("I", "Y"), formula.replace("I", "Z"), formula.replace("I", "AA"), formula.replace("I", "AB"), formula.replace("I", "AC")]
            table3_df = add_row(table3_df, third_row)
            # 第四行开始依次填入各个应用组的名称、数量、单价、合计
            count = len(table3_df)
            start = count
            for name, index in group_index.items():
                row = start_pos[0] + count
                count += 1
                formula1 = f"=$D{row}*M$2"
                formula2 = f"=ROUND(M{row}*$E{row},0)"
                row_list = [count - start, name, '人天', f"=SUM('表三附1（开发、集成工作量测算）'!AF{index[0] + 1}:AF{index[1] + 1})", '=计算参数表!$D$89', '', '', '', f'=ROUND(E{row}*D{row},0)', '', '', '', formula1, formula1.replace("M", "N"), formula1.replace("M", "O"), formula1.replace("M", "P"), formula1.replace("M", "Q"), formula1.replace(
                    "M", "R"), formula1.replace("M", "S"), formula1.replace("M", "T"), f"=row(B{row})", formula2, formula2.replace("M", "N"), formula2.replace("M", "O"), formula2.replace("M", "P"), formula2.replace("M", "Q"), formula2.replace("M", "R"), formula2.replace("M", "S"), formula2.replace("M", "T")]
                table3_df = add_row(table3_df, row_list)

        # 将app_df填充空白行列后，存为一个临时的json文件用于再读取
        try:
            temp_df = insert_start_pos(table3_df, start_pos)
            user_project_directory = os.path.join(
                UPLOAD_DIRECTORY, request.username, request.project_name, 'table3.json')
            temp_df.to_json(user_project_directory,
                            orient='records', force_ascii=False)
        except Exception as e:
            logger.error(f"保存临时json文件失败！{str(e)}")
        finally:
            # 转化为二维列表输出结果
            content_list = table3_df.values.tolist()
            return APIResponse(200, "生成成功", {"content_list": content_list, "mark_list": mark_list, "merged_list": merged_list})

    except Exception as e:
        logger.error(f"生成表三失败！{str(e)}")
        raise HTTPException(status_code=500, detail=f"生成失败！{str(e)}")
