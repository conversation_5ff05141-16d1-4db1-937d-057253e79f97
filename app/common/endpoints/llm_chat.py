from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import Literal
from app.utils.llm import process_text
from app.utils.response import APIResponse, api_response, RESPONSE_MODELS
from app.schemas import SuccessResponse, ErrorResponse

router = APIRouter()


class ParagraphRequest(BaseModel):
    """文本处理请求模型"""
    paragraph: str = Field(
        ...,
        description="要处理的文本段落",
        min_length=1,
        max_length=10000,
        example="这是一个需要处理的文本段落。"
    )
    option: Literal["expand", "shorten", "polish"] = Field(
        ...,
        description="处理选项：expand（扩写）、shorten（缩写）、polish（优化）",
        example="expand"
    )

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "paragraph": "项目建设的必要性分析。",
                    "option": "expand"
                },
                {
                    "paragraph": "本项目旨在通过建设智能化管理平台，提升业务处理效率，优化用户体验，降低运营成本，为企业数字化转型提供有力支撑。",
                    "option": "shorten"
                },
                {
                    "paragraph": "系统具有良好的性能和稳定性。",
                    "option": "polish"
                }
            ]
        }


@router.post(
    "/basic",
    summary="AI文本处理",
    description="使用大语言模型对文本进行扩写、缩写或优化处理",
    responses={
        200: {"model": SuccessResponse, "description": "文本处理成功"},
        400: {"model": ErrorResponse, "description": "处理选项无效"},
        422: {"model": ErrorResponse, "description": "参数验证失败"},
        500: {"model": ErrorResponse, "description": "AI服务调用失败"}
    },
    tags=["llm_chat"]
)
@api_response
async def process_text_route(request: ParagraphRequest):
    """
    ## AI文本处理

    基于大语言模型的智能文本处理服务，支持文本扩写、缩写和优化三种处理模式。

    ### 功能特性
    - **智能扩写**: 在保持原意的基础上丰富内容，增加细节描述
    - **智能缩写**: 提取核心要点，保留关键信息，简化表达
    - **智能优化**: 改善文本表达，提升语言质量和专业性

    ### 处理模式详解

    #### expand（扩写）
    - 适用场景：需要丰富内容、增加细节的文本
    - 处理效果：在原文基础上增加相关描述、背景信息、具体细节
    - 输出长度：通常为原文的2-3倍

    #### shorten（缩写）
    - 适用场景：需要精简内容、提取要点的文本
    - 处理效果：保留核心信息，去除冗余表达，简化句式
    - 输出长度：通常为原文的1/2-1/3

    #### polish（优化）
    - 适用场景：需要改善表达质量的文本
    - 处理效果：优化用词、改善语法、提升专业性和可读性
    - 输出长度：与原文基本相当

    ### 技术特性
    - 基于先进的大语言模型
    - 支持中文文本处理
    - 保持原文语义和风格
    - 适用于各种文档类型

    ### 使用限制
    - 文本长度：1-10000个字符
    - 处理时间：通常在5-30秒内完成
    - 并发限制：支持多用户同时使用

    ### 参数说明
    - **paragraph**: 待处理的文本内容，支持中英文
    - **option**: 处理模式，必须为expand/shorten/polish之一

    ### 返回数据
    返回处理后的文本内容，保持原文的基本格式和结构。

    ### 错误处理
    - **400**: 处理选项无效，请检查option参数
    - **422**: 文本长度超限或格式错误
    - **500**: AI服务不可用或处理超时

    ### 应用场景
    - 项目文档编写和优化
    - 技术方案内容完善
    - 报告摘要生成
    - 文本质量提升
    """
    valid_options = ["expand", "shorten", "polish"]
    if request.option not in valid_options:
        raise HTTPException(
            status_code=400, detail="无效的选项。请选择 'expand', 'shorten' 或 'polish'。")

    try:
        processed_text = process_text(request.paragraph, request.option)
        return APIResponse(200, "文本处理成功", {"text": processed_text})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理文本时发生错误: {str(e)}")
