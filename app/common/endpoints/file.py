# --coding:utf-8--
# 本文件存放以下接口

import os
import json
import shutil
from typing import List
from app.core.logging_config import get_logger
from pydantic import BaseModel, Field
from app.core.config import settings
from app.utils.response import APIResponse, api_response, RESPONSE_MODELS
from app.schemas import SuccessResponse, ErrorResponse, FileInfo
from app.docs_api.endpoints.cover import get_category_info
from app.docs_api.endpoints.overview import read_investment_units
from app.utils.doc_processor import parse_txt_from_docx, parse_image_from_docx, parse_table_from_docx, docx_search, parse_config, save_unit_ratio
from fastapi import APIRouter, UploadFile, File, HTTPException, Form, Depends

router = APIRouter()
logger = get_logger(__name__)

# 从配置中获取系统根目录、上传目录、允许的文件类型和最大文件大小
ROOT_DIRECTORY = settings.PROJECT_ROOT
UPLOAD_DIRECTORY = settings.UPLOAD_DIRECTORY
MAX_FILE_SIZE = settings.MAX_FILE_SIZE
STATIC_URL = settings.STATIC_URL


def validate_upload_directory(directory: str):
    # 检查上传目录是否存在
    if not os.path.exists(directory):
        # 创建目录
        os.makedirs(directory, exist_ok=True)


def validate_file(file: UploadFile):
    # 检查文件扩展名
    file_ext = os.path.splitext(file.filename)[1].lower()
    if file_ext not in ['.doc', '.docx', '.xls', '.xlsx']:
        raise HTTPException(status_code=400, detail=f"不支持的文件类型: {file_ext}")

    # 检查文件大小
    file.file.seek(0, 2)  # 移动到文件末尾
    file_size = file.file.tell()  # 获取文件大小
    file.file.seek(0)  # 重置文件指针到开头
    if file_size > MAX_FILE_SIZE:
        # 413 Payload Too Large
        raise HTTPException(
            status_code=413, detail=f"文件大小超过限制: {file.filename}")


class UploadFilesRequest(BaseModel):
    username: str
    project_name: str


async def get_upload_files_request(
    username: str = Form(..., description="用户名"),
    project_name: str = Form(..., description="项目名称")
) -> UploadFilesRequest:
    return UploadFilesRequest(username=username, project_name=project_name)


class ProjectInfo(BaseModel):
    username: str = Field(..., description="用户名")
    project_name: str = Field(..., description="项目名称")


class DeleteFileRequest(BaseModel):
    username: str = Field(..., description="用户名")
    project_name: str = Field(..., description="项目名称")
    filename: str = Field(..., description="要删除的文件名")


class SearchFileRequest(BaseModel):
    username: str = Field(..., description="用户名")
    project_name: str = Field(..., description="项目名")
    search_title: str = Field(..., description="要查询的段落标题")
    format: str = Field(..., description="返回格式（list：列表或string：字符串）")


class SetProjectConfigRequest(BaseModel):
    username: str = Field(..., description="用户名")
    project_name: str = Field(..., description="项目名")
    construction_unit: str = Field("", description="建设单位")
    construction_cycle: str = Field("", description="建设周期")
    project_category: str = Field("", description="项目分类")
    project_type: str = Field("", description="项目类型")
    construction_nature: str = Field("", description="建设性质")
    system_security_level: str = Field("", description="系统等保级别")
    system_user_number: int = Field(0, description="系统用户数量")
    system_deployment_mode: str = Field("", description="系统部署方式")


@router.post(
    "/upload",
    summary="上传项目文件",
    description="上传Word或Excel文件到指定用户的项目目录，支持批量上传",
    responses={
        200: {"model": SuccessResponse, "description": "文件上传成功"},
        400: {"model": ErrorResponse, "description": "文件类型不支持或其他客户端错误"},
        413: {"model": ErrorResponse, "description": "文件大小超过限制"},
        422: {"model": ErrorResponse, "description": "项目名称解析失败"},
        500: {"model": ErrorResponse, "description": "服务器内部错误"}
    },
    tags=["files"]
)
@api_response
async def upload_files(
    request: UploadFilesRequest = Depends(get_upload_files_request),
    files: List[UploadFile] = File(..., description="要上传的文件列表，支持.doc/.docx/.xls/.xlsx格式")
):
    """
    ## 上传项目文件

    将一个或多个文件上传到指定用户的项目目录中。系统会自动解析文件内容，
    提取项目相关信息，并为后续的文档生成和表格计算提供数据支持。

    ### 功能特性
    - 支持批量文件上传
    - 自动文件类型验证
    - 文件大小限制检查
    - 自动目录创建和管理
    - 文件内容解析和信息提取
    - 项目配置自动更新

    ### 支持的文件格式
    - **Word文档**: .doc, .docx (项目需求文档、技术方案等)
    - **Excel表格**: .xls, .xlsx (数据表格、计算模板等)

    ### 文件大小限制
    - 单个文件最大: 50MB
    - 总上传大小: 无限制

    ### 处理流程
    1. 验证用户权限和项目名称
    2. 检查文件格式和大小
    3. 创建项目目录结构
    4. 保存文件到指定位置
    5. 解析文件内容提取信息
    6. 更新项目配置文件
    7. 返回上传结果

    ### 参数说明
    - **username**: 用户名，用于确定文件存储路径
    - **project_name**: 项目名称，格式为"项目类（具体项目名称）"
    - **files**: 文件列表，支持同时上传多个文件

    ### 返回数据
    返回已成功上传的文件信息列表，包括文件名、大小、类型等详细信息。

    ### 错误处理
    - **400**: 文件类型不支持 - 仅支持Word和Excel格式
    - **413**: 文件过大 - 超过50MB限制
    - **422**: 项目名称格式错误 - 无法解析项目分类信息
    - **500**: 服务器错误 - 文件保存或解析失败
    """
    try:
        user_project_directory = os.path.join(
            UPLOAD_DIRECTORY, request.username, request.project_name)

        category = {}
        # 读取规划项目文件，获取其对应一级、二级、三级分类
        try:
            category = get_category_info(request.project_name)
        except Exception as e:
            raise HTTPException(
                status_code=422, detail=f"项目名称解析失败！请确认项目名称是否正确！{e}")

        validate_upload_directory(user_project_directory)
        uploaded_files = []

        # 初始化，先获取项目原有参数
        chapter_list = ["项目背景", "项目依据", "项目目标", "项目范围", "现状分析",
                        "需求分析", "必要性结论", "业务架构", "应用架构", "数据架构",
                        "技术架构", "系统部署方式及软硬件资源需求", "安全技术方案", "项目实施需求",
                        "投资依据说明", "总投资", "资金计划建议", "管理效益分析",
                        "经济效益分析", "社会效益分析", "项目风险分析", "项目可研结论"]
        project_config = {}
        unit_list = []
        config_file_path = os.path.join(user_project_directory, "config.json")
        if os.path.exists(config_file_path):
            with open(config_file_path, "r", encoding="utf-8") as f:
                project_config = json.load(f)
            units = project_config.get("construction_unit", "")
            if units:
                unit_list = units.split("、")

        for file in files:
            validate_file(file)
            file_location = os.path.join(user_project_directory, file.filename)

            # 保存上传的文件
            with open(file_location, "wb+") as file_object:
                shutil.copyfileobj(file.file, file_object)

            # 获取文件扩展名
            file_ext = os.path.splitext(file.filename)[1].lower()

            # 处理Word文档并获取必要参数
            if file_ext in ['.doc', '.docx']:
                parse_txt_from_docx(file_location)
                images_dir = os.path.join(
                    ROOT_DIRECTORY, "static", request.username, request.project_name, "media")
                images_tag = os.path.join(
                    STATIC_URL, request.username, request.project_name, "media")
                parse_image_from_docx(file_location, images_dir, images_tag)
                parse_table_from_docx(file_location)
                project_config = parse_config(
                    request.username, request.project_name)
                try:
                    content = docx_search(
                        request.username, request.project_name, "项目范围", format="string")
                    if not content:
                        content = request.project_name
                    # 读取建设单位文件，获取建设单位的对应字典，然后遍历键值对，如果键或值在content中，则将键添加到unit_list中
                    unit_dict = read_investment_units()
                    for key, value in unit_dict.items():
                        if key in content or value in content:
                            if key == "南方电网公司":
                                unit_list.append("南方电网")
                            else:
                                unit_list.append(key)
                    for unit in ("公司总部", "总部", "总部公司", "总公司", "总公司总部"):
                        if unit in content:
                            unit_list.append("南方电网")
                    # 去重
                    unit_list = list(set(unit_list))

                    # 如果建设单位列表为空，则添加默认值
                    if not unit_list:
                        unit_list = ["南方电网"]

                    # 保存建设单位到config.json文件
                    project_config["construction_unit"] = '、'.join(unit_list)
                    config_path = os.path.join(
                        user_project_directory, "config.json")
                    with open(config_path, 'w', encoding='utf-8') as json_file:
                        json.dump(project_config, json_file,
                                  ensure_ascii=False, indent=4)
                    # 根据建设单位保存分子公司计算比例文件
                    save_unit_ratio(request.username,
                                    request.project_name, unit_list=unit_list)
                except Exception as e:
                    logger.error(f"解析建设单位失败！{e}")
                    unit_list = []

            uploaded_files.append(file.filename)

        file_result = {
            "files": uploaded_files,
            "project": category,
            "unit_list": unit_list,
            "project_config": project_config,
            "chapter_list": chapter_list
        }

        return APIResponse(200, "文件上传成功", file_result)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/files", response_model=List[str], summary="获取文件列表")
@api_response
async def list_files(request: ProjectInfo):
    """
    获取指定用户项目目录下的.docx文件列表。

    Parameters:
    - **username**: 用户名
    - **project_name**: 项目名称

    Returns:
    - **files**: .docx文件名列表

    Raises:
    - **HTTPException(500)**: 未预期的错误
    """
    try:
        user_project_directory = os.path.join(
            UPLOAD_DIRECTORY, request.username, request.project_name)
        validate_upload_directory(user_project_directory)
        all_files = os.listdir(user_project_directory)
        docx_files = [file for file in all_files if file.lower().endswith('.docx') or file.lower().endswith('.doc')
                      or file.lower().endswith('.xls') or file.lower().endswith('.xlsx')]
        return APIResponse(200, "获取文件列表成功", {"files": docx_files})
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/delete", summary="删除文件")
@api_response
async def delete_file(request: DeleteFileRequest):
    try:
        user_project_directory = os.path.join(
            UPLOAD_DIRECTORY, request.username, request.project_name)
        validate_upload_directory(user_project_directory)
        file_path = os.path.join(user_project_directory, request.filename)

        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=404, detail=f"文件 '{request.filename}' 不存在")

        # 获取文件扩展名
        file_ext = os.path.splitext(file_path)[1].lower()

        # 处理Word文档
        if file_ext in ['.doc', '.docx']:
            # 删除同名的.csv文件
            csv_file = os.path.splitext(file_path)[0] + '.csv'
            if os.path.exists(csv_file):
                os.remove(csv_file)

            # 删除对应的table.json文件
            table_file = os.path.splitext(file_path)[0] + '_table.json'
            if os.path.exists(table_file):
                os.remove(table_file)

        # 处理Excel文件
        elif file_ext in ['.xls', '.xlsx']:
            # 删除同名的json文件
            json_file = os.path.splitext(file_path)[0] + '_config.json'
            if os.path.exists(json_file):
                os.remove(json_file)

        # 删除原始文件
        os.remove(file_path)

        return APIResponse(200, f"文件 '{request.filename}' 已成功删除")

    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 删除项目接口
@router.delete("/delete_project", summary="删除项目")
@api_response
async def delete_project(request: ProjectInfo):
    try:
        user_project_directory = os.path.join(
            UPLOAD_DIRECTORY, request.username, request.project_name)
        user_static_directory = os.path.join(
            ROOT_DIRECTORY, "static", request.username, request.project_name)
        if os.path.exists(user_project_directory):
            shutil.rmtree(user_project_directory)
        if os.path.exists(user_static_directory):
            shutil.rmtree(user_static_directory)
        return APIResponse(200, "项目删除成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search", summary="查询标题")
@api_response
async def search_file(request: SearchFileRequest):
    """
    查询指定用户项目目录下特定文件的标题内容。

    Parameters:
    - **username**: 用户名
    - **project_name**: 项目名称
    - **title**: 要查询的标题
    - **format**: 返回格式,可选list(列表)或string(字符串)

    Returns:
    - **results**: 标题下的内容

    Raises:
    - **HTTPException(404)**: 指定的文件或文件内容不存在
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        results = docx_search(
            request.username, request.project_name, request.search_title, request.format)

        if not results:
            raise HTTPException(
                status_code=404, detail=f"文件或文件内容不存在！")  # 404 Not Found

        return APIResponse(200, "查询成功！", results)

    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/project_config", summary="配置项目必要参数")
@api_response
async def set_project_config(request: SetProjectConfigRequest):
    try:
        user_project_directory = os.path.join(
            UPLOAD_DIRECTORY, request.username, request.project_name)
        # 创建config.json文件,如果文件存在则覆盖
        config_file_path = os.path.join(user_project_directory, "config.json")
        with open(config_file_path, "w", encoding="utf-8") as f:
            json.dump(request.model_dump(), f, ensure_ascii=False, indent=4)
        if request.construction_unit:
            save_unit_ratio(request.username, request.project_name,
                            unit_str=request.construction_unit)
        return APIResponse(200, "配置项目必要参数成功")
    except Exception as e:
        logger.error(f"配置项目必要参数失败！{e}")
        raise HTTPException(status_code=500, detail=f"配置项目必要参数失败！{e}")


@router.get("/project_config", summary="获取项目必要参数")
@api_response
async def get_project_config(username: str, project_name: str):
    try:
        user_project_directory = os.path.join(
            UPLOAD_DIRECTORY, username, project_name)
        config_file_path = os.path.join(user_project_directory, "config.json")
        if not os.path.exists(config_file_path):
            raise HTTPException(status_code=404, detail="项目配置文件不存在！")
        with open(config_file_path, "r", encoding="utf-8") as f:
            config = json.load(f)
        return APIResponse(200, "获取项目必要参数成功", config)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
