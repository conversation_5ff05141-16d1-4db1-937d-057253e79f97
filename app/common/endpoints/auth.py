from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, EmailStr, Field
from app.utils.database import DatabaseManager
from app.core.config import settings
from passlib.context import CryptContext
from typing import Optional
from datetime import datetime, timedelta, timezone
from app.utils.response import APIResponse, api_response, RESPONSE_MODELS
from app.schemas import SuccessResponse, ErrorResponse, LoginResponse
from jose import jwt, JWTError
import os
import shutil
from app.core.logging_config import get_logger

logger = get_logger(__name__)

router = APIRouter()

# 创建密码上下文 - 支持多种哈希算法
pwd_context = CryptContext(
    schemes=["sha256_crypt", "bcrypt"],
    deprecated="bcrypt",  # 标记bcrypt为已弃用，但仍能验证旧密码
)


def hash_password(password: str) -> str:
    """统一处理密码哈希，使用sha256_crypt"""
    # 使用默认算法(sha256_crypt)创建新密码哈希
    return pwd_context.hash(password)


def verify_password(password: str, hashed: str) -> bool:
    """统一处理密码验证，支持多种哈希算法"""
    try:
        # 尝试验证密码，支持bcrypt和sha256_crypt
        return pwd_context.verify(password, hashed)
    except Exception as e:
        logger.error(f"密码验证异常: {str(e)}", exc_info=True)
        return False

# 创建获取数据库连接的辅助函数


def get_db():
    db = DatabaseManager(
        host=settings.DB_HOST,
        user=settings.DB_USER,
        port=settings.DB_PORT,
        password=settings.DB_PASSWORD,
        database=settings.DB_NAME
    )
    db.connect()
    return db


class UserRegister(BaseModel):
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    email: Optional[EmailStr] = Field(None, description="电子邮箱地址")
    phone_number: Optional[str] = Field(None, description="电话号码")
    department: Optional[str] = Field(None, description="部门")
    full_name: Optional[str] = Field(None, description="全名")


class UserLogin(BaseModel):
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


class ResetPassword(BaseModel):
    username: str = Field(..., description="用户名")
    old_password: str = Field(..., description="旧密码")
    new_password: str = Field(..., description="新密码")


# JWT 相关配置
SECRET_KEY = settings.SECRET_KEY
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 7 * 24 * 60  # Token 有效期7天（以分钟为单位）


def create_access_token(data: dict):
    """创建访问令牌"""
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + \
        timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


class TokenVerify(BaseModel):
    token: str = Field(..., description="访问令牌")


@router.post(
    "/register",
    summary="用户注册",
    description="创建新用户账户，支持完整的用户信息录入",
    responses={
        200: {"model": SuccessResponse, "description": "注册成功"},
        409: {"model": ErrorResponse, "description": "用户名已存在"},
        422: {"model": ErrorResponse, "description": "参数验证失败"},
        500: {"model": ErrorResponse, "description": "服务器内部错误"}
    },
    tags=["auth"]
)
@api_response
async def register(user: UserRegister):
    """
    ## 用户注册

    创建新的用户账户，用户可以使用注册的账户登录系统并使用各项功能。

    ### 功能特性
    - 用户名唯一性验证
    - 密码安全哈希存储
    - 完整用户信息管理
    - 自动注册时间记录

    ### 注册流程
    1. 验证用户名是否已存在
    2. 对密码进行安全哈希处理
    3. 保存用户信息到数据库
    4. 返回注册结果

    ### 参数要求
    - **username**: 用户名，3-20个字符，仅支持字母、数字、下划线
    - **password**: 密码，至少8个字符，建议包含字母、数字和特殊字符
    - **email**: 邮箱地址，可选，用于找回密码等功能
    - **phone_number**: 手机号码，可选
    - **department**: 部门信息，可选
    - **full_name**: 真实姓名，可选

    ### 安全特性
    - 密码使用bcrypt算法加密存储
    - 用户名重复检查
    - 输入参数验证和清理

    ### 错误处理
    - **409**: 用户名已存在，请选择其他用户名
    - **422**: 参数格式错误，如邮箱格式不正确
    - **500**: 数据库连接失败或其他服务器错误
    """
    try:
        # 获取数据库连接
        db = get_db()

        # 检查用户名是否已存在
        username_query = "SELECT 1 FROM users WHERE username = %s LIMIT 1"
        if db.execute_query(username_query, (user.username,)):
            raise HTTPException(status_code=409, detail="用户名已存在")

        # 哈希密码
        hashed_password = hash_password(user.password)

        # 插入用户数据
        insert_query = """
            INSERT INTO users (
                username, password, email, 
                phone_number, department, full_name
            ) VALUES (%s, %s, %s, %s, %s, %s)
        """
        insert_params = (
            user.username, hashed_password, user.email,
            user.phone_number, user.department, user.full_name
        )

        db.execute_update(insert_query, insert_params)

        # 关闭数据库连接
        db.disconnect()

        return APIResponse(200, "用户注册成功")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"注册过程中发生错误: {str(e)}"
        )


# 删除用户
@router.delete("/delete_user", summary="删除用户")
async def delete_user(username: str):
    """
    Deletes a user from the database and removes their associated files.
    Raises an exception if any step fails.
    """
    db = None
    try:
        # Check if user exists first
        db = get_db()
        check_query = "SELECT 1 FROM users WHERE username = %s"
        result = db.execute_query(check_query, (username,))
        if not result:
            # No need to raise 404 here if called as helper, let caller handle
            logger.warning(f"User {username} not found for deletion.")
            return # Or raise a specific custom exception if needed

        # Delete from database
        db.execute_update("DELETE FROM users WHERE username = %s", (username,))
        logger.info(f"User {username} deleted from database.")

        # Delete user uploaded files
        user_project_directory = os.path.join(settings.UPLOAD_DIRECTORY, username)
        if os.path.exists(user_project_directory):
            shutil.rmtree(user_project_directory)
            logger.info(f"Removed project directory: {user_project_directory}")

        # Delete user static files (if any)
        user_static_directory = os.path.join(settings.PROJECT_ROOT, "static", username)
        if os.path.exists(user_static_directory):
            shutil.rmtree(user_static_directory)
            logger.info(f"Removed static directory: {user_static_directory}")

        # Success, return normally (no specific value needed)
        return

    except Exception as e:
        logger.error(f"Error deleting user {username}: {str(e)}", exc_info=True)
        # Re-raise the exception to be caught by the caller
        raise e
    finally:
        if db:
            db.disconnect()


@router.post(
    "/login",
    summary="用户登录",
    description="用户身份验证，成功后返回JWT访问令牌",
    responses={
        200: {"model": SuccessResponse, "description": "登录成功，返回用户信息和访问令牌"},
        401: {"model": ErrorResponse, "description": "用户名或密码错误"},
        422: {"model": ErrorResponse, "description": "参数验证失败"},
        500: {"model": ErrorResponse, "description": "服务器内部错误"}
    },
    tags=["auth"]
)
@api_response
async def login(user: UserLogin):
    """
    ## 用户登录

    验证用户身份并生成访问令牌，用于后续API调用的身份认证。

    ### 功能特性
    - 用户名密码验证
    - JWT令牌生成
    - 登录时间记录
    - 用户信息返回

    ### 认证流程
    1. 验证用户名是否存在
    2. 验证密码是否正确
    3. 更新最后登录时间
    4. 生成JWT访问令牌
    5. 返回用户信息和令牌

    ### 令牌信息
    - **类型**: JWT (JSON Web Token)
    - **有效期**: 7天
    - **算法**: HS256
    - **包含信息**: 用户ID、用户名、全名

    ### 使用方式
    获取令牌后，在后续API请求的Header中添加：
    ```
    Authorization: Bearer <access_token>
    ```

    ### 参数说明
    - **username**: 注册时使用的用户名
    - **password**: 用户密码（明文，系统会自动验证哈希）

    ### 返回数据
    - **用户信息**: 用户名、全名等基本信息
    - **访问令牌**: 用于API认证的JWT令牌
    - **令牌类型**: bearer

    ### 错误处理
    - **401**: 用户名不存在或密码错误
    - **422**: 请求参数格式错误
    - **500**: 数据库连接失败或令牌生成失败
    """
    db = get_db()

    try:
        query = "SELECT user_id, password, full_name, username FROM users WHERE username = %s"
        params = (user.username,)
        result = db.execute_query(query, params)
        if not result:
            raise HTTPException(status_code=401, detail="用户名错误")

        user_id = result[0]['user_id']
        stored_password = result[0]['password']
        full_name = result[0]['full_name']
        username = result[0]['username']

        if verify_password(user.password, stored_password):
            # 更新最后登录时间
            update_query = "UPDATE users SET last_login_time = %s WHERE user_id = %s"
            update_params = (datetime.now(), user_id)
            try:
                db.execute_update(update_query, update_params)

                # 创建访问令牌
                token_data = {
                    "sub": str(user_id),
                    "username": username,
                    "full_name": full_name
                }
                access_token = create_access_token(token_data)

                # 关闭数据库连接
                db.disconnect()
                return APIResponse(
                    200,
                    "用户登录成功",
                    data={
                        "full_name": full_name,
                        "username": username,
                        "access_token": access_token
                    }
                )
            except Exception as e:
                db.disconnect()
                logger.error(f"更新登录时间失败: {str(e)}", exc_info=True)
                raise HTTPException(status_code=500, detail="登录成功，但更新登录时间失败")
        else:
            db.disconnect()
            raise HTTPException(status_code=401, detail="密码错误")
    except HTTPException:
        db.disconnect()
        raise
    except Exception as e:
        db.disconnect()
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reset-password", summary="重置密码")
@api_response
async def reset_password(data: ResetPassword):
    """
    重置用户密码。

    Parameters:
    - **data**: ResetPassword 对象，包含用户名、旧密码和新密码

    Returns:
    - **message**: 密码重设成功消息

    Raises:
    - **HTTPException(404)**: 用户名不存在
    - **HTTPException(401)**: 旧密码错误
    - **HTTPException(500)**: 数据库操作失败
    """
    # 获取数据库连接
    db = get_db()

    try:
        query = "SELECT password FROM users WHERE username = %s"
        params = (data.username,)
        result = db.execute_query(query, params)
        if not result:
            raise HTTPException(status_code=404, detail="用户名不存在")

        stored_password = result[0]['password']
        if not verify_password(data.old_password, stored_password):
            db.disconnect()
            raise HTTPException(status_code=401, detail="旧密码错误")

        new_hashed_password = hash_password(data.new_password)
        update_query = "UPDATE users SET password = %s WHERE username = %s"
        update_params = (new_hashed_password, data.username)
        try:
            db.execute_update(update_query, update_params)
            db.disconnect()
            return APIResponse(200, "密码重设成功")
        except Exception as e:
            db.disconnect()
            raise HTTPException(status_code=500, detail=str(e))
    except HTTPException:
        db.disconnect()
        raise
    except Exception as e:
        db.disconnect()
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/verify_token", summary="验证访问令牌")
@api_response
async def verify_token(token_data: TokenVerify):
    """
    验证访问令牌的有效性。

    Parameters:
    - **token_data**: TokenVerify 对象，包含访问令牌

    Returns:
    - **message**: 验证成功消息
    - **data**: 用户信息

    Raises:
    - **HTTPException(401)**: 令牌无效或已过期
    """
    try:
        # 解码并验证令牌
        payload = jwt.decode(token_data.token, SECRET_KEY,
                             algorithms=[ALGORITHM])

        # 检查令牌是否过期
        exp = payload.get("exp")
        if not exp or datetime.fromtimestamp(exp) < datetime.utcnow():
            raise HTTPException(status_code=401, detail="令牌已过期")

        # 获取用户信息
        username = payload.get("username")
        full_name = payload.get("full_name")

        if not username:
            raise HTTPException(status_code=401, detail="无效的令牌")

        return APIResponse(
            200,
            "令牌验证成功",
            data={
                "username": username,
                "full_name": full_name
            }
        )
    except JWTError:
        raise HTTPException(status_code=401, detail="无效的令牌")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
