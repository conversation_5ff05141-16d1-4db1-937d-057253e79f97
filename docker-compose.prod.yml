# 定义服务
services:
  app:  # 应用服务配置
    image: office_plugin:latest  # 使用office_plugin:latest镜像
    ports:  # 端口映射
      - "12010:12010"  # 将容器的12010端口映射到宿主机的12010端口
    environment:  # 环境变量配置
      # 应用环境标识，prod代表广东院生产环境
      APP_MODE: prod

      # 加密用户token所需密钥，用于保护用户的会话安全
      SECRET_KEY: sk-hdgw873grfgw78rghwsegrf7283t4gds8

      # 文件上传配置
      UPLOAD_DIRECTORY: uploads # 上传文件目录
      MAX_FILE_SIZE: 52428800 # 允许用户上传的最大文件大小，单位为字节

      # 配套数据库配置
      DB_HOST: mysql # 使用服务名作为主机名
      DB_PORT: 3306 # 容器内部端口
      DB_USER: root # 使用root用户
      DB_PASSWORD: <EMAIL> # 使用root密码
      DB_NAME: gedi # 数据库名称

      # TuGraph图数据库配置
      GDB_HOST: *************
      GDB_USER: admin
      GDB_PASSWORD: gedi@TuGraph

      # LLM配置
      LLM_CONFIGURATIONS_RAW: |-
          - name: "【8卡L40S】"
            model: "Qwen3-30B-A3B"
            base_url: "http://**************/v1"
            api_key: "gpustack_ee06db68136114df_e80417bf3c91767d22417d5cd0165b32"
            temperature: 0.6
            max_tokens: 32768
            stream: false
            weight: 80  # 表示该大模型被选中的相对权重（例如LLM-1的权重为x，LLM-2的权重为y，则LLM-1被选中的概率为x/(x+y)）
          - name: "【8卡H20】"
            model: "Qwen3-235B-A22B"
            base_url: "http://**************/v1"
            api_key: "gpustack_ee06db68136114df_e80417bf3c91767d22417d5cd0165b32"
            temperature: 0.6
            max_tokens: 32768
            stream: false
            weight: 20
        # - name: "【4卡L40S】"
        #   model: "Qwen3-30B-A3B"
        #   base_url: "http://*************:12001/v1"
        #   api_key: "EMPTY"
        #   temperature: 0.6
        #   max_tokens: 32768
        #   stream: false
        #   weight: 25

      # 静态文件访问配置
      STATIC_URL: http://10.150.112.80:12010/static/
      
      # 日志配置
      LOG_LEVEL: INFO
      LOG_DIR: logs
      LOG_FORMAT: human  # 强制使用人类可读的日志格式

    depends_on:
      mysql:
        condition: service_healthy
    restart: always

  mysql:  # MySQL 数据库服务配置
    image: office_plugin_mysql:latest
    ports:
      - "3307:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=<EMAIL>
      - MYSQL_DATABASE=gedi
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: [ "CMD", "mysqladmin", "ping", "-h", "localhost" ]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: always

volumes:
  mysql_data:
