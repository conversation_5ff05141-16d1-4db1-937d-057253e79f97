# "有解"数字助手系统运维手册

## 1. 系统概述

### 1.1 项目简介
"有解"数字助手是一款基于大语言模型的智能办公助手，旨在提高办公效率和文档处理能力。

### 1.2 技术栈
- **后端框架**: FastAPI (Python 3.11)
- **数据库**: MySQL 8.0+
- **图数据库**: TuGraph
- **AI模型**: 基于OpenAI接口的大语言模型
- **容器化**: Docker & Docker Compose
- **Web服务器**: Uvicorn
- **反向代理**: 可选Nginx

### 1.3 系统要求
- **最低配置**:
  - CPU: 8核心
  - 内存: 16GB RAM
  - 磁盘: 100GB可用空间（SSD）
  - 网络: 千兆网络连接

- **推荐配置**:
  - CPU: 16核心或以上
  - 内存: 32GB RAM或以上
  - 磁盘: 500GB可用空间（NVMe SSD推荐）
  - 网络: 万兆网络或多网卡绑定

### 1.4 端口配置
- **应用端口**: 12010 (HTTP)
- **MySQL端口**: 3307 (映射到容器内3306)
- **TuGraph端口**: 根据配置而定

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   FastAPI应用   │    │   MySQL数据库   │
│   (客户端)      │◄──►│   (容器)        │◄──►│   (容器)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   TuGraph       │
                       │   (图数据库)    │
                       └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   LLM服务       │
                       │   (外部API)     │
                       └─────────────────┘
```

### 2.2 目录结构
```
office_plugins/
├── app/                    # 应用主目录
│   ├── admin_api/         # 管理后台API
│   ├── common/            # 通用功能模块
│   ├── core/              # 核心配置
│   ├── docs_api/          # 文档API
│   ├── excel_api/         # Excel处理API
│   ├── frontend_version/  # 前端版本管理
│   ├── middleware/        # 中间件
│   ├── templates/         # 模板文件
│   └── utils/             # 工具类
├── docker/                # Docker配置
├── logs/                  # 日志文件
├── static/                # 静态文件
├── uploads/               # 上传文件
├── frontend_packages/     # 前端安装包
└── documents/             # 文档模板
```

## 3. 环境配置

### 3.1 生产环境配置 (docker-compose.prod.yml)
```yaml
environment:
  APP_MODE: prod
  SECRET_KEY: sk-hdgw873grfgw78rghwsegrf7283t4gds8
  UPLOAD_DIRECTORY: uploads
  MAX_FILE_SIZE: 52428800
  
  # 数据库配置
  DB_HOST: mysql
  DB_PORT: 3306
  DB_USER: root
  DB_PASSWORD: <EMAIL>
  DB_NAME: gedi
  
  # 图数据库配置
  GDB_HOST: *************
  GDB_USER: admin
  GDB_PASSWORD: gedi@TuGraph
  
  # 静态文件配置
  STATIC_URL: http://*************:12010/static/
  
  # 日志配置
  LOG_LEVEL: INFO
  LOG_DIR: logs
  LOG_FORMAT: human
```

### 3.2 LLM配置
系统支持多个LLM服务配置，通过权重进行负载均衡：

```yaml
LLM_CONFIGURATIONS_RAW: |-
  - name: "【8卡L40S】"
    model: "Qwen3-30B-A3B"
    base_url: "http://**************/v1"
    api_key: "gpustack_ee06db68136114df_e80417bf3c91767d22417d5cd0165b32"
    temperature: 0.6
    max_tokens: 32768
    stream: false
    weight: 80
  - name: "【8卡H20】"
    model: "Qwen3-235B-A22B"
    base_url: "http://**************/v1"
    api_key: "gpustack_ee06db68136114df_e80417bf3c91767d22417d5cd0165b32"
    temperature: 0.6
    max_tokens: 32768
    stream: false
    weight: 20
```

#### 3.2.1 权重参数详细说明

**weight参数**是LLM服务负载均衡的核心配置，用于控制不同LLM服务被选中的概率：

- **权重计算原理**: 系统根据各服务的权重值计算选中概率
  - 总权重 = 所有服务权重之和 (80 + 20 = 100)
  - 服务A选中概率 = 服务A权重 / 总权重 (80/100 = 80%)
  - 服务B选中概率 = 服务B权重 / 总权重 (20/100 = 20%)

- **权重配置策略**:
  - **高性能服务**: 设置较高权重(如80)，承担主要请求负载
  - **备用服务**: 设置较低权重(如20)，作为负载分担和故障转移
  - **测试服务**: 设置极低权重(如5)，仅用于少量测试请求

- **动态调整建议**:
  - 根据服务器性能和响应时间调整权重
  - 服务器维护时可临时设置权重为0
  - 新服务上线时可先设置较低权重进行灰度测试

- **权重配置示例**:
  ```yaml
  # 主力服务器 - 承担80%请求
  weight: 80

  # 备用服务器 - 承担15%请求
  weight: 15

  # 测试服务器 - 承担5%请求
  weight: 5

  # 维护中服务器 - 暂停服务
  weight: 0
  ```

## 4. 部署指南

### 4.1 生产环境部署

#### 4.1.1 镜像准备
```bash
# 在开发环境构建镜像
docker-compose -f docker-compose.dev.yml build

# 标记为生产版本
docker tag office_plugin:dev office_plugin:latest
docker tag office_plugin_mysql:dev office_plugin_mysql:latest

# 导出镜像
docker save -o office_plugin.tar office_plugin:latest
docker save -o office_plugin_mysql.tar office_plugin_mysql:latest

# 压缩镜像（可选）
docker save office_plugin:latest | xz -z -T0 -9 -v > office_plugin.tar.xz
docker save office_plugin_mysql:latest | xz -z -T0 -9 -v > office_plugin_mysql.tar.xz
```

#### 4.1.2 生产环境部署
```bash
# 1. 传输文件到生产服务器
# - office_plugin.tar
# - office_plugin_mysql.tar
# - docker-compose.prod.yml
# - docker/init.sql

# 2. 加载镜像
docker load -i office_plugin.tar
docker load -i office_plugin_mysql.tar

# 3. 启动服务
docker-compose -f docker-compose.prod.yml up -d

# 4. 验证部署
docker-compose -f docker-compose.prod.yml ps
curl http://localhost:12010/
```

### 4.2 健康检查
系统包含以下健康检查机制：

#### 4.2.1 MySQL健康检查
```yaml
healthcheck:
  test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
  interval: 10s
  timeout: 5s
  retries: 5
```

#### 4.2.2 应用健康检查
```bash
# 检查应用状态
curl -f http://localhost:12010/ || exit 1

# 检查数据库连接
docker-compose -f docker-compose.prod.yml exec app python -c "
from app.core.config import settings
from app.utils.database import DatabaseManager
db = DatabaseManager(settings.DB_HOST, settings.DB_PORT, settings.DB_USER, settings.DB_PASSWORD, settings.DB_NAME)
db.connect()
print('数据库连接正常')
db.disconnect()
"
```

## 5. 监控与日志

### 5.1 日志系统

#### 5.1.1 日志配置
系统采用分层日志记录：
- **控制台输出**: 开发环境彩色格式，生产环境简洁格式
- **文件记录**: JSON格式，便于日志分析

#### 5.1.2 日志文件
```
logs/
├── app.log      # 应用主日志（所有级别）
├── error.log    # 错误日志（ERROR和CRITICAL）
└── access.log   # 访问日志（API请求）
```

#### 5.1.3 日志轮转
- **文件大小**: 10MB自动轮转
- **保留数量**: 
  - app.log: 5个备份文件
  - error.log: 10个备份文件
- **编码**: UTF-8

#### 5.1.4 日志级别
- **开发环境**: INFO级别
- **测试环境**: INFO级别  
- **生产环境**: INFO级别（控制台WARNING）

### 5.2 监控指标

#### 5.2.1 系统监控
```bash
# 容器状态监控
docker-compose -f docker-compose.prod.yml ps

# 资源使用监控
docker stats

# 磁盘使用监控
df -h
du -sh uploads/
du -sh logs/
```

#### 5.2.2 应用监控
```bash
# API响应监控
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:12010/

# 数据库连接监控
docker-compose -f docker-compose.prod.yml exec mysql mysqladmin ping

# 日志监控
tail -f logs/app.log | grep ERROR
tail -f logs/access.log
```



## 6. 数据库管理

### 6.1 数据库架构

#### 6.1.1 MySQL配置
- **版本**: MySQL 8.0+
- **字符集**: utf8mb4
- **存储引擎**: InnoDB
- **端口**: 3306 (容器内) / 3307 (宿主机)

#### 6.1.2 数据库结构
```sql
-- 用户表
CREATE TABLE users (
    user_id INT NOT NULL AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    phone_number VARCHAR(20),
    department VARCHAR(100),
    full_name VARCHAR(100),
    is_admin TINYINT(1) DEFAULT 0,
    registration_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id)
);
```

#### 6.1.3 数据库连接池
- **连接池大小**: 5个连接
- **连接超时**: 5秒
- **事务隔离级别**: READ COMMITTED
- **自动重连**: 启用

### 6.2 数据库维护

#### 6.2.1 日常维护命令
```bash
# 进入MySQL容器
docker-compose -f docker-compose.prod.yml exec mysql mysql -u root -p

# 查看数据库状态
SHOW STATUS;
SHOW PROCESSLIST;

# 查看表大小
SELECT
    table_name AS "表名",
    round(((data_length + index_length) / 1024 / 1024), 2) AS "大小(MB)"
FROM information_schema.tables
WHERE table_schema = "gedi"
ORDER BY (data_length + index_length) DESC;

# 优化表
OPTIMIZE TABLE users;
```

#### 6.2.2 性能监控
```sql
-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- 查看连接数
SHOW STATUS LIKE 'Connections';
SHOW STATUS LIKE 'Threads_connected';

-- 查看缓存命中率
SHOW STATUS LIKE 'Qcache_hits';
SHOW STATUS LIKE 'Qcache_inserts';
```

### 6.3 数据备份策略

#### 6.3.1 自动备份脚本
```bash
#!/bin/bash
# backup_database.sh

BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
CONTAINER_NAME="office_plugins_mysql_1"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
docker exec $CONTAINER_NAME mysqldump -<NAME_EMAIL> \
    --single-transaction \
    --routines \
    --triggers \
    gedi > $BACKUP_DIR/gedi_backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/gedi_backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "数据库备份完成: gedi_backup_$DATE.sql.gz"
```

#### 6.3.2 备份验证
```bash
# 验证备份文件完整性
gunzip -t /backup/mysql/gedi_backup_20250718_120000.sql.gz

# 测试恢复（在测试环境）
gunzip -c /backup/mysql/gedi_backup_20250718_120000.sql.gz | \
docker exec -i test_mysql mysql -u root -ppassword test_db
```

## 7. 安全管理

### 7.1 认证与授权

#### 7.1.1 用户认证
- **认证方式**: JWT Token
- **Token有效期**: 7天
- **密码加密**: SHA256_CRYPT (支持BCrypt向后兼容)
- **密钥管理**: 环境变量存储

#### 7.1.2 权限管理
```python
# 管理员权限验证
def verify_admin_permission(user_id: int) -> bool:
    query = "SELECT is_admin FROM users WHERE user_id = %s"
    result = db.execute_query(query, (user_id,))
    return result and result[0]['is_admin'] == 1

# API权限装饰器
@require_admin
async def admin_only_endpoint():
    pass
```

#### 7.1.3 API安全
- **CORS配置**: 允许所有来源（生产环境需限制）
- **请求大小限制**: 50MB
- **文件类型验证**: 支持的文件格式检查
- **SQL注入防护**: 参数化查询

### 7.2 数据安全

#### 7.2.1 敏感数据保护
```bash
# 环境变量安全
# 不在日志中记录密码
logger.info(f"数据库连接地址: {settings.DB_HOST}")
logger.info(f"数据库连接用户名: {settings.DB_USER}")
# 密码不记录到日志

# 文件权限设置
chmod 600 docker-compose.prod.yml
chmod 700 uploads/
```

#### 7.2.2 网络安全
```yaml
# Docker网络隔离
networks:
  app-network:
    driver: bridge
    internal: true  # 内部网络，不允许外部访问

services:
  app:
    networks:
      - app-network
  mysql:
    networks:
      - app-network
    # 不暴露到宿主机网络（生产环境）
```

### 7.3 安全审计

#### 7.3.1 访问日志
```python
# API访问记录
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time

    logger.info(f"API {request.method} {request.url.path} {response.status_code} - {process_time:.2f}s")
    return response
```

#### 7.3.2 安全检查清单
- [ ] 定期更新系统依赖
- [ ] 检查弱密码
- [ ] 审查用户权限
- [ ] 监控异常登录
- [ ] 检查文件上传安全
- [ ] 验证API访问频率

## 8. 备份与恢复

### 8.1 备份策略

#### 8.1.1 数据备份分类
1. **数据库备份**
   - 全量备份：每日凌晨2点
   - 增量备份：每4小时
   - 保留期：30天

2. **文件备份**
   - 上传文件：每日备份
   - 配置文件：版本控制
   - 日志文件：按需备份

3. **应用备份**
   - Docker镜像：版本标记
   - 配置文件：Git管理

#### 8.1.2 备份脚本
```bash
#!/bin/bash
# full_backup.sh - 完整备份脚本

BACKUP_ROOT="/backup"
DATE=$(date +%Y%m%d)
PROJECT_ROOT="/opt/office_plugins"

# 创建备份目录
mkdir -p $BACKUP_ROOT/$DATE

# 1. 数据库备份
echo "开始数据库备份..."
docker exec office_plugins_mysql_1 mysqldump -<NAME_EMAIL> \
    --single-transaction --routines --triggers gedi | \
    gzip > $BACKUP_ROOT/$DATE/database_$DATE.sql.gz

# 2. 上传文件备份
echo "开始文件备份..."
tar -czf $BACKUP_ROOT/$DATE/uploads_$DATE.tar.gz -C $PROJECT_ROOT uploads/

# 3. 配置文件备份
echo "开始配置备份..."
tar -czf $BACKUP_ROOT/$DATE/config_$DATE.tar.gz -C $PROJECT_ROOT \
    docker-compose.prod.yml docker/ logs/ static/

# 4. 创建备份清单
echo "创建备份清单..."
cat > $BACKUP_ROOT/$DATE/backup_manifest.txt << EOF
备份日期: $(date)
数据库备份: database_$DATE.sql.gz
文件备份: uploads_$DATE.tar.gz
配置备份: config_$DATE.tar.gz
备份大小: $(du -sh $BACKUP_ROOT/$DATE | cut -f1)
EOF

# 5. 清理旧备份
find $BACKUP_ROOT -type d -name "20*" -mtime +30 -exec rm -rf {} \;

echo "备份完成: $BACKUP_ROOT/$DATE"
```

### 8.2 恢复程序

#### 8.2.1 数据库恢复
```bash
#!/bin/bash
# restore_database.sh

BACKUP_FILE=$1
if [ -z "$BACKUP_FILE" ]; then
    echo "用法: $0 <backup_file.sql.gz>"
    exit 1
fi

echo "警告：此操作将覆盖现有数据库！"
read -p "确认继续？(yes/no): " confirm

if [ "$confirm" = "yes" ]; then
    # 停止应用
    docker-compose -f docker-compose.prod.yml stop app

    # 恢复数据库
    gunzip -c $BACKUP_FILE | \
    docker exec -i office_plugins_mysql_1 mysql -<NAME_EMAIL> gedi

    # 重启应用
    docker-compose -f docker-compose.prod.yml start app

    echo "数据库恢复完成"
else
    echo "操作已取消"
fi
```

#### 8.2.2 完整系统恢复
```bash
#!/bin/bash
# disaster_recovery.sh - 灾难恢复脚本

BACKUP_DATE=$1
BACKUP_ROOT="/backup"
PROJECT_ROOT="/opt/office_plugins"

if [ -z "$BACKUP_DATE" ]; then
    echo "用法: $0 <backup_date> (格式: YYYYMMDD)"
    exit 1
fi

BACKUP_DIR="$BACKUP_ROOT/$BACKUP_DATE"

if [ ! -d "$BACKUP_DIR" ]; then
    echo "备份目录不存在: $BACKUP_DIR"
    exit 1
fi

echo "开始灾难恢复，备份日期: $BACKUP_DATE"
echo "备份清单:"
cat $BACKUP_DIR/backup_manifest.txt

read -p "确认开始恢复？(yes/no): " confirm
if [ "$confirm" != "yes" ]; then
    echo "操作已取消"
    exit 0
fi

# 1. 停止所有服务
echo "停止服务..."
cd $PROJECT_ROOT
docker-compose -f docker-compose.prod.yml down

# 2. 备份当前状态
echo "备份当前状态..."
mkdir -p /tmp/current_backup
cp -r uploads/ /tmp/current_backup/ 2>/dev/null || true

# 3. 恢复文件
echo "恢复上传文件..."
rm -rf uploads/
tar -xzf $BACKUP_DIR/uploads_$BACKUP_DATE.tar.gz -C .

# 4. 恢复配置
echo "恢复配置文件..."
tar -xzf $BACKUP_DIR/config_$BACKUP_DATE.tar.gz -C .

# 5. 启动数据库
echo "启动数据库..."
docker-compose -f docker-compose.prod.yml up -d mysql

# 等待数据库启动
sleep 30

# 6. 恢复数据库
echo "恢复数据库..."
gunzip -c $BACKUP_DIR/database_$BACKUP_DATE.sql.gz | \
docker exec -i $(docker-compose -f docker-compose.prod.yml ps -q mysql) \
mysql -<NAME_EMAIL> gedi

# 7. 启动应用
echo "启动应用..."
docker-compose -f docker-compose.prod.yml up -d

# 8. 验证恢复
echo "验证系统状态..."
sleep 10
if curl -f http://localhost:12010/ > /dev/null 2>&1; then
    echo "✅ 系统恢复成功"
else
    echo "❌ 系统恢复失败，请检查日志"
fi

echo "恢复完成。当前状态备份在: /tmp/current_backup"
```

## 9. 故障排查

### 9.1 常见问题诊断

#### 9.1.1 应用无法启动
```bash
# 检查容器状态
docker-compose -f docker-compose.prod.yml ps

# 查看启动日志
docker-compose -f docker-compose.prod.yml logs app

# 检查环境变量
docker-compose -f docker-compose.prod.yml exec app env | grep -E "(DB_|SECRET_|LLM_)"

# 检查端口占用
netstat -tlnp | grep 12010
```

**常见原因及解决方案**:
1. **端口被占用**: 修改端口或停止占用进程
2. **环境变量缺失**: 检查docker-compose.yml配置
3. **数据库连接失败**: 检查数据库服务状态
4. **权限问题**: 检查文件和目录权限
5. **Python依赖冲突**: 检查requirements.txt版本兼容性
6. **磁盘空间不足**: 清理日志或扩容磁盘
7. **Docker网络问题**: 重建Docker网络或检查网络配置
8. **内存不足**: 增加容器内存限制或减少并发请求

#### 9.1.2 数据库连接问题
```bash
# 检查MySQL容器状态
docker-compose -f docker-compose.prod.yml exec mysql mysqladmin ping

# 测试数据库连接
docker-compose -f docker-compose.prod.yml exec app python -c "
from app.utils.database import DatabaseManager
from app.core.config import settings
db = DatabaseManager(settings.DB_HOST, settings.DB_PORT, settings.DB_USER, settings.DB_PASSWORD, settings.DB_NAME)
try:
    db.connect()
    print('数据库连接成功')
    db.disconnect()
except Exception as e:
    print(f'数据库连接失败: {e}')
"

# 检查数据库日志
docker-compose -f docker-compose.prod.yml logs mysql
```

**常见数据库问题**:
1. **连接数过多**: 检查连接池配置，关闭空闲连接
2. **认证失败**: 检查用户名密码配置
3. **数据库崩溃**: 检查MySQL错误日志，可能需要恢复
4. **网络隔离**: 检查容器网络是否正常通信
5. **权限不足**: 检查用户权限配置
6. **表锁定**: 检查长时间运行的事务或查询

#### 9.1.3 性能问题
```bash
# 检查系统资源
docker stats

# 检查磁盘空间
df -h
du -sh uploads/
du -sh logs/

# 检查内存使用
free -h

# 检查API响应时间
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:12010/
```

**性能问题解决方案**:
1. **API响应慢**: 检查数据库查询优化，添加索引
2. **内存泄漏**: 检查Python进程内存增长，可能需要重启
3. **CPU使用率高**: 检查是否有计算密集型任务，考虑异步处理
4. **磁盘I/O瓶颈**: 使用SSD，优化文件读写操作
5. **网络延迟**: 检查网络配置，减少不必要的网络请求

#### 9.1.4 文件上传问题
```bash
# 检查上传目录权限
ls -la uploads/

# 检查磁盘空间
df -h

# 检查上传配置
docker-compose -f docker-compose.prod.yml exec app python -c "
from app.core.config import settings
print(f'上传目录: {settings.UPLOAD_DIRECTORY}')
print(f'最大文件大小: {settings.MAX_FILE_SIZE/1024/1024}MB')
"
```

**常见上传问题**:
1. **权限不足**: 确保容器内用户有写入权限
2. **磁盘空间不足**: 清理旧文件或扩容
3. **文件大小限制**: 检查MAX_FILE_SIZE配置
4. **文件类型限制**: 检查文件类型验证逻辑
5. **上传超时**: 增加Nginx或FastAPI的超时设置

#### 9.1.5 LLM服务连接问题
```bash
# 检查LLM配置
docker-compose -f docker-compose.prod.yml exec app python -c "
from app.core.config import settings
print(f'LLM配置数量: {len(settings.LLM_CONFIGURATIONS)}')
for i, config in enumerate(settings.LLM_CONFIGURATIONS):
    print(f'LLM #{i+1}: {config.get(\"name\")} - URL: {config.get(\"base_url\")}')
"

# 测试LLM连接
docker-compose -f docker-compose.prod.yml exec app python -c "
from app.utils.llm import chat_with_llm
try:
    response = chat_with_llm('测试消息')
    print('LLM连接成功')
except Exception as e:
    print(f'LLM连接失败: {e}')
"
```

**LLM服务问题解决方案**:
1. **API密钥无效**: 更新API密钥配置
2. **服务不可用**: 检查LLM服务器状态
3. **网络隔离**: 确保容器可以访问LLM服务
4. **请求超时**: 增加超时设置
5. **配额限制**: 检查API使用配额
6. **参数错误**: 检查请求参数格式

#### 9.1.6 用户认证问题
```bash
# 检查JWT密钥配置
docker-compose -f docker-compose.prod.yml exec app python -c "
from app.core.config import settings
print(f'JWT密钥已配置: {bool(settings.SECRET_KEY)}')
"

# 检查用户数据库
docker-compose -f docker-compose.prod.yml exec mysql mysql -<NAME_EMAIL> -e "SELECT COUNT(*) FROM gedi.users;"
```

**认证问题解决方案**:
1. **Token过期**: 检查ACCESS_TOKEN_EXPIRE_MINUTES设置
2. **密钥不匹配**: 确保所有服务使用相同的SECRET_KEY
3. **用户不存在**: 检查用户数据库记录
4. **密码哈希算法**: 确保使用正确的密码验证方法
5. **Cookie/Header问题**: 检查前端Token传递方式

### 9.2 日志分析

#### 9.2.1 错误日志分析
```bash
# 查看最近的错误
tail -100 logs/error.log | jq '.'

# 统计错误类型
grep -o '"level":"ERROR"' logs/app.log | wc -l

# 查找特定错误
grep "数据库连接失败" logs/app.log | tail -10

# 分析API错误
grep '"status_code":5' logs/access.log | tail -20
```

#### 9.2.2 性能日志分析
```bash
# 分析慢请求
awk -F'"' '/process_time/ {if($8 > 5) print $0}' logs/access.log

# 统计API调用频率
grep -o '"path":"[^"]*"' logs/access.log | sort | uniq -c | sort -nr

# 分析用户活动
grep -o '"username":"[^"]*"' logs/app.log | sort | uniq -c
```

### 9.3 故障处理流程

#### 9.3.1 紧急故障处理
1. **立即响应** (5分钟内)
   - 确认故障范围
   - 通知相关人员
   - 启动应急预案

2. **快速诊断** (15分钟内)
   - 检查系统状态
   - 分析错误日志
   - 确定故障原因

3. **临时修复** (30分钟内)
   - 重启服务
   - 切换备用系统
   - 回滚到稳定版本

4. **根本修复** (2小时内)
   - 修复根本问题
   - 验证修复效果
   - 更新文档

#### 9.3.2 故障记录模板
```markdown
## 故障报告

**故障时间**: 2025-07-18 14:30:00
**发现方式**: 监控告警/用户反馈
**影响范围**: 全部用户/部分功能
**故障级别**: P0(紧急)/P1(高)/P2(中)/P3(低)

### 故障现象
- 具体症状描述
- 错误信息
- 影响的功能

### 故障原因
- 根本原因分析
- 触发条件

### 处理过程
1. 14:30 - 发现故障
2. 14:35 - 开始诊断
3. 14:45 - 实施临时修复
4. 15:00 - 故障恢复

### 解决方案
- 临时解决方案
- 永久解决方案

### 预防措施
- 监控改进
- 流程优化
- 技术改进

### 经验教训
- 问题总结
- 改进建议
```

## 10. 应急恢复方案

### 10.1 应急响应等级

#### 10.1.1 故障等级定义
- **P0 - 紧急**: 系统完全不可用，影响所有用户
- **P1 - 高优先级**: 核心功能不可用，影响大部分用户
- **P2 - 中优先级**: 部分功能异常，影响部分用户
- **P3 - 低优先级**: 轻微问题，不影响主要功能

#### 10.1.2 响应时间要求
- **P0**: 5分钟内响应，30分钟内恢复
- **P1**: 15分钟内响应，2小时内恢复
- **P2**: 1小时内响应，8小时内恢复
- **P3**: 4小时内响应，24小时内恢复

### 10.2 应急恢复程序

#### 10.2.1 快速重启方案
```bash
#!/bin/bash
# emergency_restart.sh - 紧急重启脚本

echo "=== 紧急重启程序 ==="
echo "时间: $(date)"

# 1. 停止所有服务
echo "停止服务..."
docker-compose -f docker-compose.prod.yml down

# 2. 清理可能的问题
echo "清理临时文件..."
docker system prune -f
docker volume prune -f

# 3. 检查磁盘空间
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 90 ]; then
    echo "警告: 磁盘空间不足 ($DISK_USAGE%)"
    # 清理日志文件
    find logs/ -name "*.log" -mtime +7 -delete
    # 清理临时文件
    find uploads/ -name "*.tmp" -delete
fi

# 4. 重启服务
echo "重启服务..."
docker-compose -f docker-compose.prod.yml up -d

# 5. 等待服务启动
echo "等待服务启动..."
sleep 30

# 6. 验证服务状态
echo "验证服务状态..."
if curl -f http://localhost:12010/ > /dev/null 2>&1; then
    echo "✅ 服务重启成功"
    # 发送成功通知
    echo "服务已成功重启 - $(date)" | mail -s "系统恢复通知" <EMAIL>
else
    echo "❌ 服务重启失败"
    # 发送失败通知
    echo "服务重启失败，需要人工介入 - $(date)" | mail -s "系统故障通知" <EMAIL>
fi

echo "=== 重启程序完成 ==="
```

#### 10.2.2 数据库应急恢复
```bash
#!/bin/bash
# emergency_db_recovery.sh - 数据库应急恢复

echo "=== 数据库应急恢复 ==="

# 1. 停止应用服务
docker-compose -f docker-compose.prod.yml stop app

# 2. 检查数据库状态
if ! docker-compose -f docker-compose.prod.yml exec mysql mysqladmin ping > /dev/null 2>&1; then
    echo "数据库无响应，尝试重启..."
    docker-compose -f docker-compose.prod.yml restart mysql
    sleep 30
fi

# 3. 检查数据完整性
echo "检查数据完整性..."
docker-compose -f docker-compose.prod.yml exec mysql mysqlcheck -<NAME_EMAIL> --all-databases

# 4. 如果数据损坏，从最新备份恢复
LATEST_BACKUP=$(ls -t /backup/*/database_*.sql.gz | head -1)
if [ -n "$LATEST_BACKUP" ]; then
    echo "发现最新备份: $LATEST_BACKUP"
    read -p "是否从备份恢复？(yes/no): " confirm
    if [ "$confirm" = "yes" ]; then
        echo "从备份恢复数据库..."
        gunzip -c $LATEST_BACKUP | \
        docker exec -i $(docker-compose -f docker-compose.prod.yml ps -q mysql) \
        mysql -<NAME_EMAIL> gedi
    fi
fi

# 5. 重启应用
docker-compose -f docker-compose.prod.yml start app

echo "=== 数据库恢复完成 ==="
```

#### 10.2.3 完整灾难恢复
```bash
#!/bin/bash
# disaster_recovery_plan.sh - 完整灾难恢复计划

echo "=== 灾难恢复计划 ==="
echo "警告: 此操作将完全重建系统"

# 确认操作
read -p "确认执行完整灾难恢复？(CONFIRM): " confirm
if [ "$confirm" != "CONFIRM" ]; then
    echo "操作已取消"
    exit 0
fi

# 1. 记录当前状态
echo "记录当前状态..."
mkdir -p /tmp/disaster_recovery_$(date +%Y%m%d_%H%M%S)
RECOVERY_LOG="/tmp/disaster_recovery_$(date +%Y%m%d_%H%M%S)/recovery.log"
exec > >(tee -a $RECOVERY_LOG)
exec 2>&1

echo "开始时间: $(date)"
echo "操作员: $(whoami)"

# 2. 停止所有服务
echo "停止所有服务..."
docker-compose -f docker-compose.prod.yml down -v

# 3. 清理环境
echo "清理Docker环境..."
docker system prune -af
docker volume prune -f

# 4. 恢复最新备份
LATEST_BACKUP_DIR=$(ls -td /backup/20* | head -1)
if [ -z "$LATEST_BACKUP_DIR" ]; then
    echo "错误: 未找到可用备份"
    exit 1
fi

echo "使用备份: $LATEST_BACKUP_DIR"

# 5. 恢复配置文件
echo "恢复配置文件..."
tar -xzf $LATEST_BACKUP_DIR/config_*.tar.gz

# 6. 恢复上传文件
echo "恢复上传文件..."
tar -xzf $LATEST_BACKUP_DIR/uploads_*.tar.gz

# 7. 重新构建和启动服务
echo "重新启动服务..."
docker-compose -f docker-compose.prod.yml up -d mysql

# 等待数据库启动
echo "等待数据库启动..."
sleep 60

# 8. 恢复数据库
echo "恢复数据库..."
gunzip -c $LATEST_BACKUP_DIR/database_*.sql.gz | \
docker exec -i $(docker-compose -f docker-compose.prod.yml ps -q mysql) \
mysql -<NAME_EMAIL> gedi

# 9. 启动应用
echo "启动应用..."
docker-compose -f docker-compose.prod.yml up -d app

# 10. 验证恢复
echo "验证系统恢复..."
sleep 30

# 检查服务状态
if curl -f http://localhost:12010/ > /dev/null 2>&1; then
    echo "✅ 灾难恢复成功"
    echo "恢复完成时间: $(date)"

    # 发送成功通知
    cat << EOF | mail -s "灾难恢复成功" <EMAIL>
系统已成功从灾难中恢复

恢复详情:
- 开始时间: $(head -1 $RECOVERY_LOG | grep "开始时间")
- 完成时间: $(date)
- 使用备份: $LATEST_BACKUP_DIR
- 恢复日志: $RECOVERY_LOG

请验证系统功能是否正常。
EOF

else
    echo "❌ 灾难恢复失败"

    # 发送失败通知
    cat << EOF | mail -s "灾难恢复失败" <EMAIL>
系统灾难恢复失败，需要人工介入

详情:
- 开始时间: $(head -1 $RECOVERY_LOG | grep "开始时间")
- 失败时间: $(date)
- 恢复日志: $RECOVERY_LOG

请立即检查系统状态。
EOF

fi

echo "=== 灾难恢复计划完成 ==="
```

### 10.3 应急联系方式

#### 10.3.1 应急响应团队
```
主要负责人:
- 系统管理员: <EMAIL> / 138-0000-0000
- 技术负责人: <EMAIL> / 139-0000-0000
- 业务负责人: <EMAIL> / 137-0000-0000

备用联系人:
- 运维工程师: <EMAIL> / 136-0000-0000
- 开发工程师: <EMAIL> / 135-0000-0000
```

#### 10.3.2 外部支持
```
云服务商支持:
- 阿里云: 95187
- 腾讯云: 95716
- 华为云: 4000-955-988

硬件供应商:
- 服务器厂商: <EMAIL>
- 网络设备: <EMAIL>
```

### 10.4 应急资源清单

#### 10.4.1 备用硬件
- 备用服务器: 1台 (规格相同)
- 备用网络设备: 交换机、路由器
- 备用存储设备: 移动硬盘、U盘

#### 10.4.2 备用软件
- 系统镜像: 最新版本Docker镜像
- 数据库备份: 最近7天的完整备份
- 配置文件: Git仓库中的最新配置

#### 10.4.3 应急工具
```bash
# 应急工具包
emergency_tools/
├── health_check.sh      # 健康检查脚本
├── emergency_restart.sh # 紧急重启脚本
├── backup_restore.sh    # 备份恢复脚本
├── network_test.sh      # 网络测试脚本
├── performance_check.sh # 性能检查脚本
└── log_analyzer.sh      # 日志分析脚本
```

## 11. 性能优化

### 11.1 系统性能监控

#### 11.1.1 关键性能指标(KPI)
- **响应时间**: API平均响应时间 < 2秒
- **吞吐量**: 并发用户数 > 100
- **可用性**: 系统可用率 > 99.5%
- **资源使用率**: CPU < 80%, 内存 < 85%, 磁盘 < 90%

#### 11.1.2 性能监控脚本
```bash
#!/bin/bash
# performance_monitor.sh

echo "=== 系统性能监控报告 ==="
echo "时间: $(date)"

# CPU使用率
echo "CPU使用率:"
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//'

# 内存使用率
echo "内存使用率:"
free | grep Mem | awk '{printf "%.2f%%\n", $3/$2 * 100.0}'

# 磁盘使用率
echo "磁盘使用率:"
df -h | grep -E "/$|/opt"

# Docker容器状态
echo "容器资源使用:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"

# API响应时间测试
echo "API响应时间:"
curl -w "响应时间: %{time_total}s\n" -o /dev/null -s http://localhost:12010/

# 数据库连接数
echo "数据库连接数:"
docker-compose -f docker-compose.prod.yml exec mysql mysql -<NAME_EMAIL> -e "SHOW STATUS LIKE 'Threads_connected';" | tail -1

echo "=== 监控报告结束 ==="
```

### 11.2 性能优化策略

#### 11.2.1 应用层优化
```python
# 数据库连接池优化
class DatabaseManager:
    def __init__(self, pool_size: int = 10):  # 增加连接池大小
        self.pool_size = pool_size

# API响应缓存
from functools import lru_cache

@lru_cache(maxsize=128)
def get_cached_data(key: str):
    # 缓存频繁访问的数据
    pass

# 异步处理优化
import asyncio
async def process_large_file(file_path: str):
    # 异步处理大文件
    pass
```

#### 11.2.2 数据库优化
```sql
-- 添加索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_last_login ON users(last_login_time);

-- 查询优化
EXPLAIN SELECT * FROM users WHERE username = 'test';

-- 配置优化 (my.cnf)
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
max_connections = 200
query_cache_size = 64M
```

#### 11.2.3 系统层优化
```bash
# 系统参数优化
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 65535' >> /etc/sysctl.conf
sysctl -p

# Docker优化
# docker-compose.yml
services:
  app:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
```

### 11.3 容量规划

#### 11.3.1 存储容量规划
```bash
# 存储增长预测脚本
#!/bin/bash
# storage_planning.sh

echo "=== 存储容量规划 ==="

# 当前使用情况
CURRENT_UPLOADS=$(du -sh uploads/ | cut -f1)
CURRENT_LOGS=$(du -sh logs/ | cut -f1)
CURRENT_DB=$(docker exec $(docker-compose -f docker-compose.prod.yml ps -q mysql) \
    mysql -<NAME_EMAIL> -e "
    SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size (MB)'
    FROM information_schema.tables
    WHERE table_schema='gedi';" | tail -1)

echo "当前存储使用:"
echo "- 上传文件: $CURRENT_UPLOADS"
echo "- 日志文件: $CURRENT_LOGS"
echo "- 数据库: ${CURRENT_DB}MB"

# 增长趋势分析
echo "存储增长趋势:"
find uploads/ -type f -mtime -30 | xargs ls -la | awk '{sum+=$5} END {print "最近30天新增: " sum/1024/1024 "MB"}'

# 容量预警
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "⚠️  磁盘使用率过高: ${DISK_USAGE}%"
    echo "建议清理或扩容"
fi

echo "=== 容量规划完成 ==="
```

#### 11.3.2 用户增长规划
```bash
# 用户增长分析
#!/bin/bash
# user_growth_analysis.sh

echo "=== 用户增长分析 ==="

# 统计用户注册趋势
docker-compose -f docker-compose.prod.yml exec mysql mysql -<NAME_EMAIL> gedi -e "
SELECT
    DATE(registration_time) as date,
    COUNT(*) as new_users
FROM users
WHERE registration_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(registration_time)
ORDER BY date;"

# 活跃用户统计
docker-compose -f docker-compose.prod.yml exec mysql mysql -<NAME_EMAIL> gedi -e "
SELECT
    COUNT(DISTINCT user_id) as active_users
FROM users
WHERE last_login_time >= DATE_SUB(NOW(), INTERVAL 7 DAY);"

echo "=== 分析完成 ==="
```

## 12. 维护计划

### 12.1 日常维护任务

#### 12.1.1 每日维护清单
```bash
#!/bin/bash
# daily_maintenance.sh

echo "=== 每日维护任务 $(date) ==="

# 1. 检查系统状态
echo "1. 检查系统状态..."
systemctl status docker
docker-compose -f docker-compose.prod.yml ps

# 2. 检查磁盘空间
echo "2. 检查磁盘空间..."
df -h

# 3. 检查日志文件大小
echo "3. 检查日志文件..."
ls -lh logs/

# 4. 清理临时文件
echo "4. 清理临时文件..."
find uploads/ -name "*.tmp" -mtime +1 -delete
find /tmp -name "office_*" -mtime +1 -delete

# 5. 检查备份状态
echo "5. 检查备份状态..."
ls -la /backup/$(date +%Y%m%d)/ 2>/dev/null || echo "今日备份未完成"

# 6. 性能检查
echo "6. 性能检查..."
./performance_monitor.sh

# 7. 安全检查
echo "7. 安全检查..."
# 检查失败登录
grep "密码错误" logs/app.log | tail -5

echo "=== 每日维护完成 ==="
```

#### 12.1.2 每周维护清单
```bash
#!/bin/bash
# weekly_maintenance.sh

echo "=== 每周维护任务 $(date) ==="

# 1. 数据库优化
echo "1. 数据库优化..."
docker-compose -f docker-compose.prod.yml exec mysql mysql -<NAME_EMAIL> gedi -e "OPTIMIZE TABLE users;"

# 2. 日志轮转
echo "2. 日志轮转..."
find logs/ -name "*.log" -size +100M -exec logrotate {} \;

# 3. 系统更新检查
echo "3. 检查系统更新..."
apt list --upgradable 2>/dev/null | head -10

# 4. Docker镜像清理
echo "4. Docker镜像清理..."
docker image prune -f

# 5. 备份验证
echo "5. 备份验证..."
LATEST_BACKUP=$(ls -t /backup/*/database_*.sql.gz | head -1)
if [ -n "$LATEST_BACKUP" ]; then
    gunzip -t "$LATEST_BACKUP" && echo "备份文件完整" || echo "备份文件损坏"
fi

# 6. 安全扫描
echo "6. 安全扫描..."
# 检查弱密码用户
docker-compose -f docker-compose.prod.yml exec mysql mysql -<NAME_EMAIL> gedi -e "
SELECT username, registration_time
FROM users
WHERE password LIKE '%default%' OR password LIKE '%123456%';"

echo "=== 每周维护完成 ==="
```

#### 12.1.3 每月维护清单
```bash
#!/bin/bash
# monthly_maintenance.sh

echo "=== 每月维护任务 $(date) ==="

# 1. 完整系统备份
echo "1. 执行完整系统备份..."
./full_backup.sh

# 2. 性能报告生成
echo "2. 生成性能报告..."
cat > /tmp/monthly_performance_report.txt << EOF
月度性能报告 - $(date +%Y年%m月)

系统运行时间: $(uptime)
磁盘使用情况: $(df -h /)
内存使用情况: $(free -h)
用户统计: $(docker-compose -f docker-compose.prod.yml exec mysql mysql -<NAME_EMAIL> gedi -e "SELECT COUNT(*) FROM users;" | tail -1) 个用户
EOF

# 3. 安全审计
echo "3. 安全审计..."
# 检查用户权限
docker-compose -f docker-compose.prod.yml exec mysql mysql -<NAME_EMAIL> gedi -e "
SELECT username, is_admin, last_login_time
FROM users
WHERE is_admin = 1;"

# 4. 依赖更新检查
echo "4. 检查依赖更新..."
pip list --outdated

# 5. 清理旧文件
echo "5. 清理旧文件..."
find uploads/ -type f -mtime +90 -name "*.tmp" -delete
find logs/ -name "*.log.*" -mtime +30 -delete

# 6. 容量规划
echo "6. 容量规划..."
./storage_planning.sh

echo "=== 每月维护完成 ==="
```

### 12.2 维护自动化

#### 12.2.1 Crontab配置
```bash
# 编辑crontab
crontab -e

# 添加定时任务
# 每日凌晨2点执行备份
0 2 * * * /opt/office_plugins/scripts/full_backup.sh

# 每日上午9点执行日常维护
0 9 * * * /opt/office_plugins/scripts/daily_maintenance.sh

# 每周日凌晨3点执行周维护
0 3 * * 0 /opt/office_plugins/scripts/weekly_maintenance.sh

# 每月1号凌晨4点执行月维护
0 4 1 * * /opt/office_plugins/scripts/monthly_maintenance.sh

# 每5分钟检查系统健康状态
*/5 * * * * /opt/office_plugins/scripts/health_check.sh

# 每小时清理临时文件
0 * * * * find /tmp -name "office_*" -mtime +1 -delete
```

#### 12.2.2 监控告警自动化
```bash
#!/bin/bash
# monitoring_alerts.sh

# 检查系统关键指标并发送告警

# CPU使用率检查
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//' | cut -d'%' -f1)
if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
    echo "CPU使用率过高: ${CPU_USAGE}%" | mail -s "CPU告警" <EMAIL>
fi

# 内存使用率检查
MEM_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
if (( $(echo "$MEM_USAGE > 85" | bc -l) )); then
    echo "内存使用率过高: ${MEM_USAGE}%" | mail -s "内存告警" <EMAIL>
fi

# 磁盘使用率检查
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 90 ]; then
    echo "磁盘使用率过高: ${DISK_USAGE}%" | mail -s "磁盘告警" <EMAIL>
fi

# 应用健康检查
if ! curl -f http://localhost:12010/ > /dev/null 2>&1; then
    echo "应用无响应" | mail -s "应用告警" <EMAIL>
fi

# 数据库健康检查
if ! docker-compose -f docker-compose.prod.yml exec mysql mysqladmin ping > /dev/null 2>&1; then
    echo "数据库无响应" | mail -s "数据库告警" <EMAIL>
fi
```

### 12.3 维护文档更新

#### 12.3.1 变更记录模板
```markdown
# 系统变更记录

## 变更信息
- **变更日期**: 2025-07-18
- **变更人员**: 张三
- **变更类型**: 配置更新/功能升级/安全补丁
- **影响范围**: 全系统/特定模块

## 变更内容
### 变更前状态
- 描述变更前的系统状态

### 变更详情
- 具体的变更内容
- 修改的文件和配置

### 变更后状态
- 描述变更后的系统状态

## 测试验证
- [ ] 功能测试通过
- [ ] 性能测试通过
- [ ] 安全测试通过
- [ ] 备份恢复测试通过

## 回滚计划
- 回滚步骤
- 回滚验证方法

## 风险评估
- 潜在风险
- 缓解措施
```

#### 12.3.2 维护日志
```bash
#!/bin/bash
# maintenance_log.sh

LOG_FILE="/var/log/maintenance.log"

log_maintenance() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> $LOG_FILE
}

# 使用示例
log_maintenance "开始每日维护任务"
log_maintenance "数据库备份完成"
log_maintenance "系统清理完成"
log_maintenance "每日维护任务结束"
```

---

## 总结

本运维手册涵盖了"有解"数字助手系统的完整运维流程，包括：

1. **系统架构和配置**: 详细的技术栈和环境配置
2. **部署和更新**: 开发和生产环境的部署流程
3. **监控和日志**: 完善的监控体系和日志管理
4. **安全管理**: 认证授权和数据安全保护
5. **备份恢复**: 多层次的备份策略和恢复程序
6. **故障排查**: 常见问题的诊断和解决方案
7. **应急恢复**: 完整的灾难恢复预案
8. **性能优化**: 系统性能监控和优化策略
9. **维护计划**: 自动化的日常维护流程

请根据实际环境调整相关配置和脚本，并定期更新本手册以保持其准确性和实用性。

**重要提醒**:
- 所有脚本在生产环境使用前请先在测试环境验证
- 定期检查和更新备份策略的有效性
- 保持系统和依赖的及时更新
- 建立完善的监控告警机制
- 定期进行灾难恢复演练
