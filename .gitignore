# ==========================
# 项目特定文件
# ==========================
# 忽略.tar/.tar.xz/.tar.gz 文件
*.tar
*.tar.xz
*.tar.gz


# 忽略 uploads 目录中的所有文件，但保留目录结构
uploads/*
!uploads/.gitkeep

# 忽略调试和日志文件
*.log
*.out
nohup.out

# Static 文件夹特定规则
# 忽略 static 文件夹下的所有内容
/static/*
# 但是，不忽略 static/admin 文件夹 (重新包含它)
!/static/admin/
# 并且，不忽略 static/downloads 文件夹 (重新包含它)
!/static/downloads/

# ==========================
# 系统和工具文件
# ==========================

# Python 字节码文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/

# IDEs 和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
Thumbs.db

# 数据库
*.sqlite3

# ==========================
# Python 包和测试
# ==========================

# 分发/打包
.Python
build/
develop-eggs/
dist/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg

# 测试
.pytest_cache/
.coverage
htmlcov/

# Jupyter 笔记本
.ipynb_checkpoints

# pyenv
.python-version

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath
*.sage.py

# 环境文件
.env
.venv

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre
.pyre/

# ==========================
# 其他工具
# ==========================

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs
/site
