# "有解"数字助手后端说明

> 本项目基于FastAPI框架开发，支持本地部署和Docker部署。

## 目录

- [1. 项目概述](#1-项目概述)
- [2. 本地部署](#2-本地部署仅在测试环境使用)
- [3. Docker部署](#3-docker部署)
- [4. Docker更新流程](#4-docker更新流程)
- [5. 常见问题排查](#5-常见问题排查)
- [6. API文档](#6-api文档)
- [7. 开发指南](#7-开发指南)
- [8. Internal Resources Solution](#8-internal-resources-solution)

## 1. 项目概述

"有解"数字助手是一款基于大语言模型的智能办公助手，旨在提高办公效率和文档处理能力。

### 1.1 技术栈

- **后端框架**：FastAPI

- **数据库**：MySQL

- **AI模型**：基于OpenAI接口

- **容器化**：Docker & Docker Compose

  

### 1.2 系统要求

- Python 3.11+
- MySQL 8.0+
- Docker & Docker Compose (用于容器化部署)
- 至少2GB内存
- 至少10GB磁盘空间

## 2. 本地部署（仅在测试环境使用）

### 2.1 安装依赖

- 创建名为`gedi`的conda虚拟环境，Python版本为`3.11.9`，然后切换到`gedi`虚拟环境
```bash
conda create -n gedi python=3.11.9
conda activate gedi
```

- 安装依赖
```bash
pip install -r requirements.txt
```


### 1.2 启动项目
- 切换到`gedi`虚拟环境
```bash
conda activate gedi
```
- 启动项目，默认端口为`12010`（可在`run.py`中修改）
```bash
./start_dev.sh
```

- 关闭项目
```bash
./stop.sh
```

## 2. Docker部署

### 2.1 项目文件说明
在打包docker镜像前，确保项目根目录包含以下文件：
- `Dockerfile`：应用程序的构建配置
- `mysql.Dockerfile`：MySQL数据库的构建配置
- `docker-compose.dev.yml`：开发环境的服务编排配置
- `docker-compose.prod.yml`：生产环境的服务编排配置
- `init.sql`：MySQL初始化脚本
- `start.sh`：容器启动脚本

### 2.2 开发环境部署
在有网络的开发环境中，执行以下命令：
```bash
# 构建并启动服务
docker-compose -f docker-compose.dev.yml up -d

# 查看服务状态
docker-compose -f docker-compose.dev.yml ps

# 查看日志
docker-compose -f docker-compose.dev.yml logs -f
```

### 2.3 生产环境部署
#### 2.3.1 在开发环境准备镜像
```bash
# 构建开发环境镜像
docker-compose -f docker-compose.dev.yml build

# 修改标签为生产环境版本
docker tag office_plugin:dev office_plugin:latest
docker tag office_plugin_mysql:dev office_plugin_mysql:latest

# 导出镜像为tar文件
docker save -o office_plugin.tar office_plugin:latest
docker save -o office_plugin_mysql.tar office_plugin_mysql:latest

# 如果镜像太大，建议导出镜像为tar.xz文件
docker save office_plugin:latest | xz -z -T0 -9 -v > office_plugin.tar.xz
docker save office_plugin_mysql:latest | xz -z -T0 -9 -v > office_plugin_mysql.tar.xz.
```

#### 2.3.2 文件传输
将以下文件传输到生产环境：
- `office_plugin.tar`
- `office_plugin_mysql.tar`
- `docker-compose.prod.yml`
- `init.sql`（数据库初始化脚本）

#### 2.3.3 在生产环境部署
```bash
# 解压缩（可选）
xz -d office_plugin.combined.tar.xz
xz -d office_plugin_mysql.combined.tar.xz
# 加载镜像
docker load -i office_plugin.tar
docker load -i office_plugin_mysql.tar

# 启动服务
docker-compose -f docker-compose.prod.yml up -d
```

#### 2.4 常用维护命令
```bash
# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看服务日志
docker-compose -f docker-compose.prod.yml logs -f

# 重启服务
docker-compose -f docker-compose.prod.yml restart

# 停止服务
docker-compose -f docker-compose.prod.yml down

# 停止服务并删除数据卷（谨慎使用）
docker-compose -f docker-compose.prod.yml down -v
```


## 3. Docker更新流程

### 3.1 开发环境准备
1. 更新代码并测试
```bash
# 确保代码更新并在开发环境测试通过
git pull
# 或其他代码更新方式
```

2. 构建新版本镜像
```bash
# 构建新版本应用镜像
docker-compose -f docker-compose.dev.yml build app

# 标记新版本镜像
docker tag office_plugin:dev office_plugin:latest

# 导出新的应用镜像
docker save -o office_plugin.tar office_plugin:latest
```

### 3.2 生产环境更新

#### 3.2.1 备份数据（强烈建议）
```bash
# 1. 创建备份目录
mkdir -p backups/$(date +%Y%m%d)

# 2. 进入MySQL容器执行备份
docker exec -it $(docker-compose -f docker-compose.prod.yml ps -q mysql) \
    mysqldump -<NAME_EMAIL> gedi > backups/$(date +%Y%m%d)/gedi_backup.sql

# 3. 备份当前的docker-compose配置
cp docker-compose.prod.yml backups/$(date +%Y%m%d)/
```

#### 3.2.2 更新应用
1. 传输文件到生产环境：
```bash
# 将新的镜像文件传输到生产服务器
scp office_plugin.tar user@server:/path/to/deployment/
```

2. 在生产环境执行更新：
```bash
# 1. 加载新镜像
docker load -i office_plugin.tar

# 2. 更新应用服务（不影响MySQL）
docker-compose -f docker-compose.prod.yml up -d --no-deps app

# 3. 检查更新后的服务状态
docker-compose -f docker-compose.prod.yml ps
```

#### 6.2.3 验证更新
```bash
# 1. 检查应用日志
docker-compose -f docker-compose.prod.yml logs -f app

# 2. 检查应用是否正常响应
curl http://localhost:12010/

# 3. 检查MySQL连接是否正常
docker-compose -f docker-compose.prod.yml exec app python -c "from app.core.config import settings; print(f'数据库连接信息: {settings.DB_HOST}:{settings.DB_PORT}')"
```

### 3.3 回滚方案
如果更新后出现问题，可以按以下步骤回滚：

```bash
# 1. 停止有问题的应用容器
docker-compose -f docker-compose.prod.yml stop app

# 2. 使用旧版本镜像重新部署
docker tag office_plugin:previous office_plugin:latest
docker-compose -f docker-compose.prod.yml up -d --no-deps app

# 3. 如果需要恢复数据库
docker-compose -f docker-compose.prod.yml down
cat backups/[日期]/gedi_backup.sql | docker exec -i $(docker-compose -f docker-compose.prod.yml ps -q mysql) mysql -<NAME_EMAIL> gedi
```

### 3.4 注意事项

1. **数据安全**：
   - MySQL数据存储在 `mysql_data` 数据卷中，更新应用不会影响数据
   - 建议每次更新前都进行数据备份
   - 不要使用 `docker-compose down -v`，这会删除数据卷

2. **更新策略**：
   - 使用 `--no-deps` 参数可以只更新应用容器，不影响MySQL
   - 如果有数据库结构变更，需要单独执行迁移脚本
   - 建议在更新前先在测试环境验证新版本

3. **环境变量**：
   - 确保新版本的环境变量配置正确
   - 检查 `docker-compose.prod.yml` 中的配置是否需要更新
   - 特别注意数据库连接、API密钥等敏感配置

4. **日志监控**：
   - 更新后密切监控应用日志
   - 检查应用启动过程中的警告或错误
   - 验证关键功能是否正常工作

5. **版本管理**：
   - 保留旧版本镜像，以便需要时回滚
   - 记录每次更新的版本号和更新内容
   - 建议使用版本标签管理镜像

6. **性能监控**：
   - 监控更新后的系统资源使用情况
   - 检查数据库连接性能
   - 观察API响应时间是否正常

