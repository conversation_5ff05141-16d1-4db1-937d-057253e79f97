# 统一日志系统使用指南

## 概述

本项目已实现统一的日志系统，提供结构化的日志记录、多种输出格式和灵活的配置选项。

## 主要特性

### 1. 统一的日志接口
- 所有模块使用相同的日志记录器
- 支持不同日志级别（DEBUG、INFO、WARNING、ERROR、CRITICAL）
- 自动记录调用位置信息（文件、函数、行号）

### 2. 多种输出格式
- **开发环境**：彩色控制台输出，便于调试
- **生产环境**：JSON格式输出，便于日志分析
- **文件输出**：结构化JSON格式，支持日志轮转

### 3. 分类日志文件
- `app.log`：所有级别的应用日志
- `error.log`：仅记录ERROR和CRITICAL级别的日志
- `access.log`：专门记录HTTP请求访问日志（需要手动调用）

### 4. 自动API访问日志
系统会自动记录所有HTTP请求，格式简洁：
- **格式**：`[时间] API 方法 路径 状态码`
- **颜色区分**：
  - 2xx（成功）：绿色
  - 3xx（重定向）：蓝色
  - 4xx（客户端错误）：黄色
  - 5xx（服务器错误）：红色

### 4. 结构化日志记录
支持添加额外的上下文信息：
- 用户ID
- 请求ID
- 客户端IP
- 处理时间
- 业务相关字段

## 使用方法

### 基本使用

```python
from app.core.logging_config import get_logger

# 获取日志记录器
logger = get_logger(__name__)

# 记录不同级别的日志
logger.debug("调试信息")
logger.info("一般信息")
logger.warning("警告信息")
logger.error("错误信息")
logger.critical("严重错误")

# 记录异常信息
try:
    # 一些可能出错的代码
    pass
except Exception as e:
    logger.error("操作失败", exc_info=True)
```

### 带上下文信息的日志

```python
# 记录带额外字段的日志
logger.info("用户登录成功", extra={
    'user_id': 'user123',
    'client_ip': '*************',
    'login_method': 'password'
})
```

### 专用日志记录函数

#### HTTP请求日志（可选）
如果需要记录特定的HTTP请求，可以手动调用：
```python
from app.core.logging_config import log_request

log_request(
    method="GET",
    url="/api/users",
    status_code=200,
    duration=150.5,  # 毫秒
    client_ip="*************",
    user_id="user123",
    request_id="req-456"
)
```

**注意**：为了减少日志冗余，项目已移除自动记录所有HTTP请求的中间件。

#### 数据库操作日志
```python
from app.core.logging_config import log_database_operation

log_database_operation(
    operation="SELECT",
    table="users",
    duration=25.3,  # 毫秒
    user_id="user123",
    success=True
)
```

#### LLM调用日志
```python
from app.core.logging_config import log_llm_call

log_llm_call(
    model="gpt-3.5-turbo",
    prompt_length=100,
    response_length=200,
    duration=1500.0,  # 毫秒
    success=True
)
```

## 配置

### 环境变量

在环境变量或配置文件中设置：

```bash
# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# 日志文件目录
LOG_DIR=logs

# 应用模式 (dev, test, prod)
APP_MODE=dev
```

### 初始化

在应用启动时初始化日志系统：

```python
from app.core.logging_config import setup_logging

# 初始化日志系统
setup_logging(app_mode="dev", log_dir="logs")
```

## 日志格式

### 控制台输出（开发环境）
```
[15:29:29] INFO 用户登录成功
[15:29:29] ERROR auth:login - 登录失败
[15:29:29] API POST /api/login 200
[15:29:29] API GET /api/users 404
```

### JSON格式（文件和生产环境）
```json
{
  "time": "2025-07-02 15:29:29",
  "level": "INFO",
  "message": "用户登录成功",
  "user_id": "user123",
  "client_ip": "*************"
}
```

## 最佳实践

### 1. 日志级别使用指南
- **DEBUG**：详细的调试信息，默认不显示（需要手动调整日志级别）
- **INFO**：一般的业务流程信息，默认级别
- **WARNING**：警告信息，不影响正常运行但需要注意
- **ERROR**：错误信息，影响功能但不会导致程序崩溃
- **CRITICAL**：严重错误，可能导致程序无法继续运行

### 2. 日志消息编写
- 使用清晰、简洁的消息描述
- 包含足够的上下文信息
- 避免记录敏感信息（密码、密钥等）
- 使用结构化的额外字段而不是在消息中拼接

### 3. 性能考虑
- DEBUG级别日志默认不显示，减少性能开销
- 日志格式已优化，减少不必要的字段
- INFO级别日志简化显示，只在ERROR/WARNING时显示详细位置信息
- 移除了冗余的HTTP请求日志中间件，避免重复记录
- 使用延迟计算的日志消息格式
- 合理设置日志轮转策略

### 4. 错误处理
```python
try:
    # 业务逻辑
    result = some_operation()
    logger.info("操作成功", extra={'result_count': len(result)})
except SpecificException as e:
    logger.error("特定错误发生", extra={'error_type': type(e).__name__})
    # 处理特定错误
except Exception as e:
    logger.error("未预期的错误", exc_info=True)
    # 处理通用错误
```

## 监控和分析

### 日志文件位置
- 开发环境：`logs/` 目录
- Docker环境：容器内 `/app/logs/` 目录

### 日志轮转
- 单个日志文件最大10MB
- 保留5个历史文件（应用日志）
- 保留10个历史文件（错误日志）

### 日志分析建议
1. 使用ELK Stack或类似工具进行日志聚合和分析
2. 设置关键错误的告警机制
3. 定期分析访问日志以了解系统使用情况
4. 监控LLM调用的性能和成功率

## 迁移指南

### 从print语句迁移
```python
# 旧方式
print(f"用户 {username} 登录成功")

# 新方式
logger.info("用户登录成功", extra={'username': username})
```

### 从原有logging迁移
```python
# 旧方式
import logging
logging.info("消息")

# 新方式
from app.core.logging_config import get_logger
logger = get_logger(__name__)
logger.info("消息")
```

## 故障排除

### 常见问题

1. **日志文件未生成**
   - 检查LOG_DIR目录是否存在且有写权限
   - 确认日志系统已正确初始化

2. **日志级别不正确**
   - 检查LOG_LEVEL环境变量设置
   - 确认应用模式（APP_MODE）配置

3. **性能问题**
   - 检查是否有过多的DEBUG级别日志
   - 考虑调整日志轮转策略

### 测试日志系统
运行测试脚本验证日志系统是否正常工作：
```bash
python test_logging.py
```
