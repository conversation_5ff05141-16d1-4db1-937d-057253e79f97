/* Admin CSS */
body {
  font-size: .875rem;
  background-color: #f8f9fa;
}

.sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  padding: 48px 0 0;
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar-sticky {
  position: relative;
  top: 0;
  height: calc(100vh - 48px);
  padding-top: .5rem;
  overflow-x: hidden;
  overflow-y: auto;
}

.sidebar .nav-link {
  font-weight: 500;
  color: #333;
  padding: 0.5rem 1rem;
  margin-bottom: 0.2rem;
  border-radius: 0.25rem;
}

.sidebar .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
  background-color: rgba(255, 255, 255, 0.2);
}

.sidebar .nav-link .feather {
  margin-right: 4px;
  color: #999;
}

.sidebar .nav-link.active .feather {
  color: inherit;
}

/* Main content */
main {
  padding-top: 1.5rem;
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  margin-bottom: 1.5rem;
}

.card-header {
  background-color: rgba(0, 0, 0, 0.03);
  font-weight: 500;
}

/* Tables */
.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Forms */
.form-label {
  font-weight: 500;
}

/* Custom utilities */
.cursor-pointer {
  cursor: pointer;
}

/* Project card styling */
.project-item {
  transition: all 0.2s ease;
}

.project-item:hover {
  background-color: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
}

/* Button styles */
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.76563rem;
}

/* Custom colors for dashboard cards */
.dashboard-card {
  border: none;
  border-radius: 0.5rem;
  transition: transform 0.2s ease;
}

.dashboard-card:hover {
  transform: translateY(-5px);
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
  .sidebar {
    position: static;
    height: auto;
    padding-top: 0;
  }
  
  main {
    margin-top: 1rem;
  }
}

/* 管理后台自定义样式 */

/* 头像样式 */
.user-panel .image img {
    width: 2.1rem;
    height: 2.1rem;
}

/* 主题色调整 */
.main-sidebar {
    background-color: #343a40;
}

.sidebar-dark-primary .nav-sidebar > .nav-item > .nav-link.active {
    background-color: #4e73df;
}

/* 卡片标题美化 */
.card-title {
    font-weight: 600;
}

/* 表格行间隔高度 */
.table td, .table th {
    padding: 0.75rem;
    vertical-align: middle;
}

/* 操作按钮组间距 */
.btn-group .btn {
    margin-right: 2px;
}

/* Dashboard 统计卡片图标 */
.small-box .icon {
    color: rgba(255, 255, 255, 0.15);
}

/* 表单元素样式优化 */
.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* 模态框美化 */
.modal-header {
    background-color: #4e73df;
    color: white;
    border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
}

.modal-header .close {
    color: white;
    opacity: 0.8;
}

.modal-header .close:hover {
    opacity: 1;
}

/* 页面加载动画 */
.page-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.7);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4e73df;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 移动端适应 */
@media (max-width: 767.98px) {
    .table-responsive {
        overflow-x: auto;
    }
    
    .btn-sm {
        padding: 0.25rem 0.4rem;
    }
} 