// 后备Bootstrap，最小化版本
if (typeof jQuery !== 'undefined') {
    (function($) {
        // 提供基本的模态框功能
        $.fn.modal = function(options) {
            if (options === 'show') {
                this.css('display', 'block');
                this.addClass('show');
                $('body').addClass('modal-open');
                
                // 创建背景遮罩
                if ($('.modal-backdrop').length === 0) {
                    $('<div class="modal-backdrop fade show"></div>').appendTo('body');
                }
            } else if (options === 'hide') {
                this.css('display', 'none');
                this.removeClass('show');
                $('body').removeClass('modal-open');
                $('.modal-backdrop').remove();
            }
            return this;
        };
        
        // 提供基本的工具提示功能
        $.fn.tooltip = function() {
            // 简单实现，实际不做任何事情
            return this;
        };
        
        // 提供基本的alert功能
        $.fn.alert = function(action) {
            if (action === 'close') {
                this.remove();
            }
            return this;
        };
        
        console.log('后备Bootstrap组件加载完成');
    })(jQuery);
} 