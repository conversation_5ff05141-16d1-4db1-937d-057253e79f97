/**
 * Admin JavaScript
 */
document.addEventListener('DOMContentLoaded', function() {
  // 检查jQuery是否已加载
  if (typeof jQuery === 'undefined') {
    console.error('jQuery 未加载!');
    return;
  }
  
  // Bootstrap 4兼容性检查
  if (typeof $.fn.modal === 'undefined') {
    console.error('Bootstrap modal 功能不可用!');
    return;
  }
  
  // 使用jQuery API初始化工具提示
  $(function() {
    $('[data-toggle="tooltip"]').tooltip();
  });

  // 处理模态框
  $('.modal').on('hidden.bs.modal', function() {
    // 重置模态框中的表单
    $(this).find('form').each(function() {
      this.reset();
    });
  });

  // 启用警告自动关闭
  $('.alert').each(function() {
    if (!$(this).hasClass('alert-persistent')) {
      var alert = $(this);
      setTimeout(function() {
        alert.alert('close');
      }, 5000);
    }
  });

  // 格式化文件大小
  function formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }

  // 格式化日期
  function formatDate(timestamp) {
    const date = new Date(parseInt(timestamp) * 1000);
    return date.toLocaleString('zh-CN');
  }

  // 显示管理员信息
  const adminName = localStorage.getItem('admin_full_name') || localStorage.getItem('admin_username') || '管理员';
  const adminNameElem = document.getElementById('admin-name');
  if (adminNameElem) {
    adminNameElem.textContent = adminName;
  }
  
  // 检查令牌是否存在
  const adminToken = localStorage.getItem('admin_token');
  if (!adminToken && window.location.pathname !== '/admin/login') {
    // 如果没有令牌且不在登录页面，重定向到登录页面
    window.location.href = '/admin/login';
  }
  
  // 退出登录处理
  const logoutBtn = document.getElementById('logout-btn');
  if (logoutBtn) {
    logoutBtn.addEventListener('click', function(e) {
      e.preventDefault();
      // 清除存储的令牌和用户信息
      localStorage.removeItem('admin_token');
      localStorage.removeItem('admin_username');
      localStorage.removeItem('admin_full_name');
      
      // 跳转到登录页面
      window.location.href = '/admin/login';
    });
  }
  
  // 激活侧边栏当前项
  const currentPath = window.location.pathname;
  const sidebarLinks = document.querySelectorAll('.nav-sidebar .nav-link');
  
  sidebarLinks.forEach(link => {
    if (link.getAttribute('href') === currentPath) {
      link.classList.add('active');
    }
  });
  
  // 添加AJAX请求拦截器，自动添加认证令牌
  const originalFetch = window.fetch;
  window.fetch = function(url, options = {}) {
    // 仅对管理后台API请求添加令牌
    if (url.includes('/admin_api/') && adminToken) {
      options.headers = options.headers || {};
      options.headers['Authorization'] = `Bearer ${adminToken}`;
    }
    return originalFetch(url, options);
  };

  // 注意: 项目管理页面中的按钮事件处理已移至projects.html中实现，避免代码冲突
});

// 格式化日期时间
function formatDateTime(timestamp) {
  if (!timestamp) return '-';
  const date = new Date(timestamp * 1000);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 显示通知消息
function showNotification(message, type = 'info') {
  // 如果toastr可用，使用toastr显示通知
  if (typeof toastr !== 'undefined') {
    toastr[type](message);
    return;
  }
  
  // 否则使用原生方式显示通知
  // 创建通知元素
  const notification = document.createElement('div');
  notification.className = `alert alert-${type} alert-dismissible fade show notification`;
  notification.innerHTML = `
    ${message}
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  `;
  
  // 添加样式
  notification.style.position = 'fixed';
  notification.style.top = '20px';
  notification.style.right = '20px';
  notification.style.zIndex = '9999';
  notification.style.minWidth = '300px';
  
  // 添加到页面
  document.body.appendChild(notification);
  
  // 3秒后自动移除
  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => {
      notification.remove();
    }, 150);
  }, 3000);
}

// 显示加载动画
function showLoading() {
  const loading = document.createElement('div');
  loading.className = 'page-loading';
  loading.innerHTML = '<div class="loading-spinner"></div>';
  document.body.appendChild(loading);
}

// 隐藏加载动画
function hideLoading() {
  const loading = document.querySelector('.page-loading');
  if (loading) {
    loading.remove();
  }
}

// 通用的API调用函数
async function callApi(url, method = 'GET', data = null) {
  showLoading();
  
  try {
    const options = {
      method: method,
      headers: {
        'Content-Type': 'application/json',
      },
    };
    
    // 添加身份验证token
    const adminToken = localStorage.getItem('admin_token');
    if (adminToken) {
      options.headers['Authorization'] = `Bearer ${adminToken}`;
    }
    
    if (data) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(url, options);
    const result = await response.json();
    
    hideLoading();
    
    if (response.ok) {
      return result;
    } else {
      if (typeof toastr !== 'undefined') {
        toastr.error(result.message || '请求失败');
      } else {
        alert(result.message || '请求失败');
      }
      throw new Error(result.message || '请求失败');
    }
  } catch (error) {
    hideLoading();
    if (typeof toastr !== 'undefined') {
      toastr.error(error.message || '请求失败');
    } else {
      alert(error.message || '请求失败');
    }
    throw error;
  }
} 