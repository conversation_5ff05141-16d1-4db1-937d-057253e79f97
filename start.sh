#!/bin/bash

# 设置脚本执行失败时立即退出
set -e

echo "正在启动【有解助手】后端服务..."


# 检查必要的环境变量是否已设置
required_vars=(
    "UPLOAD_DIRECTORY"
    "MAX_FILE_SIZE"
    "SECRET_KEY"
    "DB_HOST"
    "DB_PORT"
    "DB_USER"
    "DB_PASSWORD"
    "DB_NAME"
    "GDB_HOST"
    "GDB_USER"
    "GDB_PASSWORD"
    "LLM_CONFIGURATIONS_RAW"
    "STATIC_URL"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "错误：环境变量 $var 未设置！"
        exit 1
    fi
done

# 创建上传目录（如果不存在）
if [ ! -d "$UPLOAD_DIRECTORY" ]; then
    echo "创建上传目录: $UPLOAD_DIRECTORY"
    mkdir -p "$UPLOAD_DIRECTORY"
fi

# 禁用 uvloop（与 Docker 环境保持一致）
export UVICORN_LOOP=asyncio

echo "✅ 环境变量加载完成"

# 在前台启动应用
python run.py
